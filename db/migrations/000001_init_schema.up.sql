-- Install uuid extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE IF NOT EXISTS transaction (
	id BIGSERIAL PRIMARY KEY,
	client_id VARCHAR (24) NOT NULL,
	warehouse_id VARCHAR (24) NOT NULL,
	request_id VARCHAR (100) NOT NULL,
	item_id VARCHAR (24) NOT NULL,
	sku VARCHAR (50) NOT NULL,
	action VARCHAR (50) NOT NULL,
	change INT NOT NULL,
	current INT NOT NULL,
	changed_by_username VARCHAR (50) NOT NULL,
	changed_by_id VARCHAR (24) NOT NULL,
	changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
	batch_number VARCHAR (50),
	serial_number VARCHAR (50),
	expiration_date DATE
);

CREATE TABLE IF NOT EXISTS inventory (
	id BIGSERIAL PRIMARY KEY,
	item_id VARCHAR (24) NOT NULL,
	client_id VARCHAR (24) NOT NULL,
	warehouse_id VARCHAR (24) NOT NULL,
	quantity INT NOT NULL,
	available_qty INT NOT NULL,
	pickable_qty INT NOT NULL,
	base_unit VARCHAR (50) NOT NULL,
	sku VARCHAR (50) NOT NULL,
	scannable VARCHAR (50) NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP,
	is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
	on_hold BOOLEAN NOT NULL DEFAULT FALSE,
	is_batch_controlled BOOLEAN NOT NULL DEFAULT FALSE,
	is_tracked_by_serial_no BOOLEAN NOT NULL DEFAULT FALSE,
	name VARCHAR (200) NOT NULL,
	description VARCHAR (200) NOT NULL,
	on_hold_by VARCHAR (50),
	is_perishable BOOLEAN NOT NULL DEFAULT FALSE,
	tracked_by VARCHAR (50),
	image VARCHAR (100),
	back_orders BOOLEAN NOT NULL DEFAULT FALSE,
	client_name VARCHAR (50),
	is_client_deleted BOOLEAN NOT NULL DEFAULT FALSE,
	is_kit BOOLEAN NOT NULL DEFAULT FALSE,
    UNIQUE (item_id)
);


CREATE TABLE IF NOT EXISTS tracked_inventory (
	id BIGSERIAL PRIMARY KEY,
	warehouse_id VARCHAR (24),
	client_id VARCHAR (24),
	item_id VARCHAR (24),
	batch_number VARCHAR (50),
	serial_number VARCHAR (50),
	expiration_date DATE,
    quantity INT NOT NULL,
	available_qty INT NOT NULL,
	pickable_qty INT NOT NULL,
    UNIQUE (item_id, batch_number, serial_number, expiration_date)
);
-- item_id foreign key
ALTER TABLE tracked_inventory
ADD CONSTRAINT item_fk
FOREIGN KEY (item_id)
REFERENCES inventory (item_id)
ON DELETE SET NULL;


CREATE TABLE IF NOT EXISTS location_wise_inventory (
	id BIGSERIAL PRIMARY KEY,
	item_id VARCHAR (24),
	warehouse_id VARCHAR (24) NOT NULL,
	client_id VARCHAR (24),
	location_id VARCHAR (24) NOT NULL,
	code VARCHAR (50) NOT NULL,
	quantity INT NOT NULL,
	available_qty INT NOT NULL,
	area_name VARCHAR (50) NOT NULL,
	area_id VARCHAR (24) NOT NULL,
	on_hold BOOLEAN NOT NULL DEFAULT FALSE,
	on_hold_by_id VARCHAR (50),
	last_updated_at TIMESTAMP,
	last_updated_by_id VARCHAR (24),
	last_updated_by_name VARCHAR (50),
	sku VARCHAR (50),
	scannable VARCHAR (50),
	batch_number VARCHAR (50),
	serial_number VARCHAR (50),
	expiration_date DATE,
	created_at TIMESTAMP,
	client_name VARCHAR (50),
    UNIQUE (item_id, location_id, batch_number, serial_number, expiration_date)
);
-- item_id foreign key
ALTER TABLE location_wise_inventory
ADD CONSTRAINT item_fk
FOREIGN KEY (item_id)
REFERENCES inventory (item_id)
ON DELETE SET NULL;


CREATE TABLE IF NOT EXISTS container_type (
	id SERIAL PRIMARY KEY,
	warehouse_id VARCHAR (24),
	name VARCHAR (50),
	only_ships_whole BOOLEAN NOT NULL DEFAULT FALSE
);


CREATE TABLE IF NOT EXISTS container_wise_inventory (
	id BIGSERIAL PRIMARY KEY,
	item_id VARCHAR (24),
	container_id uuid NOT NULL,
	container_type_id INT,
	warehouse_id VARCHAR (24) NOT NULL,
	client_id VARCHAR (24),
	location_id VARCHAR (24),
	quantity INT NOT NULL,
	available_qty INT NOT NULL,
    code VARCHAR (50) NOT NULL,
	last_updated_at TIMESTAMP,
	sku VARCHAR (50),
	scannable VARCHAR (50),
	batch_number VARCHAR (50),
	serial_number VARCHAR (50),
	expiration_date DATE,
	created_at TIMESTAMP,
	is_shipped BOOLEAN NOT NULL DEFAULT FALSE,
	client_name VARCHAR (50),
	on_hold BOOLEAN NOT NULL DEFAULT FALSE,
	on_hold_by VARCHAR (50),
	first_used_at TIMESTAMP,
	ref_no VARCHAR (100),
    UNIQUE (item_id, container_id, location_id, batch_number, serial_number, expiration_date)
);
-- container_type_id foreign key with container_type
ALTER TABLE container_wise_inventory
ADD CONSTRAINT container_type_fk
FOREIGN KEY (container_type_id)
REFERENCES container_type (id)
ON DELETE SET NULL;

-- item_id and location_id foreign key with location_wise_inventory
ALTER TABLE container_wise_inventory
ADD CONSTRAINT item_location_batch_serial_fk
FOREIGN KEY (item_id, location_id, batch_number, serial_number, expiration_date)
REFERENCES location_wise_inventory (item_id, location_id, batch_number, serial_number, expiration_date)
ON DELETE SET NULL;

-- Cart table
CREATE TABLE IF NOT EXISTS cart (
	id BIGSERIAL PRIMARY KEY,
	warehouse_id VARCHAR (24) NOT NULL,
	cart_id uuid NOT NULL,
    code VARCHAR (100) NOT NULL,
    qr VARCHAR (100) NOT NULL,
    alias VARCHAR (100),
	retain_tote BOOLEAN NOT NULL DEFAULT FALSE,
	length FLOAT,
	width FLOAT,
	height FLOAT,
	dims_unit VARCHAR (20),
	weight_capacity FLOAT,
	weight_unit VARCHAR (20),
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
	created_by_id VARCHAR (24) NOT NULL,
	created_by_name VARCHAR (50) NOT NULL,
	last_updated_at TIMESTAMP WITH TIME ZONE,
    UNIQUE (cart_id)
);

CREATE TABLE IF NOT EXISTS tote_type (
	id BIGSERIAL PRIMARY KEY,
	warehouse_id VARCHAR (24) NOT NULL,
	name VARCHAR (100) NOT NULL,
	length FLOAT NOT NULL CHECK (length > 0),
	width FLOAT NOT NULL CHECK (width > 0),
	height FLOAT NOT NULL CHECK (height > 0),
	dims_unit VARCHAR (20) NOT NULL,
	weight_capacity FLOAT NOT NULL CHECK (weight_capacity > 0),
	weight_unit VARCHAR (20) NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
	created_by_id VARCHAR (24) NOT NULL,
	created_by_name VARCHAR (50) NOT NULL,
    UNIQUE (name)
);

CREATE TABLE IF NOT EXISTS tote (
	id BIGSERIAL PRIMARY KEY,
	warehouse_id VARCHAR (24) NOT NULL,
	tote_id uuid NOT NULL,
	tote_type_id BIGINT,
    code VARCHAR (100) NOT NULL,
    qr VARCHAR (100) NOT NULL,
    alias VARCHAR (100),
    ref_no VARCHAR (100),
	retain_tote BOOLEAN NOT NULL DEFAULT FALSE,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
	created_by_id VARCHAR (24) NOT NULL,
	created_by_name VARCHAR (50) NOT NULL,
	last_updated_at TIMESTAMP WITH TIME ZONE,
    UNIQUE (tote_id)
);

-- tote_type_id foreign key with tote_type
ALTER TABLE tote
ADD CONSTRAINT tote_type_fk
FOREIGN KEY (tote_type_id)
REFERENCES tote_type (id)
ON DELETE SET NULL;

-- cart_totes represents association between carts and totes
CREATE TABLE IF NOT EXISTS cart_totes (
	id BIGSERIAL PRIMARY KEY,
	cart_id uuid NOT NULL,
	tote_id uuid NOT NULL
);

-- cart_id foreign key with cart
ALTER TABLE cart_totes
ADD CONSTRAINT cart_fk
FOREIGN KEY (cart_id)
REFERENCES cart (cart_id)
ON DELETE RESTRICT;

-- tote_id foreign key with tote
ALTER TABLE cart_totes
ADD CONSTRAINT tote_fk
FOREIGN KEY (tote_id)
REFERENCES tote (tote_id)
ON DELETE RESTRICT;

CREATE TABLE IF NOT EXISTS cart_items (
	id BIGSERIAL PRIMARY KEY,
	warehouse_id VARCHAR (24) NOT NULL,
	cart_id uuid NOT NULL,
	cart_code VARCHAR (100) NOT NULL,
	client_id VARCHAR (24) NOT NULL,
	client_name VARCHAR (24) NOT NULL,
	operation_type VARCHAR (24) NOT NULL,
	item_id VARCHAR (24) NOT NULL,
	name VARCHAR (100) NOT NULL,
	sku VARCHAR (100) NOT NULL,
	scannable VARCHAR (100) NOT NULL,
	batch_number VARCHAR (100),
	serial_number VARCHAR (100),
	expiration_date DATE,
	quantity INT NOT NULL,
	picklist_id VARCHAR (24) NOT NULL,
	picklist_code VARCHAR (100) NOT NULL,
	order_id VARCHAR (24) NOT NULL,
	order_code VARCHAR (100) NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
	created_by_id VARCHAR (24) NOT NULL,
	created_by_name VARCHAR (50) NOT NULL,
	last_updated_at TIMESTAMP
);

-- cart_id foreign key with cart
ALTER TABLE cart_items
ADD CONSTRAINT cart_items_fk
FOREIGN KEY (cart_id)
REFERENCES cart (cart_id)
ON DELETE RESTRICT;

-- item_id foreign key with inventory
ALTER TABLE cart_items
ADD CONSTRAINT cart_items_inventory_fk
FOREIGN KEY (item_id)
REFERENCES inventory (item_id)
ON DELETE RESTRICT;

CREATE TABLE IF NOT EXISTS tote_items (
	id BIGSERIAL PRIMARY KEY,
	warehouse_id VARCHAR (24) NOT NULL,
	tote_id uuid NOT NULL,
	tote_code VARCHAR (100) NOT NULL,
	client_id VARCHAR (24) NOT NULL,
	client_name VARCHAR (24) NOT NULL,
	operation_type VARCHAR (24) NOT NULL,
	item_id VARCHAR (24) NOT NULL,
	name VARCHAR (100) NOT NULL,
	sku VARCHAR (100) NOT NULL,
	scannable VARCHAR (100) NOT NULL,
	batch_number VARCHAR (100),
	serial_number VARCHAR (100),
	expiration_date DATE,
	quantity INT NOT NULL,
	picklist_id VARCHAR (24) NOT NULL,
	picklist_code VARCHAR (100) NOT NULL,
	order_id VARCHAR (24) NOT NULL,
	order_code VARCHAR (100) NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
	created_by_id VARCHAR (24) NOT NULL,
	created_by_name VARCHAR (50) NOT NULL,
	last_updated_at TIMESTAMP
);

-- tote_id foreign key with tote
ALTER TABLE tote_items
ADD CONSTRAINT tote_items_fk
FOREIGN KEY (tote_id)
REFERENCES tote (tote_id)
ON DELETE RESTRICT;

-- item_id foreign key with inventory
ALTER TABLE tote_items
ADD CONSTRAINT tote_items_inventory_fk
FOREIGN KEY (item_id)
REFERENCES inventory (item_id)
ON DELETE RESTRICT;

-- Replenishment
CREATE TABLE IF NOT EXISTS replenishment_pool (
	id BIGSERIAL PRIMARY KEY,
	warehouse_id VARCHAR (24) NOT NULL,
	client_id VARCHAR (24) NOT NULL,
	client_name VARCHAR (50) NOT NULL,
	item_id VARCHAR (24) NOT NULL,
	sku VARCHAR (100) NOT NULL,
	quantity INT NOT NULL,
	unit VARCHAR (50) NOT NULL,
	added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL
);

CREATE TABLE IF NOT EXISTS replenishment_slip (
	id BIGSERIAL PRIMARY KEY,
	replenishment_slip_id uuid NOT NULL,
	code VARCHAR (50) NOT NULL,
	status VARCHAR (50) NOT NULL,
	warehouse_id VARCHAR (24) NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
	created_by_id VARCHAR (24) NOT NULL,
	created_by_name VARCHAR (50) NOT NULL,
	assigned_user_id VARCHAR (24),
	assigned_user_name VARCHAR (50),
	assigned_at TIMESTAMP,
	last_updated_at TIMESTAMP,
    UNIQUE (replenishment_slip_id)
);

CREATE TABLE IF NOT EXISTS replenishment_slip_items (
	id BIGSERIAL PRIMARY KEY,
	replenishment_slip_id uuid NOT NULL,
	item_id VARCHAR (24) NOT NULL,
	sku VARCHAR (100) NOT NULL,
	batch_number VARCHAR (50),
	serial_number VARCHAR (50),
	expiration_date DATE,
	quantity INT NOT NULL,
	unit VARCHAR (50) NOT NULL,
	source_location_id VARCHAR (24) NOT NULL,
	source_location_code VARCHAR (100) NOT NULL,
	source_location_qty INT NOT NULL,
	cart_id uuid NOT NULL,
	cart_code VARCHAR (100) NOT NULL,
	cart_qty INT NOT NULL,
	destination_location_id VARCHAR (24) NOT NULL,
	destination_location_code VARCHAR (100) NOT NULL,
	destination_location_qty INT NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
	created_by_id VARCHAR (24) NOT NULL,
	created_by_name VARCHAR (50) NOT NULL,
	last_updated_at TIMESTAMP,
    UNIQUE (replenishment_slip_id, source_location_id, cart_id, destination_location_id, item_id, batch_number, serial_number, expiration_date)
);

-- replenishment_slip_id foreign key with replenishment_slip
ALTER TABLE replenishment_slip_items
ADD CONSTRAINT replenishment_slip_items_replenishment_slip_id_fk
FOREIGN KEY (replenishment_slip_id)
REFERENCES replenishment_slip (replenishment_slip_id)
ON DELETE RESTRICT;

-- item_id foreign key with inventory
ALTER TABLE replenishment_slip_items
ADD CONSTRAINT replenishment_slip_items_item_id_fk
FOREIGN KEY (item_id)
REFERENCES inventory (item_id)
ON DELETE RESTRICT;

-- cart_id foreign key with cart
ALTER TABLE replenishment_slip_items
ADD CONSTRAINT replenishment_slip_items_cart_id_fk
FOREIGN KEY (cart_id)
REFERENCES cart (cart_id)
ON DELETE RESTRICT;

-- cart_totes represents association between carts and totes
CREATE TABLE IF NOT EXISTS replenishment_carts (
	id BIGSERIAL PRIMARY KEY,
	replenishment_slip_id uuid NOT NULL,
	cart_id uuid NOT NULL
);

-- cart_id foreign key with cart
ALTER TABLE replenishment_carts
ADD CONSTRAINT cart_fk
FOREIGN KEY (cart_id)
REFERENCES cart (cart_id)
ON DELETE RESTRICT;

-- replenishment_slip_id foreign key with replenishment_slip
ALTER TABLE replenishment_carts
ADD CONSTRAINT replenishment_carts_replenishment_slip_id_fk
FOREIGN KEY (replenishment_slip_id)
REFERENCES replenishment_slip (replenishment_slip_id)
ON DELETE RESTRICT;