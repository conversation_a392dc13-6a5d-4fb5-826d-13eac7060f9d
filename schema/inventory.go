package schema

import (
	"time"

	uuid "github.com/satori/go.uuid"
)

type AuditItem struct {
	ClientID       string     `json:"client_id" validate:"required"`
	ClientName     string     `json:"client_name" validate:"required"`
	SKU            string     `json:"sku" validate:"required"`
	ItemID         string     `json:"item_id" validate:"required"`
	BatchNumber    *string    `json:"batch_number" validate:"required"`
	SerialNumber   *string    `json:"serial_number" validate:"required"`
	ExpirationDate *time.Time `json:"expiration_date" validate:"required"`
	Quantity       int        `json:"quantity" validate:"required"`
	Reason         *string    `json:"reason"`
	AddNewItem     bool       `json:"add_new_item"`
}

type ValidateAuditInventory struct {
	RequestID      string      `json:"request_id"`
	Username       string      `json:"username"`
	UserID         string      `json:"user_id"`
	ContainerID    uuid.UUID   `json:"container_id"`
	ContainerRefNo *string     `json:"container_ref_no"`
	LocationID     string      `json:"location_id" validate:"required"`
	AuditItems     []AuditItem `json:"audit_items"`
}

type SessionDetails struct {
	RequestID string `json:"request_id"`
	Username  string `json:"username"`
	UserID    string `json:"user_id"`
}

type AreaDetails struct {
	NumLocations int `json:"num_locations"`
	NumItems     int `json:"num_items"`
}

type ValidateUpdateItemQuantity struct {
	WarehouseID string `json:"warehouse_id"`
	ClientID    string `json:"client_id" validate:"required"`
	UserID      string `json:"user_id"`
	Username    string `json:"username"`
	RequestID   string `json:"request_id"`
	ItemID      string `json:"item_id" validate:"required"`
	Quantity    int    `json:"quantity" validate:"required"`

	// # Is Frontend Request: It indicates if the 'request' is from the 'frontend' or 'gRPC'
	IsFrontendRequest bool `json:"is_frontend_request"`
}
