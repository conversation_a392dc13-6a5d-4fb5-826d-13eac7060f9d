package schema

import (
	"time"

	uuid "github.com/satori/go.uuid"
)

// # ValidateGenerateQRCode struct validates the schema for generating QR code
type ValidateContainerQRCode struct {
	FileType        string `json:"file_type" validate:"required,oneof=csv pdf"`
	LabelType       string `json:"label_type" validate:"required_if=FileType pdf"`
	Count           int    `json:"count" validate:"required,gte=1"`
	ContainerTypeID int    `json:"container_type_id" validate:"required,gte=1"`
}

// # QR Code
type QRCode struct {
	Type          string    `json:"type,omitempty" bson:"type,omitempty"`
	WarehouseName string    `json:"warehouse_name,omitempty" bson:"warehouse_name,omitempty"`
	Data          *[]QRData `json:"data,omitempty" bson:"data,omitempty"`
}

// # QR Code Data
type QRData struct {
	QRcode string `json:"qrcode,omitempty" bson:"qrcode,omitempty"`
	Code   string `json:"code,omitempty" bson:"code,omitempty"`
}

type ContainerResponse struct {
	ContainerID   *uuid.UUID `json:"container_id"`
	Code          *string    `json:"code"`
	ContainerType *string    `json:"container_type"`
	WarehouseID   *string    `json:"warehouse_id"`
	LocationID    *string    `json:"location_id"`
	LocationCode  *string    `json:"location_code"`
	LastUpdatedAt *time.Time `json:"last_updated_at"`
	CreatedAt     *time.Time `json:"created_at"`
	IsShipped     *bool      `json:"is_shipped"`
	RefNo         *string    `json:"ref_no"`
	Items         []Item     `json:"items"`
}

// # Item Object
type Item struct {
	ItemID         *string    `json:"item_id"`
	Quantity       *int32     `json:"quantity"`
	AvailableQty   *int32     `json:"available_qty"`
	SKU            *string    `json:"sku"`
	Scannable      *string    `json:"scannable"`
	BatchNumber    *string    `json:"batch_number"`
	SerialNumber   *string    `json:"serial_number"`
	ExpirationDate *time.Time `json:"expiration_date"`
	BaseUnit       *string    `json:"base_unit"`
	Name           *string    `json:"name"`
	Image          *string    `json:"image"`
	Description    *string    `json:"description"`
	ClientID       *string    `json:"client_id"`
	ClientName     *string    `json:"client_name"`
}

type ValidateMoveContainersBtwnLocations struct {
	ClientID            string    `json:"client_id" validate:"required"`
	WarehouseID         string    `json:"warehouse_id" validate:"required"`
	SourceLocation      string    `json:"source_location" validate:"required"`
	DestinationLocation string    `json:"destination_location" validate:"required"`
	ContainerID         uuid.UUID `json:"container_id" validate:"required"`
}

type ValidateMoveItemsBtwnContainers struct {
	ClientID             string          `json:"client_id" validate:"required"`
	WarehouseID          string          `json:"warehouse_id" validate:"required"`
	SourceContainer      uuid.UUID       `json:"source_container" validate:"required"`
	DestinationContainer uuid.UUID       `json:"destination_container" validate:"required"`
	Items                []ContainerItem `json:"items" validate:"required"`
}

type ContainerItem struct {
	SKU            string     `json:"sku" validate:"required"`
	ItemID         string     `json:"item_id" validate:"required"`
	BatchNumber    *string    `json:"batch_number"`
	SerialNumber   *string    `json:"serial_number"`
	ExpirationDate *time.Time `json:"expiration_date"`
	Quantity       int        `json:"quantity" validate:"required"`
}

type ValidateMoveItems struct {
	SourceLocation              *string         `json:"source_location"`
	DestinationLocation         *string         `json:"destination_location"`
	ExplicitDestinationLocation *string         `json:"explicit_destination_location"`
	SourceContainer             *uuid.UUID      `json:"source_container"`
	DestinationContainer        *uuid.UUID      `json:"destination_container"`
	Container                   *uuid.UUID      `json:"container"`
	Items                       []ContainerItem `json:"items"`
}

type ValidateCreateContainerType struct {
	Name       string `json:"name" validate:"required"`
	ShipsWhole *bool  `json:"ships_whole" validate:"required"`
}

type ValidateGetItemContainers struct {
	ClientID       string     `json:"client_id" validate:"required"`
	ItemID         string     `json:"item_id" validate:"required"`
	LocationID     string     `json:"location_id" validate:"required"`
	BatchNumber    *string    `json:"batch_number"`
	SerialNumber   *string    `json:"serial_number"`
	ExpirationDate *time.Time `json:"expiration_date"`
}

type Container struct {
	LocationID        string     `json:"location_id"`
	ContainerID       uuid.UUID  `json:"container_id"`
	Code              string     `json:"code"`
	ContainerType     string     `json:"container_type"`
	Quantity          *int       `json:"quantity,omitempty"`
	AvailableQuantity *int       `json:"available_quantity,omitempty"`
	BatchNumber       *string    `json:"batch_number,omitempty"`
	SerialNumber      *string    `json:"serial_number,omitempty"`
	ExpirationDate    *time.Time `json:"expiration_date,omitempty"`
}

// # Aggregate Container Object
type AggregateContainer struct {
	ContainerID      *uuid.UUID              `json:"container_id"`
	Code             *string                 `json:"code"`
	ContainerType    *string                 `json:"container_type"`
	ContainerDetails *AggregateContainerItem `json:"container_details"`
}

// # Aggregate Container Item Object
type AggregateContainerItem struct {
	LastUpdatedAt *time.Time `json:"last_updated_at"`
	CreatedAt     *time.Time `json:"created_at"`
	IsShipped     *bool      `json:"is_shipped"`
	Items         []*Item    `json:"items"` // # Container Items
}

type ContainerItemObj struct {
	Code           string
	ItemID         *string
	SKU            *string
	BatchNumber    *string
	SerialNumber   *string
	ExpirationDate *time.Time
	Quantity       int
	AvailableQty   int
	ContainerType  string
}

// location: warehouse id, location id, code, area name, area id
// container: warehouse_id, container id, code, container type id

type Mismatched struct {
	Containers    []string
	LenContainers int
	Locations     []string
	LenLocations  int
}

type ValidateDeleteContainer struct {
	ContainerID uuid.UUID `json:"container_id" validate:"required"`
}
