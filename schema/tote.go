package schema

type ValidateCreateTote struct {
	NumberOfTotes  int64   `json:"number_of_totes" validate:"required"`
	Name           string  `json:"name" validate:"required"`
	Length         float64 `json:"length" validate:"required"`
	Width          float64 `json:"width" validate:"required"`
	Height         float64 `json:"height" validate:"required"`
	DimsUnit       string  `json:"dims_unit" validate:"required_with=Length Width Height"`
	WeightCapacity float64 `json:"weight_capacity" validate:"required"`
	WeightUnit     string  `json:"weight_unit" validate:"required_with=Weight"`

	FileType  string `json:"file_type" validate:"required,oneof=csv pdf"`
	LabelType string `json:"label_type" validate:"required_if=FileType pdf"`

	WarehouseID   string `json:"warehouse_id"`
	WarehouseName string `json:"warehouse_name"`
	UserID        string `json:"user_id"`
	Username      string `json:"username"`
}
