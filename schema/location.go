package schema

import (
	"time"

	uuid "github.com/satori/go.uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type LocationResponse struct {
	ID                *int64     `json:"id"`
	WarehouseID       *string    `json:"warehouse_id"`
	ClientID          *string    `json:"client_id"`
	LocationID        *string    `json:"location_id"`
	Code              *string    `json:"code"`
	AreaName          *string    `json:"area_name"`
	AreaID            *string    `json:"area_id"`
	OnHold            *bool      `json:"on_hold"`
	OnHoldByID        *string    `json:"on_hold_by_id"`
	LastUpdatedAt     *time.Time `json:"last_updated_at"`
	LastUpdatedByID   *string    `json:"last_updated_by_id"`
	LastUpdatedByName *string    `json:"last_updated_by_name"`
	CreatedAt         *time.Time `json:"created_at"`
	Items             []Item     `json:"items"`
	IsPrime           *bool      `json:"is_prime"`
}

// # Aggregate Location Response Object
type AggregateLocation struct {
	ID                *int64                `json:"id"`
	WarehouseID       *string               `json:"warehouse_id"`
	ClientID          *string               `json:"client_id"`
	LocationID        *string               `json:"location_id"`
	Code              *string               `json:"code"`
	AreaName          *string               `json:"area_name"`
	AreaID            *string               `json:"area_id"`
	OnHold            *bool                 `json:"on_hold"`
	OnHoldByID        *string               `json:"on_hold_by_id"`
	LastUpdatedAt     *time.Time            `json:"last_updated_at"`
	LastUpdatedByID   *string               `json:"last_updated_by_id"`
	LastUpdatedByName *string               `json:"last_updated_by_name"`
	CreatedAt         *time.Time            `json:"created_at"`
	IsPrime           *bool                 `json:"is_prime"`
	Items             []*Item               `json:"items"`
	Containers        []*AggregateContainer `json:"containers"`
}

// # Item Locations Response Object
type ItemLocations struct {
	Locations []*ItemLocation `json:"locations"` // # List of Item Locations
}

// # Item Location Object
type ItemLocation struct {
	LocationID   *string          `json:"location_id"`
	LocationCode *string          `json:"location_code"`
	Containers   []*ItemContainer `json:"containers"` // # List of Item Containers on Location
}

// # Item Container Object
type ItemContainer struct {
	ContainerID   *string    `json:"container_id"`
	ContainerType *string    `json:"container_type"`
	ItemName      *string    `json:"item_name"`
	Unit          *string    `json:"unit"`
	LPCode        *string    `json:"lp_code"`
	BatchNumber   *string    `json:"batch_number"`
	SerialNumber  *string    `json:"serial_number"`
	Expiry        *time.Time `json:"expiry_date"`
	TotalQuantity *int       `json:"total_qty"`
}

type Location struct {
	LocationID        string  `json:"location_id"`
	AreaID            string  `json:"area_id"`
	Code              string  `json:"code"`
	AreaName          *string `json:"area_name"`
	Quantity          int     `json:"quantity"`
	AvailableQuantity int     `json:"available_quantity"`
	PickableQuantity  int     `json:"pickable_quantity"`
	BaseUnit          string  `json:"base_unit"`
	ItemOnHold        bool    `json:"item_on_hold"`
	LocationOnHold    bool    `json:"location_on_hold"`
}

type LocationGroup struct {
	ItemID         string `json:"item_id"`
	Code           string `json:"code"`
	LocationID     string `json:"location_id"`
	Quantity       int    `json:"quantity"`
	AvailableQty   int    `json:"available_qty"`
	LocationOnHold bool   `json:"location_on_hold"`
}

type ValidateGetItemLocations struct {
	ItemID                        string     `json:"item_id" validate:"required"`
	BatchNumber                   *string    `json:"batch_number"`
	SerialNumber                  *string    `json:"serial_number"`
	ExpirationDate                *time.Time `json:"expiration_date"`
	CombineItemQuantityOnLocation bool       `json:"combine_item_quantity_on_location"`
}

type ValidateGetItemLocationsGroup struct {
	ItemID         string     `json:"item_id" validate:"required"`
	BatchNumber    *string    `json:"batch_number"`
	SerialNumber   *string    `json:"serial_number"`
	ExpirationDate *time.Time `json:"expiration_date"`
}

type ValidateHoldLocation struct {
	LocationID string `json:"location_id" validate:"required"`
	IsHold     *bool  `json:"is_hold" validate:"required"`

	UserID   string `json:"user_id"`
	Username string `json:"username"`
}

type LocationItemObj struct {
	ItemID         string
	BatchNumber    *string
	SerialNumber   *string
	ExpirationDate *time.Time
	AvailableQty   int
}

type ValidateSuggestLocation struct {
	ContainerID *uuid.UUID `json:"container_id"`
	ItemID      *string    `json:"item_id"`

	UserID      string `json:"user_id"`
	Username    string `json:"username"`
	WarehouseID string `json:"warehouse_id"`
}

type MakeAreaPickableTracked struct {
	ItemID         *string
	BatchNumber    *string
	SerialNumber   *string
	ExpirationDate *time.Time
	Quantity       int
}

type MakeAreaPickable struct {
	ItemID   *string
	Quantity int
}
type TrackedItemDetails struct {
	Quantity       int        `json:"quantity"`
	AvailableQty   int        `json:"available_qty"`
	BatchNumber    *string    `json:"batch_number"`
	SerialNumber   *string    `json:"serial_number"`
	ExpirationDate *time.Time `json:"expiration_date"`
	CreatedAt      *time.Time `json:"created_at"`
}

type ValidateSuggestLocationForWorkOrder struct {
	ItemID string `json:"item_id" validate:"required"`

	UserID      string `json:"user_id"`
	Username    string `json:"username"`
	WarehouseID string `json:"warehouse_id"`
}

type ExistingLocations struct {
	LocationID string
	Code       string
	AreaName   string
	Quantity   int64
}

type ValidateDeleteLocations struct {
	LocationIDs []string `json:"location_ids" validate:"required"`

	WarehouseID primitive.ObjectID `json:"warehouse_id"`
	UserID      string             `json:"user_id"`
	Username    string             `json:"username"`
	Email       string             `json:"email"`
	Name        string             `json:"name"`
}

type AreaPopertiesBool struct {
	IsPrime    bool
	IsPickable bool
	IsHold     bool
}
