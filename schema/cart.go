package schema

import (
	"inventory/model"
	"time"

	uuid "github.com/satori/go.uuid"
)

type ValidateCreateCart struct {
	NumberOfCarts  int64   `json:"number_of_carts" validate:"required"`
	<PERSON><PERSON>          string  `json:"alias"`
	RetainTote     bool    `json:"retain_tote"`
	Length         float64 `json:"length"`
	Width          float64 `json:"width"`
	Height         float64 `json:"height"`
	DimsUnit       string  `json:"dims_unit" validate:"required_with=Length Width Height"`
	WeightCapacity float64 `json:"weight_capacity"`
	WeightUnit     string  `json:"weight_unit" validate:"required_with=Weight"`

	FileType  string `json:"file_type" validate:"required,oneof=csv pdf"`
	LabelType string `json:"label_type" validate:"required_if=FileType pdf"`

	WarehouseID   string `json:"warehouse_id"`
	WarehouseName string `json:"warehouse_name"`
	UserID        string `json:"user_id"`
	Username      string `json:"username"`
}

type ValidatePrepareCart struct {
	CartID  *uuid.UUID  `json:"cart_id" validate:"required"`
	ToteIDs []uuid.UUID `json:"tote_ids" validate:"required"`

	WarehouseID   string `json:"warehouse_id"`
	WarehouseName string `json:"warehouse_name"`
	UserID        string `json:"user_id"`
	Username      string `json:"username"`
}

type Cart struct {
	ID   uuid.UUID `json:"_id"`
	Code string    `json:"code"`
}

type Tote struct {
	ID    uuid.UUID         `json:"_id"`
	Code  string            `json:"code"`
	Items []*model.ToteItem `json:"items"`
}

type OrderCartDetailsResponse struct {
	WarehouseID *string `json:"warehouse_id"`
	OrderID     *string `json:"order_id"`
	OrderCode   *string `json:"order_code"`
	Cart        *Cart   `json:"cart"`
	Tote        *Tote   `json:"tote"`
}

type OrderCartDetails struct {
	CartID         *uuid.UUID
	Code           *string
	ID             *int64
	WarehouseID    *string
	ToteID         *uuid.UUID
	ToteCode       *string
	ClientID       *string
	ClientName     *string
	OperationType  *string
	ItemID         *string
	Name           *string
	SKU            *string
	Scannable      *string
	BatchNumber    *string
	SerialNumber   *string
	ExpirationDate *time.Time
	Quantity       *int32
	PicklistID     *string
	PicklistCode   *string
	OrderID        *string
	OrderCode      *string
	CreatedAt      *time.Time
	CreatedByID    *string
	CreatedByName  *string
	LastUpdatedAt  *time.Time
}

type ValidateRemoveToteFromCart struct {
	CartID *uuid.UUID `json:"cart_id" validate:"required"`
	ToteID *uuid.UUID `json:"tote_id" validate:"required"`

	WarehouseID   string `json:"warehouse_id"`
	WarehouseName string `json:"warehouse_name"`
	UserID        string `json:"user_id"`
	Username      string `json:"username"`
}

type ValidatePrintCart struct {
	CartID        *uuid.UUID `json:"cart_id" validate:"required"`
	OnlyPrintCart bool       `json:"only_print_cart"`

	FileType  string `json:"file_type" validate:"required,oneof=csv pdf"`
	LabelType string `json:"label_type" validate:"required_if=FileType pdf"`

	WarehouseID   string `json:"warehouse_id"`
	WarehouseName string `json:"warehouse_name"`
	UserID        string `json:"user_id"`
	Username      string `json:"username"`
}
