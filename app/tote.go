package app

import (
	"context"
	"encoding/base64"
	"fmt"
	"inventory/model"
	"inventory/schema"
	"strconv"
	"strings"
	"time"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/pkg/errors"
	"github.com/rs/zerolog"
	uuid "github.com/satori/go.uuid"
)

// Tote defines methods of Tote service to be implemented
type Tote interface {
	CreateTote(*schema.ValidateCreateTote) ([]string, error)
	CreateToteType(*schema.ValidateCreateTote) error
	GetToteTypes(string) ([]model.ToteType, error)
	GetTotes(string, bool) ([]model.Tote, error)
	GetTote(uuid.UUID) (*model.Tote, error)
}

// ToteOpts contains arguments to be accepted for new instance of Tote service
type ToteOpts struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// ToteImpl implements Tote service
type ToteImpl struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// InitTote returns initializes Tote service
func InitTote(opts *ToteOpts) Tote {
	e := &ToteImpl{
		App:    opts.App,
		DB:     opts.DB,
		Logger: opts.Logger,
	}
	return e
}

func (t *ToteImpl) GetPreviousToteCodeInt(whID string) (*int, error) {
	// Tote-0001
	// Previous tote code
	var prevCode string
	var prevCodeInt int
	query := `SELECT code FROM tote WHERE warehouse_id = $1 ORDER BY NULLIF(regexp_replace(code, '\D', '', 'g'), '')::int DESC LIMIT 1`
	err := t.DB.QueryRow(context.TODO(), query, whID).Scan(&prevCode)
	if err != nil {
		if err == pgx.ErrNoRows {
			prevCodeInt = 0
		} else {
			return nil, errors.Wrap(err, "failed to query for totes")
		}
	} else {
		if len(prevCode) != 0 {
			prevCodeInt, err = strconv.Atoi(strings.Split(prevCode, "-")[1])
			if err != nil {
				return nil, errors.Wrap(err, "failed to convert tote code to int")
			}
		} else {
			prevCodeInt = 0
		}
	}
	return &prevCodeInt, nil
}

// CreateTote creates a new totes
func (t *ToteImpl) CreateTote(data *schema.ValidateCreateTote) ([]string, error) {
	// # Create Context
	ctx := context.Background()

	// Create transaction
	tx, err := t.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to begin transaction")
	}
	defer tx.Rollback(ctx)

	// # Get Tote Type ID if it exists else create it
	var toteTypeID int
	query := `SELECT id FROM tote_type WHERE name = $1 AND warehouse_id = $2`
	err = t.DB.QueryRow(context.TODO(), query, data.Name, data.WarehouseID).Scan(&toteTypeID)
	if err != nil {
		if err == pgx.ErrNoRows {
			// # Create Tote Type if it does not exist
			err = t.CreateToteType(data)
			if err != nil {
				tx.Rollback(ctx)
				return nil, err
			}
			// # Get Tote Type ID
			err = t.DB.QueryRow(context.TODO(), query, data.Name, data.WarehouseID).Scan(&toteTypeID)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "failed to get tote type id after creation")
			}
		} else {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "failed to get tote type id")
		}
	}

	// # Previous tote code
	prevCodeInt, err := t.GetPreviousToteCodeInt(data.WarehouseID)
	if err != nil {
		tx.Rollback(ctx)
		return nil, err
	}

	// # QR Struct
	var QR schema.QRCode

	// # QR Data Struct
	Data := []schema.QRData{}

	// # Validate if the label type is one of the allowed values if the file type is pdf
	if (data.FileType == "pdf") && (data.LabelType != "minimal_1.25x3.5") && (data.LabelType != "minimal_1.5x4") && (data.LabelType != "minimal_4x6") {
		err := errors.New("label_type must be one of [minimal_1.25x3.5 minimal_1.5x4 minimal_4x6]")
		tx.Rollback(ctx)
		return nil, err
	}

	// # Create Totes
	now := time.Now().UTC()
	var qrCodes []string
	var rows [][]interface{}
	for i := 1; i <= int(data.NumberOfTotes); i++ {
		code := fmt.Sprintf("Tote-%d", *prevCodeInt+i) // # Tote-1
		tote_id := uuid.NewV4()
		qr := fmt.Sprintf("t/%s/%s", tote_id, code) // # t/tote_id/tote_code
		rows = append(rows, []interface{}{data.WarehouseID, tote_id, code, qr, "", "", now, data.UserID, data.Username, now, toteTypeID})

		// # CSV - QR Code Strings | PDF - QR Code Images
		if data.FileType == "csv" {
			// # Append to QR codes list
			qrCodes = append(qrCodes, qr)
		} else {
			// # Generate QR code PNG image
			bufBytes, err := t.App.Utils.GenerateQrCode(qr)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "Failed to generate QR code")
			}

			// # Encode image to base64 string
			imgBase64Str := base64.StdEncoding.EncodeToString(bufBytes)

			// # Construct QR
			qr := schema.QRData{
				QRcode: imgBase64Str,
				Code:   code,
			}

			// # Append to QR Data struct
			Data = append(Data, qr)
		}
	}

	// # Bulk insert rows
	copyCount, err1 := tx.CopyFrom(
		ctx,
		// # Table name
		pgx.Identifier{"tote"},
		// # Columns
		[]string{"warehouse_id", "tote_id", "code", "qr", "alias", "ref_no", "created_at", "created_by_id", "created_by_name", "last_updated_at", "tote_type_id"},
		// # Rows to insert
		pgx.CopyFromRows(rows),
	)
	if err1 != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err1, "unable to bulk insert totes")
	}
	if copyCount != int64(len(rows)) {
		tx.Rollback(ctx)
		return nil, errors.New("failed to insert some rows in the database")
	}

	if data.FileType == "pdf" {
		// # Construct Final QR Struct
		QR.Type = data.Name
		QR.WarehouseName = data.WarehouseName
		QR.Data = &Data

		// # Template File Variables
		var file, path string

		// # Check Label Type
		if data.LabelType == "minimal_1.25x3.5" {
			// # Label Type: minimal_1.25x3.5
			// # Template File Name
			file = t.App.Config.PDFConfig.LPLabel1x3MinimalTemplateFileName
			// # Template File Path
			path = t.App.Config.PDFConfig.LPLabel1x3MinimalTemplateFilePath
		} else if data.LabelType == "minimal_1.5x4" {
			// # Label Type: minimal_1.5x4
			// # Template File Name
			file = t.App.Config.PDFConfig.LPLabel1x4MinimalTemplateFileName
			// # Template File Path
			path = t.App.Config.PDFConfig.LPLabel1x4MinimalTemplateFilePath
		} else {
			// # Label Type: minimal_4x6
			// # Template File Name
			file = t.App.Config.PDFConfig.LPLabel4x6MinimalTemplateFileName
			// # Template File Path
			path = t.App.Config.PDFConfig.LPLabel4x6MinimalTemplateFilePath
		}

		// # Generate QR PDF
		buf, err := GenerateQrPDF(&QR, file, path, data.LabelType)
		if err != nil {
			msg := "Failed to generate PDF"
			t.Logger.Err(err).Msg(msg)
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, msg)
		}

		// # Base File Name
		baseName := "wms_tote_label.pdf"

		// # File Name Separator
		sep := "_"

		// # Construct file name for PDF file
		fileName := data.WarehouseName + sep + data.LabelType + sep + baseName

		// # S3 Bucket Name
		bucket := t.App.Config.AWSConfig.TempBucket

		// # Upload PDF File to S3
		fileUrl, err := t.App.SSS.AddQRPDFToS3(fileName, bucket, buf.Bytes())
		if err != nil {
			tx.Rollback(ctx)
			err = errors.Wrap(err, "Failed to upload QR codes PDF to S3!")
			return nil, err
		}

		// Commit transaction
		if tx_err := tx.Commit(ctx); tx_err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "failed to commit transaction for pdf, rolling back")
		}

		// # Return S3 URL of PDF file
		return []string{fileUrl}, nil
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back")
	}

	return qrCodes, nil
}

func (t *ToteImpl) CreateToteType(data *schema.ValidateCreateTote) error {
	// Create Tote Type
	query := `INSERT INTO tote_type (warehouse_id, name, length, width, height, dims_unit, weight_capacity, weight_unit, created_at, created_by_id, created_by_name) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`
	commandTag, err := t.DB.Exec(context.TODO(), query, data.WarehouseID, data.Name, data.Length, data.Width, data.Height, data.DimsUnit, data.WeightCapacity, data.WeightUnit, time.Now().UTC(), data.UserID, data.Username)
	if err != nil {
		return errors.Wrap(err, "failed to create tote type")
	}
	if commandTag.RowsAffected() != 1 {
		return errors.Wrap(err, "Tote type already exists for this warehouse")
	}

	return nil
}

func (t *ToteImpl) GetToteTypes(whID string) ([]model.ToteType, error) {
	var toteTypes []model.ToteType

	query := `SELECT * FROM tote_type WHERE warehouse_id = $1`
	rows, err := t.DB.Query(context.TODO(), query, whID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get tote types")
	}
	for rows.Next() {
		var toteType model.ToteType
		err := rows.Scan(
			&toteType.ID,
			&toteType.WarehouseID,
			&toteType.Name,
			&toteType.Length,
			&toteType.Width,
			&toteType.Height,
			&toteType.DimsUnit,
			&toteType.WeightCapacity,
			&toteType.WeightUnit,
			&toteType.CreatedAt,
			&toteType.CreatedByID,
			&toteType.CreatedByName,
		)
		if err != nil {
			return nil, errors.Wrap(err, "failed to scan tote_type rows")
		}
		toteTypes = append(toteTypes, toteType)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read tote_type rows")
	}

	return toteTypes, nil
}

// GetTotes returns all totes for a warehouse with the option to filter empty totes
func (t *ToteImpl) GetTotes(whID string, emptyTotes bool) ([]model.Tote, error) {
	// # Create Context
	ctx := context.Background()
	var query string

	// # Query totes
	if emptyTotes {
		query = `SELECT t.id, t.warehouse_id, t.tote_id, t.code, t.qr, t.alias, t.ref_no, t.created_at, t.created_by_id, t.created_by_name, t.last_updated_at, tt.length, tt.width, tt.height, tt.dims_unit, tt.weight_capacity, tt.weight_unit, tt.name FROM tote t JOIN tote_type tt ON t.tote_type_id = tt.id WHERE t.warehouse_id = $1 AND t.tote_id NOT IN (SELECT tote_id FROM tote_items) ORDER BY t.id DESC`
	} else {
		query = `SELECT t.id, t.warehouse_id, t.tote_id, t.code, t.qr, t.alias, t.ref_no, t.created_at, t.created_by_id, t.created_by_name, t.last_updated_at, tt.length, tt.width, tt.height, tt.dims_unit, tt.weight_capacity, tt.weight_unit, tt.name FROM tote t JOIN tote_type tt ON t.tote_type_id = tt.id WHERE t.warehouse_id = $1 ORDER BY t.id DESC`
	}
	rows, err := t.DB.Query(ctx, query, whID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to query for totes")
	}
	defer rows.Close()

	// # Totes
	var totes []model.Tote
	for rows.Next() {
		var tote model.Tote
		err = rows.Scan(
			&tote.ID,
			&tote.WarehouseID,
			&tote.ToteID,
			&tote.Code,
			&tote.QR,
			&tote.Alias,
			&tote.RefNo,
			&tote.CreatedAt,
			&tote.CreatedByID,
			&tote.CreatedByName,
			&tote.LastUpdatedAt,
			&tote.Length,
			&tote.Width,
			&tote.Height,
			&tote.DimsUnit,
			&tote.WeightCapacity,
			&tote.WeightUnit,
			&tote.Name,
		)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to scan tote rows")
		}

		// Fetch cartInfo for each tote
		var cartID *uuid.UUID
		var cartCode *string
		cartQuery := `SELECT ct.cart_id, c.code FROM cart_totes ct INNER JOIN cart c ON ct.cart_id = c.cart_id WHERE ct.tote_id = $1`
		err = t.DB.QueryRow(ctx, cartQuery, tote.ToteID).Scan(&cartID, &cartCode)
		if err != nil {
			if err == pgx.ErrNoRows {
				cartID = nil
				cartCode = nil
			} else {
				return nil, errors.Wrap(err, "Failed to query for tote cart")
			}
		}
		if cartID != nil && cartCode != nil {
			cartInfo := &model.CartInfo{
				CartID:   cartID,
				CartCode: cartCode,
			}
			tote.CartInfo = cartInfo
		}

		totes = append(totes, tote)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "Unable to read tote rows")
	}

	return totes, nil
}

// GetTote returns a tote by its ID
func (t *ToteImpl) GetTote(toteID uuid.UUID) (*model.Tote, error) {
	// # Create Context
	ctx := context.Background()

	// # Query Tote
	tote := &model.Tote{}
	query := `SELECT t.id, t.warehouse_id, t.tote_id, t.code, t.qr, t.alias, t.ref_no, t.created_at, t.created_by_id, t.created_by_name, t.last_updated_at, tt.length, tt.width, tt.height, tt.dims_unit, tt.weight_capacity, tt.weight_unit, tt.name FROM tote t JOIN tote_type tt ON t.tote_type_id = tt.id WHERE t.tote_id = $1`
	err := t.DB.QueryRow(ctx, query, toteID).Scan(
		&tote.ID,
		&tote.WarehouseID,
		&tote.ToteID,
		&tote.Code,
		&tote.QR,
		&tote.Alias,
		&tote.RefNo,
		&tote.CreatedAt,
		&tote.CreatedByID,
		&tote.CreatedByName,
		&tote.LastUpdatedAt,
		&tote.Length,
		&tote.Width,
		&tote.Height,
		&tote.DimsUnit,
		&tote.WeightCapacity,
		&tote.WeightUnit,
		&tote.Name,
	)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to query for tote")
	}

	// # Query Tote Items
	query = `SELECT client_id, client_name, operation_type, item_id, name, sku, scannable, batch_number, serial_number, expiration_date, quantity, picklist_id, picklist_code, order_id, order_code, created_at, created_by_id, created_by_name, last_updated_at FROM tote_items WHERE tote_id = $1`
	rows, err := t.DB.Query(ctx, query, toteID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to query for tote items")
	}
	for rows.Next() {
		toteItem := &model.ToteItem{}
		err := rows.Scan(
			&toteItem.ClientID,
			&toteItem.ClientName,
			&toteItem.OperationType,
			&toteItem.ItemID,
			&toteItem.Name,
			&toteItem.SKU,
			&toteItem.Scannable,
			&toteItem.BatchNumber,
			&toteItem.SerialNumber,
			&toteItem.ExpirationDate,
			&toteItem.Quantity,
			&toteItem.PicklistID,
			&toteItem.PicklistCode,
			&toteItem.OrderID,
			&toteItem.OrderCode,
			&toteItem.CreatedAt,
			&toteItem.CreatedByID,
			&toteItem.CreatedByName,
			&toteItem.LastUpdatedAt,
		)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to scan tote item rows")
		}
		tote.ToteItems = append(tote.ToteItems, toteItem)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "Unable to read tote item rows")
	}

	// check if tote is already mapped to a cart. if yes then get cartID and code
	var cartID *uuid.UUID
	var cartCode *string
	query = `SELECT ct.cart_id, c.code FROM cart_totes ct INNER JOIN cart c ON ct.cart_id = c.cart_id WHERE ct.tote_id = $1`
	err = t.DB.QueryRow(ctx, query, toteID).Scan(&cartID, &cartCode)
	if err != nil {
		if err == pgx.ErrNoRows {
			cartID = nil
			cartCode = nil
		} else {
			return nil, errors.Wrap(err, "Failed to query for tote cart")
		}
	}
	if cartID != nil && cartCode != nil {
		cartInfo := &model.CartInfo{
			CartID:   cartID,
			CartCode: cartCode,
		}
		tote.CartInfo = cartInfo
	}
	return tote, nil
}
