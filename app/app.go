package app

import (
	"inventory/server/auth"
	"inventory/server/config"
	"inventory/server/kafka"
	mongostorage "inventory/server/storage/mongodb"
	postgres "inventory/server/storage/postgres"
	redisstorage "inventory/server/storage/redis"

	"github.com/rs/zerolog"
	"google.golang.org/grpc"

	core_proto "proto/core"
	entity_proto "proto/entity"
	inbound_proto "proto/inbound"
	integrations_proto "proto/integrations"
	outbound_proto "proto/outbound"
	reporting_proto "proto/reporting"
)

// Options contains arguments required to create a new app instance
type Options struct {
	SessionAuth auth.SessionAuth
	MongoDB     *mongostorage.MongoStorage
	PostgresDB  *postgres.PostgresDB
	Redis       *redisstorage.RedisStorage
	Logger      *zerolog.Logger
	Config      *config.APPConfig
	GrpcClient  *GrpcClient
}

// App := contains resources to implement business logic
type App struct {
	SessionAuth auth.SessionAuth
	MongoDB     *mongostorage.MongoStorage
	PostgresDB  *postgres.PostgresDB
	Redis       *redisstorage.RedisStorage
	Logger      *zerolog.Logger
	Config      *config.APPConfig

	// Grpc
	GrpcClient *GrpcClient

	// AWS service
	SES SES
	SSS SSS

	//Services
	Utils        Utils
	Sample       Sample
	Container    Container
	Location     Location
	Inventory    Inventory
	Operations   Operations
	Transaction  Transaction
	Notification Notification
	Script       Script
	Cart         Cart
	Tote         Tote
	// CronManager
	CronManager CronManager

	// KAFKA
	DeleteContainerProducer        kafka.Producer
	DeleteContainerConsumer        kafka.Consumer
	TopicNotificationProducer      kafka.Producer
	SubscriberNotificationProducer kafka.Producer
}

// NewApp returns new app instance
func NewApp(opts *Options) *App {
	return &App{
		SessionAuth: opts.SessionAuth,
		MongoDB:     opts.MongoDB,
		PostgresDB:  opts.PostgresDB,
		Redis:       opts.Redis,
		Logger:      opts.Logger,
		Config:      opts.Config,
		SES:         NewSESImpl(&SESImplOpts{Config: &opts.Config.AWSConfig}),
		SSS:         NewSSSImpl(&SSSImplOpts{Config: &opts.Config.AWSConfig}),
		GrpcClient:  opts.GrpcClient,
	}
}

// Contains GRPC clients
type GrpcClient struct {
	Core         *Core
	Inbound      *Inbound
	Outbound     *Outbound
	Entity       *Entity
	Integrations *Integrations
	Reporting    *Reporting
	// Add new GRPC clients here
}

// Core client
type Core struct {
	Client core_proto.CoreClient
	Conn   *grpc.ClientConn
}

// Outbound client
type Outbound struct {
	Client outbound_proto.OutboundClient
	Conn   *grpc.ClientConn
}

// Inbound client
type Inbound struct {
	Client inbound_proto.InboundClient
	Conn   *grpc.ClientConn
}

// Entity client
type Entity struct {
	Client entity_proto.EntityClient
	Conn   *grpc.ClientConn
}

// Integrations client
type Integrations struct {
	Client integrations_proto.IntegrationsClient
	Conn   *grpc.ClientConn
}

// Reporting client
type Reporting struct {
	Client reporting_proto.ReportingClient
	Conn   *grpc.ClientConn
}
