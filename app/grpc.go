package app

import (
	"context"
	"fmt"
	"inventory/model"
	"inventory/schema"
	"inventory/util"
	"log"
	"math"
	"strconv"
	"strings"
	"time"

	core_proto "proto/core"
	inventory_proto "proto/inventory"
	outbound_proto "proto/outbound"
	reporting_proto "proto/reporting"

	"github.com/getsentry/sentry-go"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	uuid "github.com/satori/go.uuid"
	errors "github.com/vasupal1996/goerror"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// GRPC server initializer. All GRPC functions will be an implementation of this struct
type Grpc struct {
	App *App
	DB  *pgxpool.Pool
	inventory_proto.UnimplementedInventoryServer
}

// Example GRPC server function
// Input: context and request structure from the service proto file
// Output: response structure from the service proto file and error
func (s *Grpc) HelloInventory(ctx context.Context, req *inventory_proto.HelloInventoryRequest) (*inventory_proto.HelloInventoryResponse, error) {
	var resp string
	if req.GetName() == "inventory" && req.GetValue() == 04 {
		resp = "Hii, Thanks , from Inventory"
	}

	return &inventory_proto.HelloInventoryResponse{
		Message:     resp,
		Value:       6 + req.GetValue(),
		RespondedAt: timestamppb.New(time.Now()),
	}, nil
}

// InsertInventory function receives inventory create request data and insertes inventory into database
func (s *Grpc) InsertInventory(ctx context.Context, req *inventory_proto.InsertInventoryRequest) (*inventory_proto.InsertInventoryResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	var rows [][]interface{}
	for _, item := range req.InsertInventory {
		var image *string
		if item.GetImage() == "" {
			image = nil
		} else {
			v := item.GetImage()
			image = &v
		}
		// Dynamic Column
		var dynamic_column *map[string]string
		if item.DynamicColumns != nil {
			dynamicColumn := make(map[string]string)
			for _, column := range item.DynamicColumns {
				dynamicColumn[column.Name] = column.Value
			}
			dynamic_column = &dynamicColumn
		} else {
			dynamic_column = nil
		}
		rows = append(rows, []interface{}{item.GetItemId(), item.GetClientId(), item.GetClientName(), item.GetWarehouseId(), 0, 0, 0, item.GetBaseUnit(), item.GetSku(), item.GetScannable(), item.GetName(), item.GetDescription(), item.GetIsPerishable(), item.GetIsBatchControlled(), item.GetIsTrackedBySerialNo(), image, item.GetIsKit(), dynamic_column, item.GetIsLeanship()})
	}
	// Bulk insert rows
	copyCount, err1 := tx.CopyFrom(
		ctx,
		// Table name
		pgx.Identifier{"inventory"},
		// Columns
		[]string{"item_id", "client_id", "client_name", "warehouse_id", "quantity", "available_qty", "pickable_qty", "base_unit", "sku", "scannable", "name", "description", "is_perishable", "is_batch_controlled", "is_tracked_by_serial_no", "image", "is_kit", "dynamic_column", "is_leanship"},
		// Rows to insert
		pgx.CopyFromRows(rows),
	)
	if err1 != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err1, "unable to bulk insert inventory", &errors.DBError)
	}
	if copyCount != int64(len(rows)) {
		tx.Rollback(ctx)
		return nil, errors.New("failed to insert some rows in the inventory database", &errors.DBError)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back", &errors.DBError)
	}

	return &inventory_proto.InsertInventoryResponse{
		Success: true,
	}, nil
}

// UpdateAvailableQty function receives items information to be changed when order is created
func (s *Grpc) UpdateAvailableQty(ctx context.Context, req *inventory_proto.UpdateAvailableQtyRequest) (*inventory_proto.UpdateAvailableQtyResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)
	var backOrderAutoAlloationItems []string
	var itemIDs []string
	for _, item := range req.GetAllocatedInventory() {
		// Update available quantity in inventory
		var batchNo *string
		if item.GetBatchNumber() == "" {
			batchNo = nil
		} else {
			v := item.GetBatchNumber()
			batchNo = &v
		}

		var serialNo *string
		if item.GetSerialNumber() == "" {
			serialNo = nil
		} else {
			v := item.GetSerialNumber()
			serialNo = &v
		}

		var expDate *time.Time
		if item.GetExpirationDate() == nil {
			expDate = nil
		} else {
			v := item.GetExpirationDate().AsTime()
			expDate = &v
		}

		// Get item from inventory
		inventory_item, err := s.App.Operations.GetItemFromInventory(item.GetItemId(), batchNo, serialNo, expDate)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Failed to get item from inventory "+item.GetItemId(), &errors.DBError)
		}
		if item.GetAlloatedQty() > 0 {
			if inventory_item.OnHold {
				tx.Rollback(ctx)
				return nil, errors.New("The item "+inventory_item.Name+" is on hold", &errors.DBError)
			}
		}

		query := `UPDATE inventory SET available_qty = available_qty - $1 WHERE item_id = $2` // [TAP] - Only updating available quantity
		commandTag, err := tx.Exec(ctx, query, item.GetAlloatedQty(), item.GetItemId())
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Unable to update inventory", &errors.DBError)
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return nil, errors.New("Inventory wasn't updated", &errors.DBError)
		}

		if batchNo != nil || serialNo != nil || expDate != nil {
			// any tracked parameter exists
			query := `UPDATE tracked_inventory SET available_qty = available_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL)` // [TAP] - Only updating available quantity
			commandTag, err := tx.Exec(ctx, query, item.GetAlloatedQty(), item.GetItemId(), batchNo, serialNo, expDate)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "unable to update tracked inventory", &errors.DBError)
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return nil, errors.New("tracked inventory wasn't updated", &errors.DBError)
			}
		}

		if item.GetAlloatedQty() < 0 {
			backOrderAutoAlloationItems = append(backOrderAutoAlloationItems, item.GetItemId())
		}
		itemIDs = append(itemIDs, item.GetItemId())
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back", &errors.DBError)
	}

	// Auto allocation of back orders
	if len(backOrderAutoAlloationItems) > 0 {
		go s.App.Operations.BackOrderAutoAlloation(backOrderAutoAlloationItems, req.GetExcludedOrderIds(), req.GetEventType(), req.GetAllowReallocation())
	}
	for _, itemID := range itemIDs {
		go s.App.Operations.UpdateShopifyInventory(itemID)
	}

	return &inventory_proto.UpdateAvailableQtyResponse{
		Success: true,
	}, nil
}

// Receive Arn scanning data from inbound and update those arn items in the inventory
func (s *Grpc) UpdateArnItems(grpcCtx context.Context, req *inventory_proto.UpdateArnItemsRequest) (*inventory_proto.UpdateArnItemsResponse, error) {
	ctx := context.Background()
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)
	var backOrderAutoAlloationItems []string
	for i, item := range req.GetItems() {
		container_id := uuid.FromStringOrNil(item.GetContainerId())

		var containerCode *string
		var containerID *uuid.UUID
		containerType := "loose"
		if container_id != uuid.Nil {
			v := container_id
			containerID = &v

			// Get container type from id
			query := `SELECT ct.name FROM container_wise_inventory c INNER JOIN container_type ct ON ct.id = c.container_type_id WHERE c.container_id = $1`
			err = tx.QueryRow(ctx, query, container_id).Scan(&containerType)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "unable to get the container type", &errors.DBError)
			}
		}
		if item.GetContainerCode() != "" {
			v := item.GetContainerCode()
			containerCode = &v
		}

		var containerRef *string
		if item.GetContainerRefNo() == "" {
			containerRef = nil
		} else {
			v := item.GetContainerRefNo()
			containerRef = &v
		}

		var batchNo *string
		if item.GetBatchNumber() == "" {
			batchNo = nil
		} else {
			v := item.GetBatchNumber()
			batchNo = &v
		}

		var serialNo *string
		if item.GetSerialNumber() == "" {
			serialNo = nil
		} else {
			v := item.GetSerialNumber()
			serialNo = &v
		}

		var expDate *time.Time
		if item.GetExpirationDate() == nil {
			expDate = nil
		} else {
			v := item.GetExpirationDate().AsTime()
			expDate = &v
		}

		var pickableQty int32
		if item.GetIsAreaPickable() {
			pickableQty = item.GetQuantity()
		} else {
			pickableQty = 0
		}

		var availableQty int32
		if item.GetIsAreaHold() {
			availableQty = 0
		} else {
			availableQty = item.GetQuantity()
		}

		// Check if location is on hold
		query := `SELECT on_hold FROM location_wise_inventory WHERE location_id = $1`
		var locationOnHold bool
		err = tx.QueryRow(ctx, query, item.GetLocationId()).Scan(&locationOnHold)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "unable to get location on hold status", &errors.DBError)
		}
		if locationOnHold {
			pickableQty = 0
		}

		// Get item from inventory
		inventory_item, err := s.App.Operations.GetItemFromInventory(item.GetItemId(), batchNo, serialNo, expDate)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "failed to get item from inventory "+item.GetItemId(), &errors.DBError)
		}

		// Update item on location
		// Check if the item exists on the location
		var exists bool
		query = `SELECT EXISTS(SELECT 1 FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND location_id = $4 AND warehouse_id = $5 AND (expiration_date = $6 OR $6 IS NULL)) AS "exists"`
		err = tx.QueryRow(ctx, query, inventory_item.ItemID, batchNo, serialNo, item.GetLocationId(), req.GetWarehouseId(), expDate).Scan(&exists)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "unable to check if item exists on location", &errors.DBError)
		}
		if exists {
			// Increment the quantity on the location since a item is getting added to a container on that location
			query := `UPDATE location_wise_inventory SET quantity = quantity + $1, available_qty = available_qty + $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)`
			commandTag, err := tx.Exec(ctx, query, item.GetQuantity(), inventory_item.ItemID, batchNo, serialNo, item.GetLocationId(), req.GetWarehouseId(), expDate)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "error incrementing item quantity on receiving location", &errors.DBError)
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return nil, errors.New("item quantity wasn't incremented on the receiving location", &errors.DBError)
			}
		} else {
			// Item doesn't exist on the location, insert a new row
			err := s.App.Operations.InsertItemInLocation(tx, ctx, item.GetLocationId(), req.GetWarehouseId(), int(item.GetQuantity()), int(item.GetQuantity()), inventory_item, batchNo, serialNo, expDate)
			if err != nil {
				tx.Rollback(ctx)
				return nil, err
			}
		}

		// Update inventory
		query = `UPDATE inventory SET quantity = quantity + $1, available_qty = available_qty + $2, pickable_qty = pickable_qty + $3 WHERE item_id = $4`
		commandTag, err := tx.Exec(ctx, query, item.GetQuantity(), availableQty, pickableQty, inventory_item.ItemID)
		if err != nil {
			tx.Rollback(ctx)
			var e error
			e = errors.Wrap(err, "unable to update inventory", &errors.DBError)
			if err == pgx.ErrNoRows {
				e = errors.New("sku doesn't exist in the inventory", &errors.DBError)
			}
			return nil, e
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return nil, errors.New("inventory wasn't updated", &errors.DBError)
		}

		if batchNo != nil || serialNo != nil || expDate != nil {
			// Check if item_id, batch_number, serial_number, expiration_date exists in tracked inventory
			var tracked_item_exists bool
			query = `SELECT EXISTS(SELECT 1 FROM tracked_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL) AND warehouse_id = $5 AND client_id = $6) AS "exists"`
			err = tx.QueryRow(ctx, query, item.GetItemId(), batchNo, serialNo, expDate, req.GetWarehouseId(), req.GetClientId()).Scan(&tracked_item_exists)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "unable to check if item exists in tracked inventory", &errors.DBError)
			}

			if tracked_item_exists {
				// Increment it's quantity and available quantity
				query := `UPDATE tracked_inventory SET quantity = quantity + $1, available_qty = available_qty + $2, pickable_qty = pickable_qty + $3 WHERE item_id = $4 AND (batch_number = $5 OR $5 IS NULL) AND (serial_number = $6 OR $6 IS NULL) AND (expiration_date = $7 OR $7 IS NULL) AND warehouse_id = $8 AND client_id = $9`
				commandTag, err := tx.Exec(ctx, query, item.GetQuantity(), availableQty, pickableQty, item.GetItemId(), batchNo, serialNo, expDate, req.GetWarehouseId(), req.GetClientId())
				if err != nil {
					tx.Rollback(ctx)
					return nil, errors.Wrap(err, "error incrementing sku quantity on tracked inventory", &errors.DBError)
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return nil, errors.New("sku quantity wasn't incremented on the receiving location", &errors.DBError)
				}
			} else {
				query := `INSERT INTO tracked_inventory (warehouse_id, client_id, item_id, batch_number, serial_number, expiration_date, quantity, available_qty, pickable_qty) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`
				commandTag, err := tx.Exec(ctx, query, req.GetWarehouseId(), req.GetClientId(), item.GetItemId(), batchNo, serialNo, expDate, item.GetQuantity(), availableQty, pickableQty)
				if err != nil {
					tx.Rollback(ctx)
					return nil, errors.Wrap(err, "unable to insert new row in tracked inventory", &errors.DBError)
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return nil, errors.New("new row in wasn't inserted", &errors.DBError)
				}
			}
		}

		var document string
		if req.GetResource() == "return_approval" {
			document = "RMA"
		} else if req.GetResource() == "fulfill_reclamation" {
			document = "Reclamation"
		} else {
			document = "ARN"
		}

		transactionData := &model.TransactionData{
			ClientID:       req.GetClientId(),
			WarehouseID:    req.GetWarehouseId(),
			RequestID:      req.GetRequestId(),
			ItemID:         item.GetItemId(),
			SKU:            item.GetSku(),
			Action:         req.GetResource(),
			Change:         int(item.GetQuantity()),
			Username:       req.GetUsername(),
			UserID:         req.GetUserId(),
			ChangedAt:      time.Now().UTC(),
			BatchNumber:    batchNo,
			SerialNumber:   serialNo,
			ExpirationDate: expDate,
			Source:         req.GetSource(),
			Document:       document,
			DocumentCode:   req.GetArnCode(),
			ClientName:     req.GetClientName(),
			DocumentID:     req.GetArnId(),
			DocumentPrn:    req.GetDocumentPrn(),
			LocationID:     item.GetLocationId(),
			LocationCode:   item.GetLocationCode(),
			ContainerID:    containerID,
			ContainerCode:  containerCode,
			ContainerType:  containerType,
		}
		err = s.App.Operations.CreateTransaction(tx, transactionData)
		if err != nil {
			tx.Rollback(ctx)
			return nil, err
		}

		if container_id != uuid.Nil {

			// Put the item on container

			// Get container
			var container_client_id, item_id, ref_no *string
			query := `SELECT client_id, item_id, ref_no FROM container_wise_inventory WHERE container_id = $1 LIMIT 1`
			err := tx.QueryRow(ctx, query, container_id).Scan(&container_client_id, &item_id, &ref_no)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "failed to query for container", &errors.DBError)
			}

			if container_client_id == nil {
				// Insert item in container
				err = s.App.Operations.InsertItemInContainer(tx, ctx, container_id, item.GetLocationId(), req.GetWarehouseId(), int(item.GetQuantity()), int(item.GetQuantity()), inventory_item, batchNo, serialNo, expDate, nil)
				if err != nil {
					tx.Rollback(ctx)
					return nil, err
				}
			} else if *container_client_id != req.GetClientId() {
				if item_id != nil {
					tx.Rollback(ctx)
					return nil, errors.Wrap(err, "container is occupied by another client", &errors.PermissionDenied)
				}

				// Insert item in container
				err = s.App.Operations.InsertItemInContainer(tx, ctx, container_id, item.GetLocationId(), req.GetWarehouseId(), int(item.GetQuantity()), int(item.GetQuantity()), inventory_item, batchNo, serialNo, expDate, nil)
				if err != nil {
					tx.Rollback(ctx)
					return nil, err
				}

			} else {
				// Check if the item exists on the container
				var exists bool
				query := `SELECT EXISTS(SELECT 1 FROM container_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND container_id = $4 AND warehouse_id = $5 AND (expiration_date = $6 OR $6 IS NULL)) AS "exists"`
				err = tx.QueryRow(ctx, query, inventory_item.ItemID, batchNo, serialNo, container_id, req.GetWarehouseId(), expDate).Scan(&exists)
				if err != nil {
					tx.Rollback(ctx)
					return nil, errors.Wrap(err, "unable to check if item exists on container", &errors.DBError)
				}
				if exists {
					// Increment the quantity on container since the item is getting added to it
					query := `UPDATE container_wise_inventory SET quantity = quantity + $1, available_qty = available_qty + $2 WHERE item_id = $3 AND (batch_number = $4 OR $4 IS NULL) AND (serial_number = $5 OR $5 IS NULL) AND container_id = $6 AND warehouse_id = $7 AND (expiration_date = $8 OR $8 IS NULL)`
					commandTag, err := tx.Exec(ctx, query, item.GetQuantity(), item.GetQuantity(), inventory_item.ItemID, batchNo, serialNo, container_id, req.GetWarehouseId(), expDate)
					if err != nil {
						tx.Rollback(ctx)
						return nil, errors.Wrap(err, "error incrementing item quantity on container", &errors.DBError)
					}
					if commandTag.RowsAffected() != 1 {
						tx.Rollback(ctx)
						return nil, errors.New("item quantity wasn't incremented on the container", &errors.DBError)
					}
				} else {
					// Sku doesn't exist on the container
					err = s.App.Operations.InsertItemInContainer(tx, ctx, container_id, item.GetLocationId(), req.GetWarehouseId(), int(item.GetQuantity()), int(item.GetQuantity()), inventory_item, batchNo, serialNo, expDate, nil)
					if err != nil {
						tx.Rollback(ctx)
						return nil, err
					}
				}
			}

			// Update container ref
			if containerRef != nil {
				if ref_no != nil {
					// Check if any container exists where ref no is different
					var exists bool
					query = `SELECT EXISTS(SELECT 1 FROM container_wise_inventory WHERE ref_no != $1 AND container_id = $2) AS "exists"`
					err = tx.QueryRow(ctx, query, containerRef, container_id).Scan(&exists)
					if err != nil {
						tx.Rollback(ctx)
						return nil, errors.Wrap(err, "unable to check if any container exists where ref no is different", &errors.DBError)
					}
					if exists {
						// Override new ref no
						query := `UPDATE container_wise_inventory SET ref_no = $1 WHERE container_id = $2`
						commandTag, err := tx.Exec(ctx, query, containerRef, container_id)
						if err != nil {
							tx.Rollback(ctx)
							return nil, errors.Wrap(err, "error updating ref no in container", &errors.DBError)
						}
						if commandTag.RowsAffected() < 1 {
							tx.Rollback(ctx)
							return nil, errors.New("ref no was not updated in the container", &errors.DBError)
						}
					}
				} else {
					// Update ref_no in all rows with that container id
					query := `UPDATE container_wise_inventory SET ref_no = $1 WHERE container_id = $2`
					commandTag, err := tx.Exec(ctx, query, containerRef, container_id)
					if err != nil {
						tx.Rollback(ctx)
						return nil, errors.Wrap(err, "error updating ref no in container", &errors.DBError)
					}
					if commandTag.RowsAffected() < 1 {
						tx.Rollback(ctx)
						return nil, errors.New("ref no was not updated in the container", &errors.DBError)
					}
				}
			}
		} else {
			// Put the item on a loose container on the given location

			// Get loose container id
			type_id, err := s.App.Operations.GetContainerTypeID("loose", req.GetWarehouseId())
			if err != nil {
				tx.Rollback(ctx)
				return nil, err
			}

			// Get the loose container on that location
			var item_id *string
			var loose_container_id uuid.UUID
			query := `SELECT item_id, container_id FROM container_wise_inventory WHERE location_id = $1 AND container_type_id = $2 AND warehouse_id = $3 LIMIT 1`
			err = tx.QueryRow(ctx, query, item.GetLocationId(), *type_id, req.GetWarehouseId()).Scan(&item_id, &loose_container_id)
			// check for errors other than no rows
			if err != nil {
				if err == pgx.ErrNoRows {
					// loose container doesn't exist. Insert a new row and continue

					// Previous container code
					prevCodeInt, err := s.App.Container.GetPreviousContainerCodeInt(req.GetWarehouseId())
					if err != nil {
						return nil, err
					}
					code := "LP-" + strconv.Itoa(*prevCodeInt+1)

					query = `INSERT INTO container_wise_inventory (item_id, sku, batch_number, serial_number, expiration_date, scannable, container_id, container_type_id, location_id, warehouse_id, client_id, client_name, code, quantity, available_qty, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)`
					commandTag, err := tx.Exec(ctx, query, inventory_item.ItemID, inventory_item.SKU, batchNo, serialNo, expDate, inventory_item.Scannable, uuid.NewV4(), *type_id, item.GetLocationId(), req.GetWarehouseId(), req.GetClientId(), req.GetClientName(), code, item.GetQuantity(), item.GetQuantity(), time.Now())
					if err != nil {
						tx.Rollback(ctx)
						return nil, errors.Wrap(err, "unable to insert new loose container", &errors.DBError)
					}
					if commandTag.RowsAffected() != 1 {
						tx.Rollback(ctx)
						return nil, errors.New("new lose container wasn't inserted", &errors.DBError)
					}

					// Go to the next item
					continue

				} else {
					tx.Rollback(ctx)
					return nil, errors.Wrap(err, "failed to query for loose container on location", &errors.DBError)
				}
			}

			if item_id == nil {
				// Empty loose container exists. Update it's item details
				query = `UPDATE container_wise_inventory SET item_id = $1, sku = $2, batch_number = $3 AND serial_number = $4, scannable = $5, quantity = $6, available_qty = $7, last_updated_at = $8, expiration_date = $9, client_id = $10, client_name = $11 WHERE container_id = $12 AND location_id = $13 AND warehouse_id = $14`
				commandTag, err := tx.Exec(ctx, query, inventory_item.ItemID, inventory_item.SKU, batchNo, serialNo, inventory_item.Scannable, item.GetQuantity(), item.GetQuantity(), time.Now(), expDate, req.GetClientId(), req.GetClientName(), loose_container_id, item.GetLocationId(), req.GetWarehouseId())
				if err != nil {
					tx.Rollback(ctx)
					return nil, errors.Wrap(err, "unable to update item details in loose container", &errors.DBError)
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return nil, errors.New("item details didn't get updated on loose container", &errors.DBError)
				}
			} else {
				// Check if item exists on that loose container
				var exists bool
				query = `SELECT EXISTS(SELECT 1 FROM container_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND container_id = $4 AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)) AS "exists"`
				err = tx.QueryRow(ctx, query, inventory_item.ItemID, batchNo, serialNo, loose_container_id, item.GetLocationId(), req.GetWarehouseId(), expDate).Scan(&exists)
				if err != nil {
					tx.Rollback(ctx)
					return nil, errors.Wrap(err, "unable to check if item exists on loose container", &errors.DBError)
				}
				if exists {
					// Increment the quantity on container since the item is getting added to it
					query := `UPDATE container_wise_inventory SET quantity = quantity + $1, available_qty = available_qty + $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND container_id = $5 AND location_id = $6 AND warehouse_id = $7 AND (expiration_date = $8 OR $8 IS NULL)`
					commandTag, err := tx.Exec(ctx, query, item.GetQuantity(), inventory_item.ItemID, batchNo, serialNo, loose_container_id, item.GetLocationId(), req.GetWarehouseId(), expDate)
					if err != nil {
						tx.Rollback(ctx)
						return nil, errors.Wrap(err, "error incrementing item quantity on container", &errors.DBError)
					}
					if commandTag.RowsAffected() != 1 {
						tx.Rollback(ctx)
						return nil, errors.New("item quantity wasn't incremented on the container", &errors.DBError)
					}
				} else {
					// Sku doesn't exist on the container
					err = s.App.Operations.InsertItemInContainer(tx, ctx, loose_container_id, item.GetLocationId(), req.GetWarehouseId(), int(item.GetQuantity()), int(item.GetQuantity()), inventory_item, batchNo, serialNo, expDate, nil)
					if err != nil {
						tx.Rollback(ctx)
						return nil, err
					}
				}
			}
		}

		go s.App.Operations.UpdateShopifyInventory(item.GetItemId())

		// auto updating backorders
		backOrderAutoAlloationItems = append(backOrderAutoAlloationItems, item.GetItemId())

		if req.GetBgTaskId() != "" {
			// If i is a multiple of 5, update the background task with processed items
			if i%5 == 0 || i == len(req.GetItems())-1 {
				// Update background task
				_, err := s.App.GrpcClient.Core.Client.UpdateBgTaskStatus(ctx, &core_proto.UpdateBgTaskStatusRequest{
					TaskId:            req.GetBgTaskId(),
					ProcessedElements: int32(i + 1),
				})
				if err != nil {
					sentry.CaptureException(errors.Wrap(err, "Failed to update background task", &errors.DBError))
				}
			}
		}

	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back", &errors.DBError)
	}

	// auto updating backorders
	if len(backOrderAutoAlloationItems) > 0 {
		go s.App.Operations.BackOrderAutoAlloation(backOrderAutoAlloationItems, nil, model.ARNReceivedTrigger, true)
	}

	return &inventory_proto.UpdateArnItemsResponse{
		Success: true,
	}, nil
}

func (s *Grpc) UpdateLocationForPickUp(ctx context.Context, req *inventory_proto.UpdateLocationForPickUpRequest) (*inventory_proto.UpdateLocationForPickUpResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	var batchNo *string
	if req.GetBatchNumber() == "" {
		batchNo = nil
	} else {
		v := req.GetBatchNumber()
		batchNo = &v
	}

	var serialNo *string
	if req.GetSerialNumber() == "" {
		serialNo = nil
	} else {
		v := req.GetSerialNumber()
		serialNo = &v
	}

	var expDate *time.Time
	if req.GetExpirationDate() == nil {
		expDate = nil
	} else {
		v := req.GetExpirationDate().AsTime()
		expDate = &v
	}

	itemID := req.GetItemId()
	// Get item from inventory
	inventory_item, err := s.App.Operations.GetItemFromInventory(itemID, batchNo, serialNo, expDate)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to get item from inventory "+req.GetSku(), &errors.DBError)
	}
	if inventory_item.OnHold {
		tx.Rollback(ctx)
		return nil, errors.New("The item "+inventory_item.Name+" is on hold", &errors.DBError)
	}

	quantity := req.GetQuantity()
	// Decrement picked quantity from container
	container_id := uuid.FromStringOrNil(req.GetContainerId())

	var containerCode *string
	var containerID *uuid.UUID
	containerType := "loose"
	if container_id != uuid.Nil {
		v := container_id
		containerID = &v

		// Get container type from id
		query := `SELECT ct.name FROM container_wise_inventory c INNER JOIN container_type ct ON ct.id = c.container_type_id WHERE c.container_id = $1`
		err = tx.QueryRow(ctx, query, container_id).Scan(&containerType)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "unable to get the container type", &errors.DBError)
		}
	}
	if req.GetContainerCode() != "" {
		v := req.GetContainerCode()
		containerCode = &v
	}

	if container_id != uuid.Nil {
		// Get existing quantity on container
		var existing_qty_on_container int
		var container_location string
		query := `SELECT quantity, location_id FROM container_wise_inventory WHERE container_id = $1 AND item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL)`
		err := tx.QueryRow(ctx, query, container_id, itemID, batchNo, serialNo, expDate).Scan(&existing_qty_on_container, &container_location)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "failed to fetch quantity from container", &errors.DBError)
		}

		if container_location != req.GetLocationId() {
			tx.Rollback(ctx)
			sentry.CaptureException(errors.New("The location in request does not match with location in container", &errors.DBError))
			return nil, errors.New("The location in request does not match with location in container", &errors.DBError)
		}

		if int32(existing_qty_on_container)-quantity < 0 {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "enough quantity doesn't exist on the selected container", &errors.DBError)

		} else if int32(existing_qty_on_container)-quantity == 0 {
			// remove item from container
			err := s.App.Operations.RemoveItemFromContainer(tx, ctx, container_id, itemID, req.GetLocationId(), req.GetWarehouseId(), batchNo, serialNo, expDate)
			if err != nil {
				tx.Rollback(ctx)
				return nil, err
			}
		} else {
			query := `UPDATE container_wise_inventory SET quantity = quantity - $1, available_qty = available_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL) AND container_id = $6`
			commandTag, err := tx.Exec(ctx, query, quantity, itemID, batchNo, serialNo, expDate, container_id)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "error decrementing item quantity on container", &errors.DBError)
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return nil, errors.New("quantity wasn't decremented on the container", &errors.DBError)
			}
		}
	}

	// Decrement picked quantity on location

	// Get existing quantity on the source location
	var existing_qty_on_location, existing_available_qty_on_location int
	query := `SELECT quantity, available_qty FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL) AND location_id = $5`
	err = tx.QueryRow(ctx, query, itemID, batchNo, serialNo, expDate, req.GetLocationId()).Scan(&existing_qty_on_location, &existing_available_qty_on_location)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "couldn't get quantity of item on location", &errors.DBError)
	}

	// check if picking method was path optimized
	if req.GetPickingMethod() == "path_optimized" {
		if existing_qty_on_location-int(quantity) < 0 {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "enough quantity doesn't exist on the selected location", &errors.DBError)

		} else if existing_qty_on_location-int(quantity) == 0 {
			// remove row or set null in case of last row from location
			err := s.App.Operations.RemoveItemFromLocation(tx, ctx, itemID, req.GetLocationId(), req.GetWarehouseId(), batchNo, serialNo, expDate)
			if err != nil {
				tx.Rollback(ctx)
				return nil, err
			}

		} else {
			// Decrement quantity
			query := `UPDATE location_wise_inventory SET quantity = quantity - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL) AND location_id = $6`
			commandTag, err := tx.Exec(ctx, query, quantity, itemID, batchNo, serialNo, expDate, req.GetLocationId())
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "error decrementing item quantity on location", &errors.DBError)
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return nil, errors.New("quantity wasn't decremented on the location", &errors.DBError)
			}
		}
	} else {
		if existing_available_qty_on_location-int(quantity) < 0 {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "enough quantity doesn't exist on location - Path optimized picking is active.", &errors.DBError)

		} else if existing_qty_on_location-int(quantity) == 0 {
			// remove row or set null in case of last row from location
			err := s.App.Operations.RemoveItemFromLocation(tx, ctx, itemID, req.GetLocationId(), req.GetWarehouseId(), batchNo, serialNo, expDate)
			if err != nil {
				tx.Rollback(ctx)
				return nil, err
			}

		} else {
			// Decrement quantity
			query := `UPDATE location_wise_inventory SET quantity = quantity - $1, available_qty = available_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL) AND location_id = $6`
			commandTag, err := tx.Exec(ctx, query, quantity, itemID, batchNo, serialNo, expDate, req.GetLocationId())
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "error decrementing item quantity on location", &errors.DBError)
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return nil, errors.New("quantity wasn't decremented on the location", &errors.DBError)
			}
		}
	}

	// Decrement picked quantity from inventory
	query = `UPDATE inventory SET quantity = quantity - $1, pickable_qty = pickable_qty - $1 WHERE item_id = $2` // [TAP] - Pickable quantity is decremented at picking
	commandTag, err := tx.Exec(ctx, query, quantity, itemID)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "unable to update inventory", &errors.DBError)
	}
	if commandTag.RowsAffected() != 1 {
		tx.Rollback(ctx)
		return nil, errors.New("inventory wasn't updated", &errors.DBError)
	}

	if batchNo != nil || serialNo != nil || expDate != nil {
		// any tracked parameter exists
		query := `UPDATE tracked_inventory SET quantity = quantity - $1, available_qty = available_qty - $1, pickable_qty = pickable_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL)` // [TAP] - Pickable quantity is decremented at picking
		commandTag, err := tx.Exec(ctx, query, quantity, itemID, batchNo, serialNo, expDate)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "unable to update tracked inventory", &errors.DBError)
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return nil, errors.New("tracked inventory wasn't updated", &errors.DBError)
		}
	}

	if len(req.GetToteDistribution()) > 0 {
		var cartCode *string
		var cartID *uuid.UUID
		if req.GetCartCode() != "" {
			v := req.GetCartCode()
			cartCode = &v
		}
		if req.GetCartId() != "" {
			v := uuid.FromStringOrNil(req.GetCartId())
			cartID = &v
		}
		qtyToAddInCurrentQty := req.GetQuantity()
		for _, data := range req.GetToteDistribution() {
			qty := data.GetQuantity()
			qtyToAddInCurrentQty -= qty
			var ToteCode *string
			var toteID *uuid.UUID
			if data.GetToteCode() != "" {
				v := data.GetToteCode()
				ToteCode = &v
			}
			if data.GetToteId() != "" {
				v := uuid.FromStringOrNil(data.GetToteId())
				toteID = &v
			} else {
				toteID = nil
			}

			if req.GetCartId() != "" && req.GetPicklistId() != "" {
				err := s.App.Cart.UpdateCartAndToteItems(tx, ctx, qty, req, inventory_item, data.GetOrderId(), data.GetOrderCode(), cartID, toteID, cartCode, ToteCode, batchNo, serialNo, expDate)
				if err != nil {
					tx.Rollback(ctx)
					return nil, err
				}
			}

			// Add to transaction
			transactionData := &model.TransactionData{
				ClientID:             req.GetClientId(),
				ClientName:           req.GetClientName(),
				WarehouseID:          req.GetWarehouseId(),
				RequestID:            req.GetRequestId(),
				ItemID:               req.GetItemId(),
				SKU:                  req.GetSku(),
				Action:               "item_pickup",
				Change:               int(0 - qty),
				Username:             req.GetUsername(),
				UserID:               req.GetUserId(),
				ChangedAt:            time.Now().UTC(),
				BatchNumber:          batchNo,
				SerialNumber:         serialNo,
				ExpirationDate:       expDate,
				Source:               req.GetSource(),
				Document:             req.GetDocument(),
				DocumentCode:         data.GetOrderCode(),
				DocumentID:           data.GetOrderId(),
				DocumentPrn:          data.GetOrderPrn(),
				LocationID:           req.GetLocationId(),
				LocationCode:         req.GetLocationCode(),
				ContainerID:          containerID,
				ContainerCode:        containerCode,
				ContainerType:        containerType,
				CartID:               cartID,
				CartCode:             cartCode,
				ToteID:               toteID,
				ToteCode:             ToteCode,
				QtyToAddInCurrentQty: &qtyToAddInCurrentQty,
			}
			err = s.App.Operations.CreateTransaction(tx, transactionData)
			if err != nil {
				tx.Rollback(ctx)
				return nil, err
			}
		}
	} else {
		// Add to transaction
		transactionData := &model.TransactionData{
			ClientID:       req.GetClientId(),
			WarehouseID:    req.GetWarehouseId(),
			RequestID:      req.GetRequestId(),
			ItemID:         req.GetItemId(),
			SKU:            req.GetSku(),
			Action:         "item_pickup",
			Change:         int(0 - quantity),
			Username:       req.GetUsername(),
			UserID:         req.GetUserId(),
			ChangedAt:      time.Now().UTC(),
			BatchNumber:    batchNo,
			SerialNumber:   serialNo,
			ExpirationDate: expDate,
			Source:         req.GetSource(),
			Document:       req.GetDocument(),
			DocumentCode:   req.GetDocumentCode(),
			ClientName:     req.GetClientName(),
			DocumentID:     req.GetDocumentId(),
			DocumentPrn:    req.GetDocumentPrn(),
			LocationID:     req.GetLocationId(),
			LocationCode:   req.GetLocationCode(),
			ContainerID:    containerID,
			ContainerCode:  containerCode,
			ContainerType:  containerType,
		}
		err = s.App.Operations.CreateTransaction(tx, transactionData)
		if err != nil {
			tx.Rollback(ctx)
			return nil, err
		}
	}

	if req.Document == "Work Order" && len(req.GetWorkOrder()) > 0 {
		// updating work order
		updateResp, err := s.App.GrpcClient.Core.Client.UpdateWorkOrder(ctx, &core_proto.UpdateWorkOrderRequest{WorkOrder: req.GetWorkOrder()})
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Unable to request for update work order", &errors.DBError)
		}
		if !updateResp.GetSuccess() {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Unable to update work order", &errors.DBError)
		}
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back", &errors.DBError)
	}

	// Update Ageing inventory

	var auditAgeDataRequest reporting_proto.AuditInventoryUpdateRequest

	updateQty := 0 - quantity

	// Add to ageing audit data
	auditItem := reporting_proto.AuditItem{
		ItemId:     itemID,
		LocationId: req.GetLocationId(),
		Quantity:   updateQty,
	}
	if containerType != "loose" {
		auditItem.ContainerId = container_id.String()
	}
	if batchNo != nil {
		auditItem.BatchNumber = *batchNo
	}
	if serialNo != nil {
		auditItem.SerialNumber = *serialNo
	}
	if expDate != nil {
		auditItem.ExpirationDate = expDate.Format("2006-01-02")
	}
	auditAgeDataRequest.AuditItems = append(auditAgeDataRequest.AuditItems, &auditItem)

	go func(auditAgeDataRequest *reporting_proto.AuditInventoryUpdateRequest) {
		res, err := s.App.GrpcClient.Reporting.Client.AuditInventoryUpdate(context.Background(), auditAgeDataRequest)
		if err != nil {
			sentry.CaptureException(errors.Wrap(err, "Pickup item: failed to update age data in reporting", &errors.DBError))
		}
		if !res.GetSuccess() {
			sentry.CaptureException(errors.Wrap(err, "Pickup item: failed to update age data in reporting", &errors.DBError))
		}
	}(&auditAgeDataRequest)

	return &inventory_proto.UpdateLocationForPickUpResponse{
		Success: true,
	}, nil
}

// # Get Item Inventory Info
func (s *Grpc) GetItemInventoryInfo(ctx context.Context, req *inventory_proto.GetItemInventoryInfoRequest) (*inventory_proto.GetItemInventoryInfoResponse, error) {
	context := context.TODO()

	var err error

	var query string
	var total_qty, available_qty, pickable_qty int32
	var base_unit, arn_id string

	var itemID string = req.GetItemId()
	var action string = "arn_fulfillment"

	// # Get 'Item Inventory Info' from 'inventory'
	query = `SELECT
				i.quantity AS total_qty,
				i.available_qty,
				i.pickable_qty,
				i.base_unit
			FROM
				inventory i
			WHERE
				i.item_id = $1`

	err = s.DB.QueryRow(context, query, itemID).Scan(
		&total_qty,
		&available_qty,
		&pickable_qty,
		&base_unit,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.New("no rows found for item inventory info", &errors.DBError)
		}
		return nil, errors.Wrap(err, "failed to query inventory for item inventory info", &errors.DBError)
	}

	// # Get 'ARN ID' from 'transaction'
	query = `SELECT
				t.document_id AS arn_id
			FROM
				"transaction" t 
			WHERE
				t.item_id = $1
				AND t."action" = $2
			ORDER BY
				t."id" DESC`

	_ = s.DB.QueryRow(context, query, itemID, action).Scan(&arn_id)

	response := inventory_proto.GetItemInventoryInfoResponse{
		Quantity:     total_qty,
		AvailableQty: available_qty,
		PickableQty:  pickable_qty,
		BaseUnit:     base_unit,
		ArnId:        arn_id,
	}

	// # Return the response
	return &response, nil
}

func (s *Grpc) GetItemsInventoryInfo(ctx context.Context, req *inventory_proto.GetItemsInventoryInfoRequest) (*inventory_proto.GetItemsInventoryInfoResponse, error) {
	var itemInfo []*inventory_proto.GetItemsInventoryInfoResponse_ItemInfo

	query := `SELECT item_id, available_qty FROM inventory WHERE item_id = ANY($1)`
	rows, err := s.DB.Query(ctx, query, req.GetItemId())
	if err != nil {
		return nil, errors.New("failed to query for inventory items", &errors.DBError)
	}
	for rows.Next() {
		var item model.Inventory
		err := rows.Scan(
			&item.ItemID,
			&item.AvailableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into inventory schema", &errors.DBError)
		}
		itemInfo = append(itemInfo, &inventory_proto.GetItemsInventoryInfoResponse_ItemInfo{
			ItemId:   item.ItemID,
			Quantity: item.AvailableQty,
		})

	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	return &inventory_proto.GetItemsInventoryInfoResponse{
		ItemInfo: itemInfo,
	}, nil
}

func (s *Grpc) DeleteClient(ctx context.Context, req *inventory_proto.DeleteClientRequest) (*inventory_proto.DeleteClientResponse, error) {
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	query := `UPDATE inventory SET is_client_deleted = $1 WHERE warehouse_id = $2 AND client_id = $3`
	commandTag, err := tx.Exec(ctx, query, req.GetIsDeleted(), req.GetWarehouseId(), req.GetClientId())
	if err != nil {
		return nil, errors.Wrap(err, "failed to delete client", &errors.DBError)
	}
	if commandTag.RowsAffected() == 0 {
		return nil, errors.New("no rows were affected in inventory", &errors.DBError)
	}

	// Delete from containers

	// {container_id: count}
	container_count_map := make(map[uuid.UUID]int)

	query = `SELECT container_id, COUNT(container_id) FROM container_wise_inventory WHERE client_id = $1 GROUP BY container_id`
	rows, err := tx.Query(ctx, query, req.GetClientId())
	if err != nil {
		return nil, errors.Wrap(err, "failed to query container_wise_inventory", &errors.DBError)
	}
	defer rows.Close()
	for rows.Next() {
		var container_id uuid.UUID
		var count int
		err := rows.Scan(&container_id, &count)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into container var", &errors.DBError)
		}
		container_count_map[container_id] = count
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	for container_id, count := range container_count_map {
		if count > 1 {
			// delete count - 1 rows of each container
			query := `DELETE FROM container_wise_inventory WHERE id = ANY (ARRAY (SELECT id from container_wise_inventory WHERE container_id = $1 LIMIT $2))`
			commandTag, err := tx.Exec(ctx, query, container_id, count-1)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "unable to delete container rows", &errors.DBError)
			}
			if commandTag.RowsAffected() == 0 {
				tx.Rollback(ctx)
				return nil, errors.New("container rows weren't deleted", &errors.DBError)
			}
		}
	}

	// Empty the single remaining row of each container
	for container_id := range container_count_map {
		// Set sku details to null and Update quantity to 0
		query = `UPDATE container_wise_inventory SET item_id = $1, batch_number = $2, serial_number = $3, sku = $4, scannable = $5, quantity = $6, available_qty = $7, expiration_date = $8, client_id = $9, client_name = $10 WHERE container_id = $11`
		commandTag, err := tx.Exec(ctx, query, nil, nil, nil, nil, nil, 0, 0, nil, nil, nil, container_id)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "unable to empty container", &errors.DBError)
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return nil, errors.New("row containing last container wasn't updated", &errors.DBError)
		}
	}

	// Delete locations with client id
	// {location_id: count}
	location_count_map := make(map[string]int)

	query = `SELECT location_id, COUNT(location_id) FROM location_wise_inventory WHERE client_id = $1 GROUP BY location_id`
	rows, err = tx.Query(ctx, query, req.GetClientId())
	if err != nil {
		return nil, errors.Wrap(err, "failed to query location_wise_inventory", &errors.DBError)
	}
	defer rows.Close()
	for rows.Next() {
		var location_id string
		var count int
		err := rows.Scan(&location_id, &count)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into location var", &errors.DBError)
		}
		location_count_map[location_id] = count
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var locations_to_be_emptied []string
	for location_id, client_location_count := range location_count_map {

		// Get count of rows containing location_id and client_id
		var location_count int
		query := `SELECT COUNT(location_id) FROM location_wise_inventory WHERE location_id = $1`
		err := tx.QueryRow(ctx, query, location_id).Scan(&location_count)
		if err != nil {
			return nil, errors.Wrap(err, "failed to query count from location_wise_inventory", &errors.DBError)
		}

		if location_count > client_location_count {
			// Location is used by other clients too. Delete rows matching this particular client
			query := `DELETE FROM location_wise_inventory WHERE location_id = $1 AND client_id = $2`
			commandTag, err := tx.Exec(ctx, query, location_id, req.GetClientId())
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "unable to delete location rows", &errors.DBError)
			}
			if commandTag.RowsAffected() == 0 {
				tx.Rollback(ctx)
				return nil, errors.New("location rows weren't deleted", &errors.DBError)
			}
		} else if location_count == client_location_count {
			// Location is used only by this client. Delete all rows and keep last location row empty
			locations_to_be_emptied = append(locations_to_be_emptied, location_id)
			// delete count - 1 rows of each location
			query := `DELETE FROM location_wise_inventory WHERE id = ANY (ARRAY (SELECT id from location_wise_inventory WHERE location_id = $1 LIMIT $2))`
			commandTag, err := tx.Exec(ctx, query, location_id, location_count-1)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "unable to delete location rows", &errors.DBError)
			}
			if commandTag.RowsAffected() == 0 {
				tx.Rollback(ctx)
				return nil, errors.New("location rows weren't deleted", &errors.DBError)
			}

		} else {
			return nil, errors.Wrap(err, "something is wrong, descrepency found in location count", &errors.DBError)
		}
	}

	for _, location_id := range locations_to_be_emptied {
		// Set all details to null and Update quantity to 0
		query = `UPDATE location_wise_inventory SET item_id = $1, sku = $2, batch_number = $3, serial_number = $4, expiration_date = $5, scannable = $6, quantity = $7, available_qty = $8, client_id = $9, client_name = $10 WHERE location_id = $11`
		commandTag, err := tx.Exec(ctx, query, nil, nil, nil, nil, nil, nil, 0, 0, nil, nil, location_id)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "unable to empty location", &errors.DBError)
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return nil, errors.New("row containing last location wasn't updated", &errors.DBError)
		}
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back", &errors.DBError)
	}

	return &inventory_proto.DeleteClientResponse{
		Success: true,
	}, nil
}

func (s *Grpc) InsertLocations(ctx context.Context, req *inventory_proto.InsertLocationsRequest) (*inventory_proto.InsertLocationsResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to begin transaction", &errors.DBError)
	}

	defer tx.Rollback(ctx)

	var rows [][]interface{}
	for _, item := range req.Locations {
		rows = append(rows, []interface{}{item.Code, item.LocationId, item.WarehouseId, item.AreaName, item.AreaId, 0, 0, time.Now(), item.IsPrime})
	}

	// Bulk insert rows
	copyCount, err := s.DB.CopyFrom(
		ctx,
		// Table name
		pgx.Identifier{"location_wise_inventory"},
		// Columns
		[]string{"code", "location_id", "warehouse_id", "area_name", "area_id", "quantity", "available_qty", "created_at", "is_prime"},
		// Rows to insert
		pgx.CopyFromRows(rows),
	)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "unable to bulk insert location_wise_inventory", &errors.DBError)
	}
	if copyCount != int64(len(rows)) {
		tx.Rollback(ctx)
		return nil, errors.New("failed to insert some rows in the location_wise_inventory table", &errors.DBError)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back", &errors.DBError)
	}

	return &inventory_proto.InsertLocationsResponse{
		Success: true,
	}, nil
}

func (s *Grpc) GetReorderCount(ctx context.Context, req *inventory_proto.GetReorderCountRequest) (*inventory_proto.GetReorderCountResponse, error) {
	var inventory_items []*model.Inventory

	item_stock_map := make(map[string]int32)
	item_reorder_map := make(map[string]int32)
	var item_ids []string
	for _, item := range req.GetItems() {
		item_ids = append(item_ids, item.GetItemId())
		item_stock_map[item.GetItemId()] = item.GetMinStockLevel()
		item_reorder_map[item.GetItemId()] = item.GetReorderQty()
	}
	query := `SELECT item_id, client_name, sku, quantity, available_qty, pickable_qty, name, base_unit, is_kit FROM inventory WHERE item_id = ANY($1)`
	rows, err := s.DB.Query(ctx, query, item_ids)
	if err != nil {
		return nil, errors.New("failed to query for inventory items", &errors.DBError)
	}
	for rows.Next() {
		var item model.Inventory
		err := rows.Scan(
			&item.ItemID,
			&item.ClientName,
			&item.SKU,
			&item.Quantity,
			&item.AvailableQty,
			&item.PickableQty,
			&item.Name,
			&item.BaseUnit,
			&item.IsKit,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into inventory schema", &errors.DBError)
		}

		if item.Quantity < item_stock_map[item.ItemID] {
			inventory_items = append(inventory_items, &item)
		}
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var response inventory_proto.GetReorderCountResponse
	for _, v := range inventory_items {
		qty := strconv.FormatInt(int64(v.Quantity), 10)
		av_qty := strconv.FormatInt(int64(v.AvailableQty), 10)
		pick_qty := strconv.FormatInt(int64(v.PickableQty), 10)
		min_stock := strconv.FormatInt(int64(item_stock_map[v.ItemID]), 10)
		reorder := strconv.FormatInt(int64(item_reorder_map[v.ItemID]), 10)

		replenishmentObj := inventory_proto.ReplenishmentReport{
			ClientName:    *v.ClientName,
			Sku:           v.SKU,
			Name:          v.Name,
			Quantity:      qty,
			AvailableQty:  av_qty,
			PickableQty:   pick_qty,
			MinStockLevel: min_stock,
			ReorderQty:    reorder,
			BaseUnit:      v.BaseUnit,
			IsKit:         strconv.FormatBool(v.IsKit),
			ItemId:        v.ItemID,
		}
		response.ReplenishmentReport = append(response.ReplenishmentReport, &replenishmentObj)
	}

	return &response, nil
}

// # Item On Hold Report
func (s *Grpc) ItemOnHoldReport(ctx context.Context, req *inventory_proto.ItemOnHoldReportRequest) (*inventory_proto.ItemOnHoldReportResponse, error) {
	var err error

	var rows pgx.Rows
	var query string

	var pageNo, pageSize *int
	var offset *int64

	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}

	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_

	if all {
		pageSize = nil
		offset = nil
	}

	// # Get On Hold Items From Inventory
	var on_hold bool = true

	if len(req.GetClients()) > 0 {
		query = `SELECT
					client_name,
					sku,
					scannable,
					"name",
					quantity,
					available_qty,
					pickable_qty
				FROM
					inventory
				WHERE
					warehouse_id = $1
					AND on_hold = $2
					AND client_id = ANY ($3)
				ORDER BY
					id DESC
				LIMIT
					$4
				OFFSET
					$5`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), on_hold, req.GetClients(), pageSize, offset)
	} else {
		query = `SELECT
					client_name,
					sku,
					scannable,
					"name",
					quantity,
					available_qty,
					pickable_qty
				FROM
					inventory
				WHERE
					warehouse_id = $1
					AND on_hold = $2
				ORDER BY
					id DESC
				LIMIT
					$3
				OFFSET
					$4`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), on_hold, pageSize, offset)
	}
	if err != nil {
		return nil, errors.Wrap(err, "failed to query inventory for on hold items", &errors.DBError)
	}

	defer rows.Close()

	var ItemOnHoldReport []*inventory_proto.ItemOnHoldReport

	for rows.Next() {
		var inventory model.Inventory
		var inventoryProto inventory_proto.ItemOnHoldReport
		err = rows.Scan(
			&inventory.ClientName,
			&inventory.SKU,
			&inventory.Scannable,
			&inventory.Name,
			&inventory.Quantity,
			&inventory.AvailableQty,
			&inventory.PickableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into inventory schema", &errors.DBError)
		}

		if inventory.ClientName != nil {
			inventoryProto.ClientName = *inventory.ClientName
		}

		inventoryProto.Sku = inventory.SKU
		inventoryProto.Scannable = inventory.Scannable
		inventoryProto.Name = inventory.Name
		inventoryProto.Quantity = inventory.Quantity
		inventoryProto.AvailableQty = inventory.AvailableQty
		inventoryProto.PickableQty = inventory.PickableQty

		ItemOnHoldReport = append(ItemOnHoldReport, &inventoryProto)
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var resultCount, totalPages int

	if len(req.GetClients()) > 0 {
		query = `SELECT COUNT(id) FROM inventory WHERE warehouse_id = $1 AND on_hold = $2 AND client_id = ANY($3)`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), on_hold, req.GetClients()).Scan(&resultCount)
	} else {
		query = `SELECT COUNT(id) FROM inventory WHERE warehouse_id = $1 AND on_hold = $2`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), on_hold).Scan(&resultCount)
	}
	if err != nil {
		return nil, errors.Wrap(err, "failed to get count of inventory rows", &errors.DBError)
	}

	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	response := &inventory_proto.ItemOnHoldReportResponse{
		TotalDocuments: int32(resultCount),
		TotalPages:     int32(totalPages),
		CurrentPage:    int32(*pageNo),
		Report:         ItemOnHoldReport,
	}

	return response, nil
}

// # Empty Location Report -> stream the report 'data' and 'metadata'
func (s *Grpc) EmptyLocationReport(req *inventory_proto.EmptyLocationReportRequest, stream inventory_proto.Inventory_EmptyLocationReportServer) error {
	ctx := stream.Context() // # Get the 'stream' context
	var pageNo, pageSize *int
	var offset *int64
	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	// Get Utilized space
	var total_count, utilized_count int
	query := `SELECT COUNT(DISTINCT location_id) FROM location_wise_inventory WHERE warehouse_id = $1`
	err := s.DB.QueryRow(ctx, query, req.GetWarehouseId()).Scan(&total_count)
	if err != nil {
		return errors.Wrap(err, "unable to get count of location rows", &errors.DBError)
	}

	query = `SELECT COUNT(DISTINCT location_id) FROM location_wise_inventory WHERE warehouse_id = $1 AND quantity > $2`
	err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), 0).Scan(&utilized_count)
	if err != nil {
		return errors.Wrap(err, "unable to get count of location rows", &errors.DBError)
	}

	spaceUtilization := (float32(utilized_count) / float32(total_count)) * 100

	// Query for empty locations
	query = `SELECT warehouse_id, location_id, code, area_name, area_id, on_hold FROM location_wise_inventory WHERE warehouse_id = $1 AND quantity = $2 ORDER BY location_id LIMIT $3 OFFSET $4`
	rows, err := s.DB.Query(context.TODO(), query, req.GetWarehouseId(), 0 /* quantity */, pageSize, offset)
	if err != nil {
		return errors.Wrap(err, "failed to query for empty locations", &errors.DBError)
	}

	defer rows.Close()

	// # Get the 'percentage report' flag from the 'request'
	var isPercentageReport bool = req.GetPercentageReport()

	if !isPercentageReport {
		// # gRPC Server Streaming Optimization #
		// # Chunk Size
		const chunkSize = 10000

		// # Chunk List
		var chunk []*inventory_proto.EmptyLocationReportDataResponse

		// # Chunk Counter
		var chunkCounter int = 0

		for rows.Next() {
			var location inventory_proto.EmptyLocationReportDataResponse
			err := rows.Scan(
				&location.WarehouseId,
				&location.LocationId,
				&location.Code,
				&location.AreaName,
				&location.AreaId,
				&location.OnHold,
			)
			if err != nil {
				return errors.Wrap(err, "unable to read resultant location rows into location schema", &errors.DBError)
			}
			location.LocationQr = fmt.Sprintf("l/%s/%s", location.LocationId, location.Code)

			// # Append the 'row' into the 'chunk' list
			chunk = append(chunk, &location)

			// # Increment the 'chunk' counter
			chunkCounter++

			// # Send the report 'chunk' to the 'stream'
			// # Stream the 'chunk' if the chunk 'size' is reached
			if chunkCounter == chunkSize {
				err = stream.Send(&inventory_proto.EmptyLocationReportStreamResponse{
					Response: &inventory_proto.EmptyLocationReportStreamResponse_Chunk{
						Chunk: &inventory_proto.EmptyLocationReportDataResponseList{
							Data: chunk,
						},
					},
				})
				if err != nil {
					return errors.Wrap(err, "failed to stream the report chunk", &errors.DBError)
				}

				// # Clear the 'chunk' list
				chunk = nil

				// # Reset the 'chunk' counter
				chunkCounter = 0
			}
		}

		// # Stream the remaining report 'data' if any 'left' in the 'chunk' list
		if chunkCounter > 0 {
			err = stream.Send(&inventory_proto.EmptyLocationReportStreamResponse{
				Response: &inventory_proto.EmptyLocationReportStreamResponse_Chunk{
					Chunk: &inventory_proto.EmptyLocationReportDataResponseList{
						Data: chunk,
					},
				},
			})
			if err != nil {
				return errors.Wrap(err, "failed to stream the remaining report chunk", &errors.DBError)
			}

			// # Clear the 'chunk' list
			chunk = nil

			// # Reset the 'chunk' counter
			chunkCounter = 0
		}

		if row_err := rows.Err(); row_err != nil {
			return errors.Wrap(row_err, "unable to read empty location rows", &errors.DBError)
		}
	}

	var resultCount, totalPages int
	query = `SELECT COUNT(id) FROM location_wise_inventory WHERE warehouse_id = $1 AND quantity = $2`
	err = s.DB.QueryRow(context.TODO(), query, req.GetWarehouseId(), 0).Scan(&resultCount)
	if err != nil {
		return errors.Wrap(err, "unable to get count of location wise inventory rows", &errors.DBError)
	}
	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	var metadata inventory_proto.EmptyLocationReportMetaDataResponse

	if isPercentageReport {
		metadata = inventory_proto.EmptyLocationReportMetaDataResponse{
			SpaceUtilization: int32(spaceUtilization),
		}
	} else {
		metadata = inventory_proto.EmptyLocationReportMetaDataResponse{
			SpaceUtilization: int32(spaceUtilization),
			TotalDocuments:   int32(resultCount),
			TotalPages:       int32(totalPages),
			CurrentPage:      int32(*pageNo),
		}
	}

	// # Send the report 'metadata' to the 'stream'
	err = stream.Send(&inventory_proto.EmptyLocationReportStreamResponse{
		Response: &inventory_proto.EmptyLocationReportStreamResponse_Metadata{
			Metadata: &metadata,
		},
	})
	if err != nil {
		return errors.Wrap(err, "failed to stream the report metadata", &errors.DBError)
	}

	return nil
}

func (s *Grpc) CardexReport(ctx context.Context, req *inventory_proto.CardexReportRequest) (*inventory_proto.CardexReportResponse, error) {
	var cardexResponse []*inventory_proto.Transaction
	var pageNo, pageSize *int
	var offset *int64
	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	fromTime, err := time.Parse("2006-01-02 15:04:05", req.GetFromTime())
	if err != nil {
		return nil, errors.Wrap(err, "Invalid from time", &errors.DBError)
	}

	toTime, err := time.Parse("2006-01-02 15:04:05", req.GetToTime())
	if err != nil {
		return nil, errors.Wrap(err, "Invalid to time", &errors.DBError)
	}

	query := `SELECT * FROM transaction WHERE item_id = $1 AND (changed_at BETWEEN $2 AND $3) ORDER BY id DESC LIMIT $4 OFFSET $5`
	rows, err := s.DB.Query(context.TODO(), query, req.GetItemId(), fromTime, toTime, pageSize, offset)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query transactions", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var transaction model.Transaction
		var transactionProto inventory_proto.Transaction
		err := rows.Scan(
			&transaction.ID,
			&transaction.ClientID,
			&transaction.WarehouseID,
			&transaction.RequestID,
			&transaction.ItemID,
			&transaction.SKU,
			&transaction.Action,
			&transaction.Change,
			&transaction.Current,
			&transaction.ChangedByUsername,
			&transaction.ChangedByID,
			&transaction.ChangedAt,
			&transaction.BatchNumber,
			&transaction.SerialNumber,
			&transaction.ExpirationDate,
			&transaction.Source,
			&transaction.Document,
			&transaction.DocumentCode,
			&transaction.ClientName,
			&transaction.DocumentID,
			&transaction.Remark,
			&transaction.DocumentPrn,
			&transaction.LocationID,
			&transaction.LocationCode,
			&transaction.ContainerID,
			&transaction.ContainerCode,
			&transaction.ContainerType,
			&transaction.CartID,
			&transaction.CartCode,
			&transaction.ToteID,
			&transaction.ToteCode,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into transaction schema", &errors.DBError)
		}

		transactionProto.Id = transaction.ID
		transactionProto.ClientId = transaction.ClientID
		transactionProto.WarehouseId = transaction.WarehouseID
		transactionProto.RequestId = transaction.RequestID
		transactionProto.ItemId = transaction.ItemID
		transactionProto.Sku = transaction.SKU
		transactionProto.Action = transaction.Action
		transactionProto.Change = int32(transaction.Change)
		transactionProto.Current = int32(transaction.Current)
		transactionProto.ChangedByUsername = transaction.ChangedByUsername
		transactionProto.ChangedById = transaction.ChangedByID
		transactionProto.ChangedAt = transaction.ChangedAt.Format("2006-01-02 15:04:05")

		if transaction.BatchNumber != nil {
			transactionProto.BatchNumber = *transaction.BatchNumber
		}
		if transaction.SerialNumber != nil {
			transactionProto.SerialNumber = *transaction.SerialNumber
		}
		if transaction.ExpirationDate != nil {
			transactionProto.ExpirationDate = transaction.ExpirationDate.Format("2006-01-02 15:04:05")
		}
		if transaction.Source != nil {
			transactionProto.Source = *transaction.Source
		}
		if transaction.Document != nil {
			transactionProto.Document = *transaction.Document
		}
		if transaction.DocumentID != nil {
			transactionProto.DocumentId = *transaction.DocumentID
		}
		if transaction.DocumentCode != nil {
			transactionProto.DocumentCode = *transaction.DocumentCode
		}
		if transaction.ClientName != nil {
			transactionProto.ClientName = *transaction.ClientName
		}

		cardexResponse = append(cardexResponse, &transactionProto)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var resultCount, totalPages int
	query = `SELECT COUNT(id) FROM transaction WHERE item_id = $1 AND (changed_at BETWEEN $2 AND $3)`
	err = s.DB.QueryRow(context.TODO(), query, req.GetItemId(), fromTime, toTime).Scan(&resultCount)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get count of transaction rows", &errors.DBError)
	}
	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	return &inventory_proto.CardexReportResponse{
		TotalDocuments: int32(resultCount),
		TotalPages:     int32(totalPages),
		CurrentPage:    int32(*pageNo),
		Report:         cardexResponse,
	}, nil
}

// # Location Wise Inventory Report -> stream the report 'data' and 'metadata'
func (s *Grpc) LocationWiseInventoryReport(req *inventory_proto.LocationWiseInventoryReportRequest, stream inventory_proto.Inventory_LocationWiseInventoryReportServer) error {
	ctx := stream.Context() // # Get the 'stream' context
	var pageNo, pageSize *int
	var offset *int64
	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	var rows pgx.Rows
	var err error

	warehouseID := req.GetWarehouseId()
	clients := req.GetClients()
	locationOnHold := req.GetLocationOnHold()

	var locationOnHoldValue bool
	if locationOnHold == "true" {
		locationOnHoldValue = true
	}

	// # Base Query For Location Wise Inventory
	baseQuery := `SELECT l.*, i."name", i.base_unit FROM location_wise_inventory l INNER JOIN inventory i ON l.item_id = i.item_id WHERE l.warehouse_id = $1`
	params := []interface{}{warehouseID}
	paramIndex := 2

	// # Clients Filter
	if len(clients) > 0 {
		baseQuery += " AND l.client_id = ANY($" + strconv.Itoa(paramIndex) + ")"
		params = append(params, clients)
		paramIndex++
	}

	// # Location On Hold Filter
	if locationOnHold != "" {
		baseQuery += " AND l.on_hold = $" + strconv.Itoa(paramIndex)
		params = append(params, locationOnHoldValue)
		paramIndex++
	}

	// # Pagination Filter
	baseQuery += " ORDER BY l.code ASC"
	baseQuery += " LIMIT $" + strconv.Itoa(paramIndex)
	params = append(params, pageSize)
	paramIndex++
	baseQuery += " OFFSET $" + strconv.Itoa(paramIndex)
	params = append(params, offset)

	// # Execute Query
	rows, err = s.DB.Query(ctx, baseQuery, params...)
	if err != nil {
		return errors.Wrap(err, "failed to query location wise inventory", &errors.DBError)
	}
	if err == pgx.ErrNoRows {
		return errors.Wrap(err, "no rows found", &errors.DBError)
	}

	defer rows.Close()

	// # gRPC Server Streaming Optimization #
	// # Chunk Size
	const chunkSize = 10000

	// # Chunk List
	var chunk []*inventory_proto.LocationWiseInventoryReportDataResponse

	// # Chunk Counter
	var chunkCounter int = 0

	for rows.Next() {
		var location model.LocationWiseInventory
		var locationProto inventory_proto.LocationWiseInventoryReportDataResponse
		err := rows.Scan(
			&location.ID,
			&location.ItemID,
			&location.WarehouseID,
			&location.ClientID,
			&location.LocationID,
			&location.Code,
			&location.Quantity,
			&location.AvailableQty,
			&location.AreaName,
			&location.AreaID,
			&location.OnHold,
			&location.OnHoldByID,
			&location.LastUpdatedAt,
			&location.LastUpdatedByID,
			&location.LastUpdatedByName,
			&location.SKU,
			&location.Scannable,
			&location.BatchNumber,
			&location.SerialNumber,
			&location.ExpirationDate,
			&location.CreatedAt,
			&location.ClientName,
			&location.IsPrime,
			&location.Name,
			&location.BaseUnit,
		)
		if err != nil {
			return errors.Wrap(err, "unable to read resultant rows into location schema", &errors.DBError)
		}

		locationProto.Id = *location.ID
		locationProto.ItemId = *location.ItemID
		locationProto.WarehouseId = *location.WarehouseID
		locationProto.ClientId = *location.ClientID
		locationProto.LocationId = *location.LocationID
		locationProto.Code = *location.Code
		locationProto.Quantity = *location.Quantity
		locationProto.AvailableQty = *location.AvailableQty
		locationProto.AreaName = *location.AreaName
		locationProto.AreaId = *location.AreaID
		locationProto.OnHold = *location.OnHold
		if location.OnHoldByID != nil {
			locationProto.OnHoldById = *location.OnHoldByID
		}
		if location.LastUpdatedAt != nil {
			locationProto.LastUpdatedAt = location.LastUpdatedAt.Format("2006-01-02 15:04:05")
		}
		if location.LastUpdatedByID != nil {
			locationProto.LastUpdatedById = *location.LastUpdatedByID
		}
		if location.LastUpdatedByName != nil {
			locationProto.LastUpdatedById = *location.LastUpdatedByName
		}
		locationProto.Sku = *location.SKU
		locationProto.Scannable = *location.Scannable
		locationProto.Name = *location.Name
		locationProto.BaseUnit = *location.BaseUnit
		if location.CreatedAt != nil {
			locationProto.CreatedAt = location.CreatedAt.Format("2006-01-02 15:04:05")
		}
		if location.BatchNumber != nil {
			locationProto.BatchNumber = *location.BatchNumber
		}
		if location.SerialNumber != nil {
			locationProto.SerialNumber = *location.SerialNumber
		}
		if location.ExpirationDate != nil {
			locationProto.ExpirationDate = location.ExpirationDate.Format("2006-01-02 15:04:05")
		}
		if location.ClientName != nil {
			locationProto.ClientName = *location.ClientName
		}
		locationProto.LocationQr = "l/" + *location.LocationID + "/" + *location.Code

		// # Append the 'row' into the 'chunk' list
		chunk = append(chunk, &locationProto)

		// # Increment the 'chunk' counter
		chunkCounter++

		// # Send the report 'chunk' to the 'stream'
		// # Stream the 'chunk' if the chunk 'size' is reached
		if chunkCounter == chunkSize {
			err = stream.Send(&inventory_proto.LocationWiseInventoryReportStreamResponse{
				Response: &inventory_proto.LocationWiseInventoryReportStreamResponse_Chunk{
					Chunk: &inventory_proto.LocationWiseInventoryReportDataResponseList{
						Data: chunk,
					},
				},
			})
			if err != nil {
				return errors.Wrap(err, "failed to stream the report chunk", &errors.DBError)
			}

			// # Clear the 'chunk' list
			chunk = nil

			// # Reset the 'chunk' counter
			chunkCounter = 0
		}
	}

	// # Stream the remaining report 'data' if any 'left' in the 'chunk' list
	if chunkCounter > 0 {
		err = stream.Send(&inventory_proto.LocationWiseInventoryReportStreamResponse{
			Response: &inventory_proto.LocationWiseInventoryReportStreamResponse_Chunk{
				Chunk: &inventory_proto.LocationWiseInventoryReportDataResponseList{
					Data: chunk,
				},
			},
		})
		if err != nil {
			return errors.Wrap(err, "failed to stream the remaining report chunk", &errors.DBError)
		}

		// # Clear the 'chunk' list
		chunk = nil

		// # Reset the 'chunk' counter
		chunkCounter = 0
	}

	if row_err := rows.Err(); row_err != nil {
		return errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var resultCount, totalPages int

	// # Base Query For Count
	baseQuery = `SELECT COUNT(l."id") FROM location_wise_inventory l INNER JOIN inventory i ON l.item_id = i.item_id WHERE l.warehouse_id = $1`
	params = []interface{}{warehouseID}
	paramIndex = 2

	// # Clients Filter
	if len(clients) > 0 {
		baseQuery += " AND l.client_id = ANY($" + strconv.Itoa(paramIndex) + ")"
		params = append(params, clients)
		paramIndex++
	}

	// # Location On Hold Filter
	if locationOnHold != "" {
		baseQuery += " AND l.on_hold = $" + strconv.Itoa(paramIndex)
		params = append(params, locationOnHoldValue)
	}

	// # Execute Query
	err = s.DB.QueryRow(ctx, baseQuery, params...).Scan(&resultCount)
	if err != nil {
		return errors.Wrap(err, "unable to get count of location wise inventory rows", &errors.DBError)
	}

	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	// # Send the report 'metadata' to the 'stream'
	err = stream.Send(&inventory_proto.LocationWiseInventoryReportStreamResponse{
		Response: &inventory_proto.LocationWiseInventoryReportStreamResponse_Metadata{
			Metadata: &inventory_proto.LocationWiseInventoryReportMetaDataResponse{
				TotalDocuments: int32(resultCount),
				TotalPages:     int32(totalPages),
				CurrentPage:    int32(*pageNo),
			},
		},
	})
	if err != nil {
		return errors.Wrap(err, "failed to stream the report metadata", &errors.DBError)
	}

	return nil
}

// # Expired Items Location Wise Inventory Report -> stream the report 'data' and 'metadata'
func (s *Grpc) ExpiredItemsLocationWiseInventoryReport(req *inventory_proto.ExpiredItemsLocationWiseInventoryReportRequest, stream inventory_proto.Inventory_ExpiredItemsLocationWiseInventoryReportServer) error {
	ctx := stream.Context() // # Get the 'stream' context
	var pageNo, pageSize *int
	var offset *int64
	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	var rows pgx.Rows
	var err error

	warehouseID := req.GetWarehouseId()
	clients := req.GetClients()
	locationOnHold := req.GetLocationOnHold()

	var locationOnHoldValue bool
	if locationOnHold == "true" {
		locationOnHoldValue = true
	}

	layout := "2006-01-02 15:04:05 MST -07:00"
	currentDate, err := time.Parse(layout, req.GetCurrentDate())
	if err != nil {
		return errors.Wrap(err, "failed to parse current date", &errors.DBError)
	}

	// # Base Query For Location Wise Inventory
	baseQuery := `SELECT l.*, i."name", i.base_unit FROM location_wise_inventory l INNER JOIN inventory i ON l.item_id = i.item_id WHERE l.warehouse_id = $1 AND l.expiration_date IS NOT NULL AND l.expiration_date < $2`
	params := []any{warehouseID, currentDate}
	paramIndex := 3

	// # Clients Filter
	if len(clients) > 0 {
		baseQuery += " AND l.client_id = ANY($" + strconv.Itoa(paramIndex) + ")"
		params = append(params, clients)
		paramIndex++
	}

	// # Location On Hold Filter
	if locationOnHold != "" {
		baseQuery += " AND l.on_hold = $" + strconv.Itoa(paramIndex)
		params = append(params, locationOnHoldValue)
		paramIndex++
	}

	// # Pagination Filter
	baseQuery += " ORDER BY l.code ASC"
	baseQuery += " LIMIT $" + strconv.Itoa(paramIndex)
	params = append(params, pageSize)
	paramIndex++
	baseQuery += " OFFSET $" + strconv.Itoa(paramIndex)
	params = append(params, offset)

	// # Execute Query
	rows, err = s.DB.Query(ctx, baseQuery, params...)
	if err != nil {
		return errors.Wrap(err, "failed to query location wise inventory", &errors.DBError)
	}
	if err == pgx.ErrNoRows {
		return errors.Wrap(err, "no rows found", &errors.DBError)
	}

	defer rows.Close()

	// # gRPC Server Streaming Optimization #
	// # Chunk Size
	const chunkSize = 10000

	// # Chunk List
	var chunk []*inventory_proto.ExpiredItemsLocationWiseInventoryReportDataResponse

	// # Chunk Counter
	var chunkCounter int = 0

	for rows.Next() {
		var location model.LocationWiseInventory
		var locationProto inventory_proto.ExpiredItemsLocationWiseInventoryReportDataResponse
		err := rows.Scan(
			&location.ID,
			&location.ItemID,
			&location.WarehouseID,
			&location.ClientID,
			&location.LocationID,
			&location.Code,
			&location.Quantity,
			&location.AvailableQty,
			&location.AreaName,
			&location.AreaID,
			&location.OnHold,
			&location.OnHoldByID,
			&location.LastUpdatedAt,
			&location.LastUpdatedByID,
			&location.LastUpdatedByName,
			&location.SKU,
			&location.Scannable,
			&location.BatchNumber,
			&location.SerialNumber,
			&location.ExpirationDate,
			&location.CreatedAt,
			&location.ClientName,
			&location.IsPrime,
			&location.Name,
			&location.BaseUnit,
		)
		if err != nil {
			return errors.Wrap(err, "unable to read resultant rows into location schema", &errors.DBError)
		}

		locationProto.Id = *location.ID
		locationProto.ItemId = *location.ItemID
		locationProto.WarehouseId = *location.WarehouseID
		locationProto.ClientId = *location.ClientID
		locationProto.LocationId = *location.LocationID
		locationProto.Code = *location.Code
		locationProto.Quantity = *location.Quantity
		locationProto.AvailableQty = *location.AvailableQty
		locationProto.AreaName = *location.AreaName
		locationProto.AreaId = *location.AreaID
		locationProto.OnHold = *location.OnHold
		if location.OnHoldByID != nil {
			locationProto.OnHoldById = *location.OnHoldByID
		}
		if location.LastUpdatedAt != nil {
			locationProto.LastUpdatedAt = location.LastUpdatedAt.Format("2006-01-02 15:04:05")
		}
		if location.LastUpdatedByID != nil {
			locationProto.LastUpdatedById = *location.LastUpdatedByID
		}
		if location.LastUpdatedByName != nil {
			locationProto.LastUpdatedById = *location.LastUpdatedByName
		}
		locationProto.Sku = *location.SKU
		locationProto.Scannable = *location.Scannable
		locationProto.Name = *location.Name
		locationProto.BaseUnit = *location.BaseUnit
		if location.CreatedAt != nil {
			locationProto.CreatedAt = location.CreatedAt.Format("2006-01-02 15:04:05")
		}
		if location.BatchNumber != nil {
			locationProto.BatchNumber = *location.BatchNumber
		}
		if location.SerialNumber != nil {
			locationProto.SerialNumber = *location.SerialNumber
		}
		if location.ExpirationDate != nil {
			locationProto.ExpirationDate = location.ExpirationDate.Format("2006-01-02 15:04:05")
		}
		if location.ClientName != nil {
			locationProto.ClientName = *location.ClientName
		}
		locationProto.LocationQr = "l/" + *location.LocationID + "/" + *location.Code

		// # Append the 'row' into the 'chunk' list
		chunk = append(chunk, &locationProto)

		// # Increment the 'chunk' counter
		chunkCounter++

		// # Send the report 'chunk' to the 'stream'
		// # Stream the 'chunk' if the chunk 'size' is reached
		if chunkCounter == chunkSize {
			err = stream.Send(&inventory_proto.ExpiredItemsLocationWiseInventoryReportStreamResponse{
				Response: &inventory_proto.ExpiredItemsLocationWiseInventoryReportStreamResponse_Chunk{
					Chunk: &inventory_proto.ExpiredItemsLocationWiseInventoryReportDataResponseList{
						Data: chunk,
					},
				},
			})
			if err != nil {
				return errors.Wrap(err, "failed to stream the report chunk", &errors.DBError)
			}

			// # Clear the 'chunk' list
			chunk = nil

			// # Reset the 'chunk' counter
			chunkCounter = 0
		}
	}

	// # Stream the remaining report 'data' if any 'left' in the 'chunk' list
	if chunkCounter > 0 {
		err = stream.Send(&inventory_proto.ExpiredItemsLocationWiseInventoryReportStreamResponse{
			Response: &inventory_proto.ExpiredItemsLocationWiseInventoryReportStreamResponse_Chunk{
				Chunk: &inventory_proto.ExpiredItemsLocationWiseInventoryReportDataResponseList{
					Data: chunk,
				},
			},
		})
		if err != nil {
			return errors.Wrap(err, "failed to stream the remaining report chunk", &errors.DBError)
		}

		// # Clear the 'chunk' list
		chunk = nil

		// # Reset the 'chunk' counter
		chunkCounter = 0
	}

	if row_err := rows.Err(); row_err != nil {
		return errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var resultCount, totalPages int

	// # Base Query For Count
	baseQuery = `SELECT COUNT(l."id") FROM location_wise_inventory l INNER JOIN inventory i ON l.item_id = i.item_id WHERE l.warehouse_id = $1 AND l.expiration_date IS NOT NULL AND l.expiration_date < $2`
	params = []any{warehouseID, currentDate}
	paramIndex = 3

	// # Clients Filter
	if len(clients) > 0 {
		baseQuery += " AND l.client_id = ANY($" + strconv.Itoa(paramIndex) + ")"
		params = append(params, clients)
		paramIndex++
	}

	// # Location On Hold Filter
	if locationOnHold != "" {
		baseQuery += " AND l.on_hold = $" + strconv.Itoa(paramIndex)
		params = append(params, locationOnHoldValue)
	}

	// # Execute Query
	err = s.DB.QueryRow(ctx, baseQuery, params...).Scan(&resultCount)
	if err != nil {
		return errors.Wrap(err, "unable to get count of location wise inventory rows", &errors.DBError)
	}

	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	// # Send the report 'metadata' to the 'stream'
	err = stream.Send(&inventory_proto.ExpiredItemsLocationWiseInventoryReportStreamResponse{
		Response: &inventory_proto.ExpiredItemsLocationWiseInventoryReportStreamResponse_Metadata{
			Metadata: &inventory_proto.ExpiredItemsLocationWiseInventoryReportMetaDataResponse{
				TotalDocuments: int32(resultCount),
				TotalPages:     int32(totalPages),
				CurrentPage:    int32(*pageNo),
			},
		},
	})
	if err != nil {
		return errors.Wrap(err, "failed to stream the report metadata", &errors.DBError)
	}

	return nil
}

func (s *Grpc) LiveContainerReport(ctx context.Context, req *inventory_proto.LiveContainerReportRequest) (*inventory_proto.LiveContainerReportResponse, error) {
	var liveContainer []*inventory_proto.Container
	var pageNo, pageSize *int
	var offset *int64
	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	var query string
	var rows pgx.Rows
	var err error
	if req.GetClients() != nil {
		query = `SELECT container_wise_inventory.*, container_type.name, inventory.name, inventory.base_unit AS container_type_name FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id INNER JOIN inventory ON container_wise_inventory.item_id = inventory.item_id WHERE container_wise_inventory.warehouse_id = $1 AND container_wise_inventory.client_id = ANY($2) ORDER BY container_wise_inventory.id ASC LIMIT $3 OFFSET $4`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetClients(), pageSize, offset)
	} else {
		query = `SELECT container_wise_inventory.*, container_type.name, inventory.name, inventory.base_unit AS container_type_name FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id INNER JOIN inventory ON container_wise_inventory.item_id = inventory.item_id WHERE container_wise_inventory.warehouse_id = $1 ORDER BY container_wise_inventory.id ASC LIMIT $2 OFFSET $3`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), pageSize, offset)
	}
	if err != nil {
		return nil, errors.Wrap(err, "failed to query container wise inventory", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var container model.Container
		var containerProto inventory_proto.Container
		err := rows.Scan(
			&container.ID,
			&container.ItemID,
			&container.ContainerID,
			&container.ContainerTypeID,
			&container.WarehouseID,
			&container.ClientID,
			&container.LocationID,
			&container.Quantity,
			&container.AvailableQty,
			&container.Code,
			&container.LastUpdatedAt,
			&container.SKU,
			&container.Scannable,
			&container.BatchNumber,
			&container.SerialNumber,
			&container.ExpirationDate,
			&container.CreatedAt,
			&container.IsShipped,
			&container.ClientName,
			&container.OnHold,
			&container.OnHoldByID,
			&container.FirstUsedAt,
			&container.RefNo,
			&container.ContainerTypeName,
			&container.Name,
			&container.BaseUnit,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into location schema", &errors.DBError)
		}

		containerProto.Id = *container.ID
		containerProto.ItemId = *container.ItemID
		containerProto.ContainerId = container.ContainerID.String()
		containerProto.ContainerType = *container.ContainerTypeName
		containerProto.WarehouseId = *container.WarehouseID
		containerProto.ClientId = *container.ClientID
		containerProto.LocationId = *container.LocationID
		containerProto.Code = *container.Code
		containerProto.Quantity = *container.Quantity
		containerProto.AvailableQty = *container.AvailableQty
		containerProto.IsShipped = *container.IsShipped
		if container.LastUpdatedAt != nil {
			containerProto.LastUpdatedAt = container.LastUpdatedAt.Format("2006-01-02 15:04:05")
		}
		containerProto.Sku = *container.SKU
		containerProto.Name = *container.Name
		containerProto.BaseUnit = *container.BaseUnit
		containerProto.Scannable = *container.Scannable
		if container.CreatedAt != nil {
			containerProto.CreatedAt = container.CreatedAt.Format("2006-01-02 15:04:05")
		}

		if container.BatchNumber != nil {
			containerProto.BatchNumber = *container.BatchNumber
		}
		if container.SerialNumber != nil {
			containerProto.SerialNumber = *container.SerialNumber
		}
		if container.ExpirationDate != nil {
			containerProto.ExpirationDate = container.ExpirationDate.Format("2006-01-02 15:04:05")
		}
		containerProto.OnHold = container.OnHold
		if container.OnHoldByID != nil {
			containerProto.OnHoldBy = *container.OnHoldByID
		}
		if container.RefNo != nil {
			containerProto.RefNo = *container.RefNo
		}
		if container.ClientName != nil {
			containerProto.ClientName = *container.ClientName
		}

		liveContainer = append(liveContainer, &containerProto)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var resultCount, totalPages int
	if req.GetClients() != nil {
		query = `SELECT COUNT(container_wise_inventory.id) FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id INNER JOIN inventory ON container_wise_inventory.item_id = inventory.item_id WHERE container_wise_inventory.warehouse_id = $1 AND container_wise_inventory.client_id = ANY($2)`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), req.GetClients()).Scan(&resultCount)
	} else {
		query = `SELECT COUNT(container_wise_inventory.id) FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id INNER JOIN inventory ON container_wise_inventory.item_id = inventory.item_id WHERE container_wise_inventory.warehouse_id = $1`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId()).Scan(&resultCount)
	}
	if err != nil {
		return nil, errors.Wrap(err, "unable to get count of inventory rows", &errors.DBError)
	}

	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	return &inventory_proto.LiveContainerReportResponse{
		TotalDocuments: int32(resultCount),
		TotalPages:     int32(totalPages),
		CurrentPage:    int32(*pageNo),
		Report:         liveContainer,
	}, nil
}

func (s *Grpc) CreateDefaultContainers(ctx context.Context, req *inventory_proto.CreateDefaultContainersRequest) (*inventory_proto.CreateDefaultContainersResponse, error) {
	var response inventory_proto.CreateDefaultContainersResponse
	var defaultContainerTypes = []string{"loose", "pallet"}
	whid := req.GetWarehouseId()

	for _, containerType := range defaultContainerTypes {
		query := `INSERT INTO container_type (warehouse_id, name, only_ships_whole) VALUES ($1, $2, $3)`
		commandTag, err := s.DB.Exec(ctx, query, whid, containerType, false)
		if err != nil {
			return &response, errors.Wrap(err, "failed to create container", &errors.DBError)
		}
		if commandTag.RowsAffected() != 1 {
			return &response, errors.Wrap(err, "Container type already exists for this warehouse", &errors.BadRequest)
		}
	}

	response.Success = true

	return &response, nil
}

func (s *Grpc) UpdateItemHoldStatus(ctx context.Context, req *inventory_proto.UpdateItemHoldStatusRequest) (*inventory_proto.UpdateItemHoldStatusResponse, error) {
	var userName *string
	if req.GetUsername() == "" {
		userName = nil
	} else {
		v := req.GetUsername()
		userName = &v
	}
	query := `UPDATE inventory SET on_hold = $1, on_hold_by = $2 WHERE item_id = $3`
	commandTag, err := s.DB.Exec(ctx, query, req.GetOnHold(), userName, req.GetItemId())
	if err != nil {
		return nil, errors.Wrap(err, "Failed to deactivate item", &errors.DBError)
	}
	if commandTag.RowsAffected() != 1 {
		return nil, errors.Wrap(err, "Deactivation Failed", &errors.BadRequest)
	}

	return &inventory_proto.UpdateItemHoldStatusResponse{Success: true}, nil
}

func (s *Grpc) HoldContainer(ctx context.Context, req *inventory_proto.HoldContainerRequest) (*inventory_proto.HoldContainerResponse, error) {
	var batchNo *string
	if req.GetBatchNumber() == "" {
		batchNo = nil
	} else {
		v := req.GetBatchNumber()
		batchNo = &v
	}

	var serialNo *string
	if req.GetSerialNumber() == "" {
		serialNo = nil
	} else {
		v := req.GetSerialNumber()
		serialNo = &v
	}

	var expDate *time.Time
	if req.GetExpirationDate() == nil {
		expDate = nil
	} else {
		v := req.GetExpirationDate().AsTime()
		expDate = &v
	}
	var holdBy *string
	if req.GetIsHold() {
		v := "system"
		holdBy = &v
	} else {
		holdBy = nil
	}

	// Get item details from inventory
	_, err := s.App.Operations.GetItemFromInventory(req.GetItemId(), batchNo, serialNo, expDate)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get the item details from inventory "+req.GetItemId(), &errors.DBError)
	}

	query := `UPDATE container_wise_inventory SET on_hold = $1, on_hold_by = $2 WHERE item_id = $3 AND container_id = $4 AND location_id = $5 AND (batch_number = $6 OR $6 IS NULL) AND (serial_number = $7 OR $7 IS NULL) AND (expiration_date = $8 OR $8 IS NULL) AND warehouse_id = $9`
	commandTag, err := s.DB.Exec(ctx, query, req.GetIsHold(), holdBy, req.GetItemId(), req.GetContainerId(), req.GetLocationId(), batchNo, serialNo, expDate, req.GetWarehouseId())
	if err != nil {
		return nil, errors.Wrap(err, "Failed to hold container", &errors.DBError)
	}
	if commandTag.RowsAffected() != 1 {
		return nil, errors.New("Failed to update for hold container", &errors.DBError)
	}

	return &inventory_proto.HoldContainerResponse{Success: true}, nil
}

func (s *Grpc) AuditCCPlan(ctx context.Context, req *inventory_proto.AuditCCPlanRequest) (*inventory_proto.AuditCCPlanResponse, error) {

	var auditItems []schema.AuditItem
	for _, item := range req.GetData() {

		container_id := uuid.FromStringOrNil(item.GetContainerId())

		var batchNo *string
		if item.GetBatchNumber() == "" {
			batchNo = nil
		} else {
			v := item.GetBatchNumber()
			batchNo = &v
		}

		var serialNo *string
		if item.GetSerialNumber() == "" {
			serialNo = nil
		} else {
			v := item.GetSerialNumber()
			serialNo = &v
		}

		var expDate *time.Time
		if item.GetExpirationDate() == nil {
			expDate = nil
		} else {
			v := item.GetExpirationDate().AsTime()
			expDate = &v
		}
		auditItem := schema.AuditItem{
			SKU:            item.GetSku(),
			ItemID:         item.GetItemId(),
			BatchNumber:    batchNo,
			SerialNumber:   serialNo,
			ExpirationDate: expDate,
			Quantity:       int(item.GetQuantity()),
		}
		auditItems = append(auditItems, auditItem)

		auditInventory := schema.ValidateAuditInventory{
			LocationID:  item.GetLocationId(),
			ContainerID: container_id,
			Username:    req.GetUsername(),
			UserID:      req.GetUserId(),
			RequestID:   req.GetRequestId(),
			AuditItems:  auditItems,
		}

		// Audit inventory, replace system quantity with physical quantity
		_, err := s.App.Inventory.AuditInventory(&auditInventory, req.GetWarehouseId())
		if err != nil {
			return nil, errors.Wrap(err, "Failed to audit inventory for cycle count.", &errors.DBError)
		}

		query := `UPDATE container_wise_inventory SET on_hold = $1, on_hold_by = $2 WHERE item_id = $3 AND container_id = $4 AND location_id = $5 AND (batch_number = $6 OR $6 IS NULL) AND (serial_number = $7 OR $7 IS NULL) AND (expiration_date = $8 OR $8 IS NULL)`
		commandTag, err := s.DB.Exec(ctx, query, false, nil, item.GetItemId(), item.GetContainerId(), item.GetLocationId(), batchNo, serialNo, expDate)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to unhold container", &errors.DBError)
		}
		if commandTag.RowsAffected() != 1 {
			return nil, errors.Wrap(err, "Unhold container Failed", &errors.BadRequest)
		}
	}

	return &inventory_proto.AuditCCPlanResponse{Success: true}, nil
}

func (s *Grpc) GetLocationsForCC(ctx context.Context, req *inventory_proto.GetLocationsForCCRequest) (*inventory_proto.GetLocationsForCCResponse, error) {
	var response inventory_proto.GetLocationsForCCResponse
	whid := req.GetWarehouseId()
	locations := req.GetLocationId()
	sku := req.GetSkus()
	var query string
	var rows pgx.Rows
	var err error
	if len(sku) > 0 {
		query = `SELECT DISTINCT location_wise_inventory.location_id, location_wise_inventory.code, container_wise_inventory.container_id, container_wise_inventory.code FROM location_wise_inventory INNER JOIN container_wise_inventory ON container_wise_inventory.location_id = location_wise_inventory.location_id WHERE location_wise_inventory.warehouse_id = $1 AND container_wise_inventory.sku= ANY($2) AND location_wise_inventory.location_id = ANY($3)`
		rows, err = s.DB.Query(context.TODO(), query, whid, sku, locations)
	} else {
		query = `SELECT DISTINCT location_wise_inventory.location_id, location_wise_inventory.code, container_wise_inventory.container_id, container_wise_inventory.code FROM location_wise_inventory INNER JOIN container_wise_inventory ON container_wise_inventory.location_id = location_wise_inventory.location_id WHERE location_wise_inventory.warehouse_id = $1 AND location_wise_inventory.location_id = ANY($2)`
		rows, err = s.DB.Query(context.TODO(), query, whid, locations)
	}
	if err != nil {
		return nil, errors.Wrap(err, "Failed to query location wise inventory", &errors.DBError)
	}
	var containers []model.CycleCountContainers
	for rows.Next() {
		var container model.CycleCountContainers
		err := rows.Scan(
			&container.LocationID,
			&container.LocationCode,
			&container.ContainerID,
			&container.ContainerCode,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into location variable", &errors.DBError)
		}
		containers = append(containers, container)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}
	var result []*inventory_proto.GetLocationsForCCResponse_Container
	for _, con := range containers {
		res := inventory_proto.GetLocationsForCCResponse_Container{
			LocationId:    *con.LocationID,
			LocationCode:  *con.LocationCode,
			ContainerId:   con.ContainerID.String(),
			ContainerCode: *con.ContainerCode,
		}
		result = append(result, &res)
	}
	response.Containers = result
	return &response, nil
}

// func removeDuplicateValues(stringSlice []string) []string {
// 	keys := make(map[string]bool)
// 	list := []string{}

// 	// If the key(values of the slice) is not equal
// 	// to the already present value in new slice (list)
// 	// then we append it. else we jump on another element.
// 	for _, entry := range stringSlice {
// 		if _, value := keys[entry]; !value {
// 			keys[entry] = true
// 			list = append(list, entry)
// 		}
// 	}
// 	return list
// }

// # Get Tracked Inventory
func (s *Grpc) GetTrackedInventory(ctx context.Context, req *inventory_proto.GetTrackedInventoryRequest) (*inventory_proto.GetTrackedInventoryResponse, error) {
	// # Error Object
	var err error

	// # Get Request Parameters
	warehouseID := req.GetWarehouseId()
	clientID := req.GetClientId()
	itemID := req.GetItemId()

	// # Get Default Values
	var search, startDate, endDate string = "", "", ""
	var pickableNotZero bool = false

	// # Call 'Get Item Tracked Inventory' method
	trackedInventory, err := s.App.Inventory.GetItemTrackedInventory(warehouseID, clientID, itemID, search, startDate, endDate, pickableNotZero)
	if err != nil {
		err = errors.Wrap(err, "failed to get tracked inventory", &errors.SomethingWentWrong)
		return nil, err
	}

	var batches []*inventory_proto.GetTrackedInventoryResponse_BatchNumbers
	var serials []*inventory_proto.GetTrackedInventoryResponse_SerialNumbers
	var expirations []*inventory_proto.GetTrackedInventoryResponse_ExpirationDates

	for _, batchNo := range trackedInventory.BatchNumbers {
		batch := inventory_proto.GetTrackedInventoryResponse_BatchNumbers{
			BatchNumber:  batchNo.BatchNumber,
			PickableQty:  int32(batchNo.PickableQty),
			AvailableQty: int32(batchNo.AvailableQty),
		}
		batches = append(batches, &batch)
	}

	for _, serialNo := range trackedInventory.SerialNumbers {
		serial := inventory_proto.GetTrackedInventoryResponse_SerialNumbers{
			SerialNumber: serialNo.SerialNumber,
			PickableQty:  int32(serialNo.PickableQty),
			AvailableQty: int32(serialNo.AvailableQty),
		}
		serials = append(serials, &serial)
	}

	for _, expirDate := range trackedInventory.ExpirationDates {
		expiration := inventory_proto.GetTrackedInventoryResponse_ExpirationDates{
			BatchNumber:  expirDate.BatchNumber,
			SerialNumber: expirDate.SerialNumber,
			PickableQty:  int32(expirDate.PickableQty),
			AvailableQty: int32(expirDate.AvailableQty),
		}
		if expirDate.ExpirationDate != nil {
			expiration.ExpirationDate = timestamppb.New(*expirDate.ExpirationDate)
		}

		expirations = append(expirations, &expiration)
	}

	return &inventory_proto.GetTrackedInventoryResponse{
		BatchNumbers:    batches,
		SerialNumbers:   serials,
		ExpirationDates: expirations,
	}, nil
}

func (s *Grpc) MakeAreaPickable(ctx context.Context, req *inventory_proto.MakeAreaPickableRequest) (*inventory_proto.MakeAreaPickableResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	var itemsMarkedPickable []schema.MakeAreaPickable
	var itemsMarkedPickableTracked []schema.MakeAreaPickableTracked

	// Query for items by area
	query := `SELECT item_id, SUM(available_qty) FROM location_wise_inventory WHERE area_id = $1 AND on_hold = $2 GROUP BY item_id`
	rows, err := s.DB.Query(context.TODO(), query, req.GetAreaId(), false)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to query for location items", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var itemMarkedPickable schema.MakeAreaPickable
		err := rows.Scan(
			&itemMarkedPickable.ItemID,
			&itemMarkedPickable.Quantity,
		)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "unable to read resultant rows into item schema", &errors.DBError)
		}
		if itemMarkedPickable.ItemID != nil {
			itemsMarkedPickable = append(itemsMarkedPickable, itemMarkedPickable)
		}
	}
	if row_err := rows.Err(); row_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	// Query for tracked items by area
	query = `SELECT item_id, batch_number, serial_number, expiration_date, SUM(available_qty) FROM location_wise_inventory WHERE area_id = $1 AND on_hold = $2 GROUP BY item_id, batch_number, serial_number, expiration_date`
	rows, err = s.DB.Query(context.TODO(), query, req.GetAreaId(), false)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to query for tracked location items", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var itemMarkedPickableTracked schema.MakeAreaPickableTracked
		err := rows.Scan(
			&itemMarkedPickableTracked.ItemID,
			&itemMarkedPickableTracked.BatchNumber,
			&itemMarkedPickableTracked.SerialNumber,
			&itemMarkedPickableTracked.ExpirationDate,
			&itemMarkedPickableTracked.Quantity,
		)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "unable to read resultant rows into tracked item schema", &errors.DBError)
		}
		if itemMarkedPickableTracked.ItemID != nil {
			if !(itemMarkedPickableTracked.BatchNumber == nil && itemMarkedPickableTracked.SerialNumber == nil && itemMarkedPickableTracked.ExpirationDate == nil) {
				itemsMarkedPickableTracked = append(itemsMarkedPickableTracked, itemMarkedPickableTracked)
			}
		}
	}
	if row_err := rows.Err(); row_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	// Update in inventory
	for _, item := range itemsMarkedPickable {
		var quantity int
		if req.GetIsPickable() {
			quantity = item.Quantity
		} else {
			quantity = -item.Quantity
		}
		query := `UPDATE inventory SET pickable_qty = pickable_qty + $1 WHERE item_id = $2`
		commandTag, err := tx.Exec(ctx, query, quantity, item.ItemID)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "error incrementing pickable quantity in inventory", &errors.DBError)
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return nil, errors.New("error incrementing pickable quantity in inventory", &errors.DBError)
		}
	}

	// Update in tracked inventory
	for _, item := range itemsMarkedPickableTracked {
		var quantity int
		if req.GetIsPickable() {
			quantity = item.Quantity
		} else {
			quantity = -item.Quantity
		}
		query := `UPDATE tracked_inventory SET pickable_qty = pickable_qty + $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL)`
		commandTag, err := tx.Exec(ctx, query, quantity, item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "error incrementing pickable quantity in tracked inventory", &errors.DBError)
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return nil, errors.New("no rows were affected while incrementing pickable quantity in tracked inventory", &errors.DBError)
		}
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back", &errors.DBError)
	}

	return &inventory_proto.MakeAreaPickableResponse{Success: true}, nil
}

func (s *Grpc) GetClientContainers(ctx context.Context, req *inventory_proto.GetClientContainersRequest) (*inventory_proto.GetClientContainersResponse, error) {
	// Map {client_id: []inventory_proto.containerData}
	clientContMap := make(map[string][]*inventory_proto.ContainerData)

	// Query for container details of all clients
	query := `SELECT DISTINCT container_id, container_type.name, client_id, first_used_at FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id WHERE client_id = ANY($1)`
	rows, err := s.DB.Query(context.TODO(), query, req.GetClientIds())
	if err != nil {
		return nil, errors.Wrap(err, "failed to query container wise inventory", &errors.DBError)
	}
	for rows.Next() {
		var containerData inventory_proto.ContainerData
		var clientID string
		var firstUsedAtStr string
		var firstUsedAt *time.Time
		err := rows.Scan(
			&containerData.ContainerId,
			&containerData.ContainerType,
			&clientID,
			&firstUsedAt,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into container data variable", &errors.DBError)
		}
		if firstUsedAt != nil {
			firstUsedAtStr = firstUsedAt.Format("2006-01-02 15:04:05")
		}
		containerData.FirstUsedAt = firstUsedAtStr

		// Add to map
		clientContMap[clientID] = append(clientContMap[clientID], &containerData)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var response inventory_proto.GetClientContainersResponse
	for client, containerList := range clientContMap {
		clientContainer := inventory_proto.ClientContainer{
			ClientId:   client,
			Containers: containerList,
		}
		response.ClientContainers = append(response.ClientContainers, &clientContainer)
	}

	return &response, nil
}

func (s *Grpc) GetInboundSummary(ctx context.Context, req *inventory_proto.GetInboundSummaryRequest) (*inventory_proto.GetInboundSummaryResponse, error) {
	// Get receiving area IDs
	coreReq := &core_proto.GetReceivingAreaIdsRequest{
		WarehouseId: req.GetWarehouseId(),
	}
	res, err := s.App.GrpcClient.Core.Client.GetReceivingAreaIds(ctx, coreReq)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get receiving areas from core", &errors.SomethingWentWrong)
	}

	var itemsInReceivingCount int32
	query := `SELECT COUNT(DISTINCT item_id) FROM location_wise_inventory WHERE warehouse_id = $1 AND area_id = ANY($2)`
	err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), res.GetReceivingAreaIds()).Scan(&itemsInReceivingCount)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query count from location_wise_inventory", &errors.DBError)
	}

	response := inventory_proto.GetInboundSummaryResponse{
		ItemsInReceiving: itemsInReceivingCount,
	}

	var locations []string
	query = `SELECT DISTINCT location_id FROM location_wise_inventory WHERE warehouse_id = $1 AND area_id = ANY($2)`
	rows, err := s.DB.Query(context.TODO(), query, req.GetWarehouseId(), res.GetReceivingAreaIds())
	if err != nil {
		return nil, errors.Wrap(err, "failed to query location_wise_inventory", &errors.DBError)
	}
	for rows.Next() {
		var locationID string
		err := rows.Scan(
			&locationID,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into location variables", &errors.DBError)
		}
		locations = append(locations, locationID)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	// Get container types and counts in receiving
	query = `SELECT container_type_id, container_type.name, COUNT(container_type_id) FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id WHERE container_wise_inventory.warehouse_id = $1 AND container_wise_inventory.location_id = ANY($2) GROUP BY container_type_id, container_type.name`
	rows, err = s.DB.Query(context.TODO(), query, req.GetWarehouseId(), locations)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query container wise inventory", &errors.DBError)
	}
	for rows.Next() {
		var containerTypeID, containerTypeCount int64
		var containerType string
		err := rows.Scan(
			&containerTypeID,
			&containerType,
			&containerTypeCount,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into variables", &errors.DBError)
		}

		if containerTypeCount > 0 {
			containerInReceiving := inventory_proto.ContainerTypeCount{
				Type:  containerType,
				Count: int32(containerTypeCount),
			}
			response.ContainersInReceiving = append(response.ContainersInReceiving, &containerInReceiving)
		}
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	return &response, nil
}

func (s *Grpc) GetInventoryForShopify(ctx context.Context, req *inventory_proto.GetInventoryForShopifyRequest) (*inventory_proto.GetInventoryForShopifyResponse, error) {
	var items []*inventory_proto.GetInventoryForShopifyResponse_Inventory
	var query string
	var rows pgx.Rows
	var err error

	if req.GetClientId() != nil {
		query = `SELECT sku, available_qty FROM inventory WHERE sku = ANY($1) AND client_id = ANY($2) AND warehouse_id = $3`
		rows, err = s.DB.Query(ctx, query, req.GetSku(), req.GetClientId(), req.GetWarehouseId())
	} else {
		return nil, errors.New("client ID is required.", &errors.DBError)
	}
	if err != nil {
		return nil, errors.New("failed to query for inventory items", &errors.DBError)
	}
	for rows.Next() {
		var item model.Inventory
		err := rows.Scan(
			&item.SKU,
			&item.AvailableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into inventory schema", &errors.DBError)
		}
		items = append(items, &inventory_proto.GetInventoryForShopifyResponse_Inventory{
			Sku:      item.SKU,
			Quantity: item.AvailableQty,
		})
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	return &inventory_proto.GetInventoryForShopifyResponse{
		Inventrory: items,
	}, nil
}

func (s *Grpc) GetClientInventory(ctx context.Context, req *inventory_proto.GetClientInventoryRequest) (*inventory_proto.GetClientInventoryResponse, error) {
	var items []*inventory_proto.CartInventory
	var query string
	var rows pgx.Rows
	var err error

	query = `SELECT sku, quantity, available_qty, pickable_qty FROM inventory WHERE client_id = $1 AND warehouse_id = $2`
	rows, err = s.DB.Query(ctx, query, req.GetClientId(), req.GetWarehouseId())
	if err != nil {
		return nil, errors.New("failed to query for inventory items", &errors.DBError)
	}
	for rows.Next() {
		var item model.Inventory
		err := rows.Scan(
			&item.SKU,
			&item.Quantity,
			&item.AvailableQty,
			&item.PickableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into inventory schema", &errors.DBError)
		}
		items = append(items, &inventory_proto.CartInventory{
			Sku:               item.SKU,
			Quantity:          item.Quantity,
			AvailableQuantity: item.AvailableQty,
			PickableQuantity:  item.PickableQty,
		})
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	return &inventory_proto.GetClientInventoryResponse{
		Inventory: items,
	}, nil
}

func (s *Grpc) EditItem(ctx context.Context, req *inventory_proto.EditItemRequest) (*inventory_proto.EditItemResponse, error) {

	var name, description, scannable string
	var image *string
	query := `SELECT name, description, image, scannable FROM inventory WHERE item_id = $1 LIMIT 1`
	err := s.DB.QueryRow(context.TODO(), query, req.GetItemId()).Scan(&name, &description, &image, &scannable)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get the item data from inventory", &errors.DBError)
	}

	var new_name string
	if req.GetName() == "" {
		new_name = name
	} else {
		new_name = req.GetName()
	}

	var new_image *string
	if req.GetImage() == "" {
		new_image = image
	} else {
		v := req.GetImage()
		new_image = &v
	}

	var new_description string
	if req.GetDescription() == "" {
		new_description = description
	} else {
		new_description = req.GetDescription()
	}

	var new_scannable string
	if req.GetScannable() == "" {
		new_scannable = scannable
	} else {
		new_scannable = req.GetScannable()
	}

	query = `UPDATE inventory SET name = $1, image = $2, description = $3, scannable = $4 WHERE item_id = $5`
	commandTag, err := s.DB.Exec(ctx, query, new_name, new_image, new_description, new_scannable, req.GetItemId())
	if err != nil {
		return nil, errors.Wrap(err, "Failed to update item info", &errors.DBError)
	}
	if commandTag.RowsAffected() != 1 {
		return nil, errors.Wrap(err, "Failed to edit item!", &errors.BadRequest)
	}

	return &inventory_proto.EditItemResponse{Success: true}, nil
}

func (s *Grpc) GetClientWiseReceivedContainers(ctx context.Context, req *inventory_proto.GetClientWiseReceivedContainersRequest) (*inventory_proto.GetClientWiseReceivedContainersResponse, error) {
	var response inventory_proto.GetClientWiseReceivedContainersResponse
	// Get receiving area IDs
	coreReq := &core_proto.GetReceivingAreaIdsRequest{
		WarehouseId: req.GetWarehouseId(),
	}
	res, err := s.App.GrpcClient.Core.Client.GetReceivingAreaIds(ctx, coreReq)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get receiving areas from core", &errors.SomethingWentWrong)
	}

	// Get receiving locations
	var locations []string
	query := `SELECT DISTINCT location_id FROM location_wise_inventory WHERE warehouse_id = $1 AND area_id = ANY($2)`
	rows, err := s.DB.Query(ctx, query, req.GetWarehouseId(), res.GetReceivingAreaIds())
	if err != nil {
		return nil, errors.Wrap(err, "failed to query location_wise_inventory", &errors.DBError)
	}
	for rows.Next() {
		var locationID string
		err := rows.Scan(
			&locationID,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into location variables", &errors.DBError)
		}
		locations = append(locations, locationID)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	// Get client wise count of containers in receiving
	query = `SELECT client_id, client_name, COUNT(client_id) FROM container_wise_inventory WHERE warehouse_id = $1 AND location_id = ANY($2) GROUP BY client_id, client_name`
	rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), locations)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query location_wise_inventory", &errors.DBError)
	}
	for rows.Next() {
		var clientID, clientName string
		var count int
		err := rows.Scan(
			&clientID,
			&clientName,
			&count,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into variables", &errors.DBError)
		}
		clientWiseContainer := &inventory_proto.ClientWiseReceivedContainer{
			ClientId:   clientID,
			ClientName: clientName,
			Count:      strconv.Itoa(count),
		}
		response.ClientWiseReceivedContainers = append(response.ClientWiseReceivedContainers, clientWiseContainer)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	return &response, nil
}

// # Client Wise Inventory Report -> stream the report 'data' and 'metadata'
func (s *Grpc) ClientWiseInventoryReport(req *inventory_proto.ClientWiseInventoryReportRequest, stream inventory_proto.Inventory_ClientWiseInventoryReportServer) error {
	ctx := stream.Context() // # Get the 'stream' context
	var query string
	var rows pgx.Rows
	var err error
	var pageNo, pageSize *int
	var offset *int64
	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	if len(req.GetClients()) > 0 {
		query = `SELECT * FROM inventory WHERE warehouse_id = $1 AND client_id = ANY($2) ORDER BY id DESC LIMIT $3 OFFSET $4`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetClients(), pageSize, offset)
	} else {
		query = `SELECT * FROM inventory WHERE warehouse_id = $1 ORDER BY id DESC LIMIT $2 OFFSET $3`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), pageSize, offset)
	}
	if err != nil {
		return errors.Wrap(err, "failed to query inventory", &errors.DBError)
	}

	defer rows.Close()

	// # gRPC Server Streaming Optimization #
	// # Chunk Size
	const chunkSize = 10000

	// # Chunk List
	var chunk []*inventory_proto.ClientWiseInventoryReportDataResponse

	// # Chunk Counter
	var chunkCounter int = 0

	for rows.Next() {
		var inventory model.Inventory
		var inventoryProto inventory_proto.ClientWiseInventoryReportDataResponse
		err := rows.Scan(
			&inventory.ID,
			&inventory.ItemID,
			&inventory.ClientID,
			&inventory.WarehouseID,
			&inventory.Quantity,
			&inventory.AvailableQty,
			&inventory.PickableQty,
			&inventory.BaseUnit,
			&inventory.SKU,
			&inventory.Scannable,
			&inventory.CreatedAt,
			&inventory.UpdatedAt,
			&inventory.IsDeleted,
			&inventory.OnHold,
			&inventory.IsBatchControlled,
			&inventory.IsTrackedBySerialNo,
			&inventory.Name,
			&inventory.Description,
			&inventory.OnHoldBy,
			&inventory.IsPerishable,
			&inventory.TrackedBy,
			&inventory.Image,
			&inventory.BackOrders,
			&inventory.ClientName,
			&inventory.IsClientDeleted,
			&inventory.IsKit,
			&inventory.DynamicColumn,

			// # LeanShip #
			&inventory.IsLeanShip,
		)
		if err != nil {
			return errors.Wrap(err, "unable to read resultant rows into inventory schema", &errors.DBError)
		}

		inventoryProto.Id = inventory.ID
		inventoryProto.ItemId = inventory.ItemID
		inventoryProto.WarehouseId = inventory.WarehouseID
		inventoryProto.ClientId = inventory.ClientID
		inventoryProto.Quantity = inventory.Quantity
		inventoryProto.AvailableQty = inventory.AvailableQty
		inventoryProto.PickableQty = inventory.PickableQty
		inventoryProto.OnHold = inventory.OnHold
		if inventory.OnHoldBy != nil {
			inventoryProto.OnHoldBy = *inventory.OnHoldBy
		}
		if inventory.CreatedAt != nil {
			inventoryProto.CreatedAt = inventory.CreatedAt.Format("2006-01-02 15:04:05")
		}
		if inventory.UpdatedAt != nil {
			inventoryProto.UpdatedAt = inventory.UpdatedAt.Format("2006-01-02 15:04:05")
		}
		if len(inventory.Description) > 0 {
			inventoryProto.Description = inventory.Description
		}
		inventoryProto.Sku = inventory.SKU
		inventoryProto.Scannable = inventory.Scannable
		inventoryProto.IsDeleted = strconv.FormatBool(inventory.IsDeleted)
		inventoryProto.Name = inventory.Name
		inventoryProto.BaseUnit = inventory.BaseUnit

		if inventory.IsBatchControlled {
			inventoryProto.IsBatchControlled = inventory.IsBatchControlled
		}
		if inventory.IsTrackedBySerialNo {
			inventoryProto.IsTrackedBySerialNo = inventory.IsTrackedBySerialNo
		}
		if inventory.IsPerishable {
			inventoryProto.IsPerishable = inventory.IsPerishable
		}
		if inventory.ClientName != nil {
			inventoryProto.ClientName = *inventory.ClientName
		}

		// # Append the 'row' into the 'chunk' list
		chunk = append(chunk, &inventoryProto)

		// # Increment the 'chunk' counter
		chunkCounter++

		// # Send the report 'chunk' to the 'stream'
		// # Stream the 'chunk' if the chunk 'size' is reached
		if chunkCounter == chunkSize {
			err = stream.Send(&inventory_proto.ClientWiseInventoryReportStreamResponse{
				Response: &inventory_proto.ClientWiseInventoryReportStreamResponse_Chunk{
					Chunk: &inventory_proto.ClientWiseInventoryReportDataResponseList{
						Data: chunk,
					},
				},
			})
			if err != nil {
				return errors.Wrap(err, "failed to stream report chunk", &errors.DBError)
			}

			// # Clear the 'chunk' list
			chunk = nil

			// # Reset the 'chunk' counter
			chunkCounter = 0
		}
	}

	// # Stream the remaining report 'data' if any 'left' in the 'chunk' list
	if chunkCounter > 0 {
		err = stream.Send(&inventory_proto.ClientWiseInventoryReportStreamResponse{
			Response: &inventory_proto.ClientWiseInventoryReportStreamResponse_Chunk{
				Chunk: &inventory_proto.ClientWiseInventoryReportDataResponseList{
					Data: chunk,
				},
			},
		})
		if err != nil {
			return errors.Wrap(err, "failed to stream remaining report chunk", &errors.DBError)
		}

		// # Clear the 'chunk' list
		chunk = nil

		// # Reset the 'chunk' counter
		chunkCounter = 0
	}

	if row_err := rows.Err(); row_err != nil {
		return errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var resultCount, totalPages int
	if len(req.GetClients()) > 0 {
		query := `SELECT COUNT(id) FROM inventory WHERE warehouse_id = $1 AND client_id = ANY($2)`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), req.GetClients()).Scan(&resultCount)
	} else {
		query := `SELECT COUNT(id) FROM inventory WHERE warehouse_id = $1`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId()).Scan(&resultCount)
	}
	if err != nil {
		return errors.Wrap(err, "unable to get count of inventory rows", &errors.DBError)
	}

	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	// # Send the report 'metadata' to the 'stream'
	err = stream.Send(&inventory_proto.ClientWiseInventoryReportStreamResponse{
		Response: &inventory_proto.ClientWiseInventoryReportStreamResponse_Metadata{
			Metadata: &inventory_proto.ClientWiseInventoryReportMetaDataResponse{
				TotalDocuments: int32(resultCount),
				TotalPages:     int32(totalPages),
				CurrentPage:    int32(*pageNo),
			},
		},
	})
	if err != nil {
		return errors.Wrap(err, "failed to stream report metadata", &errors.DBError)
	}

	return nil
}

// # Inventory Out Of Stock Report -> stream the report 'data' and 'metadata'
func (s *Grpc) InventoryOutOfStockReport(req *inventory_proto.InventoryOutOfStockReportRequest, stream inventory_proto.Inventory_InventoryOutOfStockReportServer) error {
	ctx := stream.Context() // # Get the 'stream' context
	var query string
	var rows pgx.Rows
	var err error
	var pageNo, pageSize *int
	var offset *int64
	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	if len(req.GetClients()) > 0 {
		query = `SELECT client_name, sku, scannable, quantity, name, description, base_unit, is_deleted, is_batch_controlled, is_tracked_by_serial_no, is_perishable FROM inventory WHERE warehouse_id = $1 AND quantity = $2 AND client_id = ANY($3) ORDER BY id DESC LIMIT $4 OFFSET $5`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), 0, req.GetClients(), pageSize, offset)
	} else {
		query = `SELECT client_name, sku, scannable, quantity, name, description, base_unit, is_deleted, is_batch_controlled, is_tracked_by_serial_no, is_perishable FROM inventory WHERE warehouse_id = $1 AND quantity = $2 ORDER BY id DESC LIMIT $3 OFFSET $4`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), 0, pageSize, offset)
	}
	if err != nil {
		return errors.Wrap(err, "failed to query inventory", &errors.DBError)
	}

	defer rows.Close()

	// # gRPC Server Streaming Optimization #
	// # Chunk Size
	const chunkSize = 10000

	// # Chunk List
	var chunk []*inventory_proto.InventoryOutOfStockReportDataResponse

	// # Chunk Counter
	var chunkCounter int = 0

	for rows.Next() {
		var inventory model.Inventory
		var inventoryProto inventory_proto.InventoryOutOfStockReportDataResponse
		err := rows.Scan(
			&inventory.ClientName,
			&inventory.SKU,
			&inventory.Scannable,
			&inventory.Quantity,
			&inventory.Name,
			&inventory.Description,
			&inventory.BaseUnit,
			&inventory.IsDeleted,
			&inventory.IsBatchControlled,
			&inventory.IsTrackedBySerialNo,
			&inventory.IsPerishable,
		)
		if err != nil {
			return errors.Wrap(err, "unable to read resultant rows into inventory schema", &errors.DBError)
		}

		inventoryProto.Sku = inventory.SKU
		inventoryProto.Scannable = inventory.Scannable
		inventoryProto.Quantity = strconv.Itoa(int(inventory.Quantity))
		inventoryProto.Name = inventory.Name
		inventoryProto.BaseUnit = inventory.BaseUnit
		inventoryProto.IsDeleted = strconv.FormatBool(inventory.IsDeleted)
		inventoryProto.IsBatchControlled = strconv.FormatBool(inventory.IsBatchControlled)
		inventoryProto.IsTrackedBySerialNo = strconv.FormatBool(inventory.IsTrackedBySerialNo)
		inventoryProto.IsPerishable = strconv.FormatBool(inventory.IsPerishable)
		inventoryProto.Description = inventory.Description
		if inventory.ClientName != nil {
			inventoryProto.ClientName = *inventory.ClientName
		}

		// # Append the 'row' into the 'chunk' list
		chunk = append(chunk, &inventoryProto)

		// # Increment the 'chunk' counter
		chunkCounter++

		// # Send the report 'chunk' to the 'stream'
		// # Stream the 'chunk' if the chunk 'size' is reached
		if chunkCounter == chunkSize {
			err = stream.Send(&inventory_proto.InventoryOutOfStockReportStreamResponse{
				Response: &inventory_proto.InventoryOutOfStockReportStreamResponse_Chunk{
					Chunk: &inventory_proto.InventoryOutOfStockReportDataResponseList{
						Data: chunk,
					},
				},
			})
			if err != nil {
				return errors.Wrap(err, "failed to stream the report chunk", &errors.DBError)
			}

			// # Clear the 'chunk' list
			chunk = nil

			// # Reset the 'chunk' counter
			chunkCounter = 0
		}
	}

	// # Stream the remaining report 'data' if any 'left' in the 'chunk' list
	if chunkCounter > 0 {
		err = stream.Send(&inventory_proto.InventoryOutOfStockReportStreamResponse{
			Response: &inventory_proto.InventoryOutOfStockReportStreamResponse_Chunk{
				Chunk: &inventory_proto.InventoryOutOfStockReportDataResponseList{
					Data: chunk,
				},
			},
		})
		if err != nil {
			return errors.Wrap(err, "failed to stream the remaining report chunk", &errors.DBError)
		}

		// # Clear the 'chunk' list
		chunk = nil

		// # Reset the 'chunk' counter
		chunkCounter = 0
	}

	if row_err := rows.Err(); row_err != nil {
		return errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var resultCount, totalPages int
	if len(req.GetClients()) > 0 {
		query := `SELECT COUNT(id) FROM inventory WHERE warehouse_id = $1 AND quantity = $2 AND client_id = ANY($3)`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), 0, req.GetClients()).Scan(&resultCount)
	} else {
		query := `SELECT COUNT(id) FROM inventory WHERE warehouse_id = $1 AND quantity = $2`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), 0).Scan(&resultCount)
	}
	if err != nil {
		return errors.Wrap(err, "unable to get count of inventory rows", &errors.DBError)
	}

	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	// # Send the report 'metadata' to the 'stream'
	err = stream.Send(&inventory_proto.InventoryOutOfStockReportStreamResponse{
		Response: &inventory_proto.InventoryOutOfStockReportStreamResponse_Metadata{
			Metadata: &inventory_proto.InventoryOutOfStockReportMetaDataResponse{
				TotalDocuments: int32(resultCount),
				TotalPages:     int32(totalPages),
				CurrentPage:    int32(*pageNo),
			},
		},
	})
	if err != nil {
		return errors.Wrap(err, "failed to stream the report metadata", &errors.DBError)
	}

	return nil
}

// # Warehouse Operations Report (stream report data and metadata)
func (s *Grpc) WarehouseOperationsReportStream(req *inventory_proto.WarehouseOperationsReportRequest, stream inventory_proto.Inventory_WarehouseOperationsReportStreamServer) error {
	ctx := stream.Context() // # Get Stream Context
	var query string
	var rows pgx.Rows
	var err error
	var pageNo, pageSize *int
	var offset *int64

	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}

	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_

	startDate, err := time.Parse(time.DateTime, req.GetStartTime())
	if err != nil {
		return err
	}

	endDate, err := time.Parse(time.DateTime, req.GetEndTime())
	if err != nil {
		return err
	}

	if all {
		pageSize = nil
		offset = nil
	}

	// # gRPC Streaming Optimization >> Increase gRPC streaming chunk size
	// # Chunk Size
	const chunkSize = 10000
	// # Chunk List
	var chunk []*inventory_proto.WarehouseOperationsReportDataResponse
	// # Chunk Counter
	var chunkCounter int = 0

	action := req.GetOperationType()
	hasAction := len(action) > 0

	selectStmt := `
		SELECT t.*, inventory.base_unit
		FROM transaction t
		INNER JOIN inventory ON t.item_id = inventory.item_id`
	where := `
		WHERE t.warehouse_id = $1 
		AND t.changed_at BETWEEN $2 AND $3
	`
	queryParams := []interface{}{req.GetWarehouseId(), startDate, endDate}
	paramIndex := len(queryParams)

	// Add filters
	if req.GetClients() != nil {
		paramIndex++
		where += fmt.Sprintf(" AND t.client_id = ANY($%d)", paramIndex)
		queryParams = append(queryParams, req.GetClients())
	}
	if req.GetItems() != nil {
		paramIndex++
		where += fmt.Sprintf(" AND t.item_id = ANY($%d)", paramIndex)
		queryParams = append(queryParams, req.GetItems())
	}
	if req.GetLocations() != nil {
		paramIndex++
		where += fmt.Sprintf(" AND t.location_id = ANY($%d)", paramIndex)
		queryParams = append(queryParams, req.GetLocations())
	}
	if strings.TrimSpace(req.GetBatchNumber()) != "" {
		batchNumber := "%" + req.GetBatchNumber() + "%"
		paramIndex++
		where += fmt.Sprintf(" AND t.batch_number ILIKE $%d", paramIndex)
		queryParams = append(queryParams, batchNumber)
	}
	if hasAction {
		paramIndex++
		where += fmt.Sprintf(" AND t.action = ANY($%d)", paramIndex)
		queryParams = append(queryParams, action)
	}
	if req.GetLps() != nil {
		paramIndex++
		where += fmt.Sprintf(" AND t.container_id = ANY($%d)", paramIndex)
		queryParams = append(queryParams, req.GetLps())
	}

	sort := "ORDER BY t.id DESC"

	// Used to get the count of results for pagination without skip and limit applied
	countParams := queryParams

	paramIndex++
	paginate := fmt.Sprintf("LIMIT $%d OFFSET $%d", paramIndex, paramIndex+1)
	paramIndex++
	queryParams = append(queryParams, pageSize, offset)

	// Build query using clauses
	query = selectStmt + " " + where + " " + sort + " " + paginate

	rows, err = s.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return errors.Wrap(err, "failed to query transaction", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var transaction model.WarehouseOperationReport
		var transactionProto inventory_proto.WarehouseOperationsReportDataResponse
		err := rows.Scan(
			&transaction.ID,
			&transaction.ClientID,
			&transaction.WarehouseID,
			&transaction.RequestID,
			&transaction.ItemID,
			&transaction.SKU,
			&transaction.Action,
			&transaction.Change,
			&transaction.Current,
			&transaction.ChangedByUsername,
			&transaction.ChangedByID,
			&transaction.ChangedAt,
			&transaction.BatchNumber,
			&transaction.SerialNumber,
			&transaction.ExpirationDate,
			&transaction.Source,
			&transaction.Document,
			&transaction.DocumentCode,
			&transaction.ClientName,
			&transaction.DocumentID,
			&transaction.Remark,
			&transaction.DocumentPrn,
			&transaction.LocationID,
			&transaction.LocationCode,
			&transaction.ContainerID,
			&transaction.ContainerCode,
			&transaction.ContainerType,
			&transaction.CartID,
			&transaction.CartCode,
			&transaction.ToteID,
			&transaction.ToteCode,
			&transaction.CurrentTrackedQty,
			&transaction.BaseUnit,
		)
		if err != nil {
			return errors.Wrap(err, "unable to read resultant rows into inventory schema", &errors.DBError)
		}

		transactionProto.Sku = transaction.SKU
		transactionProto.ItemId = transaction.ItemID
		transactionProto.Action = transaction.Action
		transactionProto.Change = strconv.Itoa(transaction.Change)
		transactionProto.Balance = strconv.Itoa(transaction.Current)
		transactionProto.OperationDoneAt = transaction.ChangedAt.Format("2006-01-02 15:04:05")
		transactionProto.OperationBy = transaction.ChangedByUsername
		transactionProto.BaseUnit = transaction.BaseUnit
		if transaction.LocationCode != nil {
			transactionProto.LocationCode = *transaction.LocationCode
		}
		if transaction.ContainerType != nil && transaction.ContainerCode != nil && *transaction.ContainerType != "loose" {
			transactionProto.LpCode = *transaction.ContainerCode
			transactionProto.LpType = *transaction.ContainerType
		}
		if transaction.BatchNumber != nil {
			transactionProto.BatchNumber = *transaction.BatchNumber
		}
		if transaction.SerialNumber != nil {
			transactionProto.SerialNumber = *transaction.SerialNumber
		}
		if transaction.ExpirationDate != nil {
			transactionProto.ExpirationDate = transaction.ExpirationDate.Format("2006-01-02 15:04:05")
		}
		if transaction.Source != nil {
			transactionProto.Source = *transaction.Source
		}
		if transaction.Document != nil {
			transactionProto.Type = *transaction.Document
		}
		if transaction.DocumentID != nil {
			transactionProto.DocumentId = *transaction.DocumentID
		}
		if transaction.DocumentCode != nil {
			transactionProto.DocumentCode = *transaction.DocumentCode
		}
		if transaction.ClientName != nil {
			transactionProto.ClientName = *transaction.ClientName
		}
		if transaction.DocumentPrn != nil {
			transactionProto.DocumentPrn = *transaction.DocumentPrn
		}

		// # Append row to chunk list
		chunk = append(chunk, &transactionProto)
		// # Increment chunk counter
		chunkCounter++

		// # Send report chunk to stream
		// # Stream chunk if chunk size is reached
		if chunkCounter == chunkSize {
			err = stream.Send(&inventory_proto.WarehouseOperationsReportStreamResponse{
				Response: &inventory_proto.WarehouseOperationsReportStreamResponse_Chunk{
					Chunk: &inventory_proto.WarehouseOperationsReportDataResponseList{
						Data: chunk,
					},
				},
			})
			if err != nil {
				return errors.Wrap(err, "failed to stream report chunk", &errors.DBError)
			}
			// # Clear the chunk list
			chunk = nil
			// # Reset the chunk counter
			chunkCounter = 0
		}
	}

	// # Stream remaining data if any left in chunk list
	if chunkCounter > 0 {
		err = stream.Send(&inventory_proto.WarehouseOperationsReportStreamResponse{
			Response: &inventory_proto.WarehouseOperationsReportStreamResponse_Chunk{
				Chunk: &inventory_proto.WarehouseOperationsReportDataResponseList{
					Data: chunk,
				},
			},
		})
		if err != nil {
			return errors.Wrap(err, "failed to stream remaining report chunk", &errors.DBError)
		}
		// # Clear the chunk list
		chunk = nil
		// # Reset the chunk counter
		chunkCounter = 0
	}

	if row_err := rows.Err(); row_err != nil {
		return errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var resultCount, totalPages int

	countSelectStmt := `
		SELECT COUNT(t.item_id)
		FROM transaction t
		INNER JOIN inventory ON t.item_id = inventory.item_id
	`
	countQuery := countSelectStmt + " " + where

	err = s.DB.QueryRow(ctx, countQuery, countParams...).Scan(&resultCount)
	if err != nil {
		return errors.Wrap(err, "unable to get count of transaction rows", &errors.DBError)
	}
	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	// # Send report metadata to stream
	if err := stream.Send(&inventory_proto.WarehouseOperationsReportStreamResponse{
		Response: &inventory_proto.WarehouseOperationsReportStreamResponse_Metadata{
			Metadata: &inventory_proto.WarehouseOperationsReportMetaDataResponse{
				TotalDocuments: int32(resultCount),
				TotalPages:     int32(totalPages),
				CurrentPage:    int32(*pageNo),
			},
		},
	}); err != nil {
		return errors.Wrap(err, "failed to stream report metadata", &errors.DBError)
	}

	return nil
}

// # FSN Report -> stream the report 'data' and 'metadata'
func (s *Grpc) FSNReport(req *inventory_proto.FSNReportRequest, stream inventory_proto.Inventory_FSNReportServer) error {
	ctx := stream.Context() // # Get the 'stream' context
	var query string
	var rows pgx.Rows
	var err error
	var pageNo, pageSize *int
	var offset *int64
	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	currentTime := time.Now().UTC()
	days := int(req.GetDays())
	// If time range is not sent, default to last 3 months
	endDate := currentTime
	startDate := currentTime.AddDate(0, 0, -days)
	startOfDay := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, startDate.Location())

	// Query for opening balance on transaction table for each SKU
	if len(req.GetClients()) > 0 {
		query = `SELECT DISTINCT ON (item_id) item_id, current FROM transaction WHERE warehouse_id = $1 AND client_id = ANY($2) AND changed_at < $3  ORDER BY item_id,id DESC`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetClients(), startOfDay)
	} else {
		query = `SELECT DISTINCT ON (item_id) item_id, current FROM transaction WHERE warehouse_id = $1 AND changed_at < $2 ORDER BY item_id,id DESC`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), startOfDay)
	}
	if err == pgx.ErrNoRows {
		return errors.Wrap(err, "transaction doesn't exist for opening balance", &errors.DBError)
	}
	if err != nil {
		return errors.Wrap(err, "unable to read row for opening balance", &errors.DBError)
	}
	sku_ob_map := make(map[string]int)
	for rows.Next() {
		var item_id string
		var openingBalance int
		err := rows.Scan(&item_id, &openingBalance)
		if err != nil {
			return errors.Wrap(err, "unable to read resultant rows into transaction schema for opening balance", &errors.DBError)
		}
		sku_ob_map[item_id] = openingBalance
	}
	if row_err := rows.Err(); row_err != nil {
		return errors.Wrap(row_err, "unable to read rows for opening balance", &errors.DBError)
	}

	// Query for closing balance on transaction table for each SKU
	if len(req.GetClients()) > 0 {
		query = `SELECT DISTINCT ON (item_id) item_id, current FROM transaction WHERE warehouse_id = $1 AND client_id = ANY($2) AND changed_at < $3  ORDER BY item_id,id DESC`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetClients(), endDate)
	} else {
		query = `SELECT DISTINCT ON (item_id) item_id, current FROM transaction WHERE warehouse_id = $1 AND changed_at < $2 ORDER BY item_id,id DESC`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), endDate)
	}
	if err == pgx.ErrNoRows {
		return errors.Wrap(err, "transaction doesn't exist for closing balance", &errors.DBError)
	}
	if err != nil {
		return errors.Wrap(err, "unable to read row for closing balance", &errors.DBError)
	}
	sku_cb_map := make(map[string]int)
	for rows.Next() {
		var item_id string
		var closingBalance int
		err := rows.Scan(&item_id, &closingBalance)
		if err != nil {
			return errors.Wrap(err, "unable to read resultant rows into transaction schema for closing balance", &errors.DBError)
		}
		sku_cb_map[item_id] = closingBalance
	}
	if row_err := rows.Err(); row_err != nil {
		return errors.Wrap(row_err, "unable to read rows for closing balance", &errors.DBError)
	}

	// Query for received on transaction table for each SKU
	if len(req.GetClients()) > 0 {
		query = `SELECT item_id, SUM(change) AS received FROM transaction WHERE warehouse_id = $1 AND client_id = ANY($2) AND changed_at >= $3 AND changed_at <= $4 AND action = $5 GROUP BY item_id`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetClients(), startOfDay, endDate, "arn_fulfillment")
	} else {
		query = `SELECT item_id, SUM(change) AS received FROM transaction WHERE warehouse_id = $1 AND changed_at >= $2 AND changed_at <= $3 AND action = $4 GROUP BY item_id`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), startOfDay, endDate, "arn_fulfillment")
	}
	if err == pgx.ErrNoRows {
		return errors.Wrap(err, "transaction doesn't exist for received", &errors.DBError)
	}
	if err != nil {
		return errors.Wrap(err, "unable to read row for received", &errors.DBError)
	}
	sku_received_map := make(map[string]int)
	for rows.Next() {
		var item_id string
		var received int
		err := rows.Scan(&item_id, &received)
		if err != nil {
			return errors.Wrap(err, "unable to read resultant rows into transaction schema for received", &errors.DBError)
		}
		sku_received_map[item_id] = received
	}
	if row_err := rows.Err(); row_err != nil {
		return errors.Wrap(row_err, "unable to read rows for received", &errors.DBError)
	}

	// Query for issued on transaction table for each SKU
	if len(req.GetClients()) > 0 {
		query = `SELECT item_id, SUM(change) AS issued FROM transaction WHERE warehouse_id = $1 AND client_id = ANY($2) AND changed_at >= $3 AND changed_at <= $4 AND action = $5 GROUP BY item_id`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetClients(), startOfDay, endDate, "item_pickup")
	} else {
		query = `SELECT item_id, SUM(change) AS issued FROM transaction WHERE warehouse_id = $1 AND changed_at >= $2 AND changed_at <= $3 AND action = $4 GROUP BY item_id`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), startOfDay, endDate, "item_pickup")
	}
	if err == pgx.ErrNoRows {
		return errors.Wrap(err, "transaction doesn't exist for issued", &errors.DBError)
	}
	if err != nil {
		return errors.Wrap(err, "unable to read row for issued", &errors.DBError)
	}
	sku_issued_map := make(map[string]int)
	for rows.Next() {
		var item_id string
		var issued int
		err := rows.Scan(&item_id, &issued)
		if err != nil {
			return errors.Wrap(err, "unable to read resultant rows into transaction schema for issued", &errors.DBError)
		}
		sku_issued_map[item_id] = int(math.Abs(float64(issued)))
	}
	if row_err := rows.Err(); row_err != nil {
		return errors.Wrap(row_err, "unable to read rows for issued", &errors.DBError)
	}

	// Query to fetch inventory data for each SKU
	if len(req.GetClients()) > 0 {
		query = `SELECT client_name, item_id, sku FROM inventory WHERE warehouse_id = $1 AND client_id = ANY($2) ORDER BY id DESC LIMIT $3 OFFSET $4`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetClients(), pageSize, offset)
	} else {
		query = `SELECT client_name, item_id, sku FROM inventory WHERE warehouse_id = $1 ORDER BY id DESC LIMIT $2 OFFSET $3`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), pageSize, offset)
	}
	if err == pgx.ErrNoRows {
		return errors.Wrap(err, "inventory doesn't exist", &errors.DBError)
	}
	if err != nil {
		return errors.Wrap(err, "unable to read row for inventory", &errors.DBError)
	}
	defer rows.Close()

	var fsnReport []model.FSNReport

	for rows.Next() {
		var client_name, item_id, sku string
		err := rows.Scan(&client_name, &item_id, &sku)
		if err != nil {
			return errors.Wrap(err, "unable to read resultant rows into inventory schema", &errors.DBError)
		}
		fsn := model.FSNReport{
			ClientName:     client_name,
			ItemID:         item_id,
			SKU:            sku,
			OpeningBalance: sku_ob_map[item_id],
			ClosingBalance: sku_cb_map[item_id],
			Received:       sku_received_map[item_id],
			Issued:         sku_issued_map[item_id],
			Duration:       days,
		}
		if fsn.OpeningBalance > 0 || fsn.Received > 0 {
			fsn.AverageStay = float64(days) / (float64(sku_ob_map[item_id] + sku_received_map[item_id]))
			fsn.ConsumptionRate = float64(sku_issued_map[item_id]) / float64(days)
		}
		fsnReport = append(fsnReport, fsn)
	}
	if row_err := rows.Err(); row_err != nil {
		return errors.Wrap(row_err, "unable to read rows for inventory", &errors.DBError)
	}

	var resultCount, totalPages int
	if len(req.GetClients()) > 0 {
		query := `SELECT COUNT(id) FROM inventory WHERE warehouse_id = $1 AND client_id = ANY($2)`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), req.GetClients()).Scan(&resultCount)
	} else {
		query := `SELECT COUNT(id) FROM inventory WHERE warehouse_id = $1`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId()).Scan(&resultCount)
	}
	if err != nil {
		return errors.Wrap(err, "unable to get count of inventory rows", &errors.DBError)
	}

	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	// #---------------------------------------------------------------------------------------#
	//  								# Commented Out Code #
	// #---------------------------------------------------------------------------------------#
	// sort.SliceStable(fsnReport, func(i, j int) bool {
	// 	return fsnReport[i].AverageStay > fsnReport[j].AverageStay
	// })
	// sumAvgStay, TotalAvgStay := 0.0, 0.0
	// for i, v := range fsnReport {
	// 	sumAvgStay = sumAvgStay + v.AverageStay
	// 	fsnReport[i].CumulativeAverageStay = sumAvgStay
	// }
	// for _, v := range fsnReport {
	// 	TotalAvgStay = TotalAvgStay + v.CumulativeAverageStay
	// }
	// for i, v := range fsnReport {
	// 	fsnReport[i].PercentAverageStay = (v.CumulativeAverageStay / TotalAvgStay) * 100
	// }
	// #---------------------------------------------------------------------------------------#
	// if v.CumulativeAverageStay <= 10 {
	// 	category = "Fast Moving"
	// } else if v.CumulativeAverageStay > 10 && v.CumulativeAverageStay <= 30 {
	// 	category = "Slow Moving"
	// } else if v.CumulativeAverageStay > 30 && v.CumulativeAverageStay <= 100 {
	// 	category = "Non Moving"
	// }
	// #---------------------------------------------------------------------------------------#

	// # gRPC Server Streaming Optimization #
	// # Chunk Size
	const chunkSize = 10000

	// # Chunk List
	var chunk []*inventory_proto.FSNReportDataResponse

	// # Chunk Counter
	var chunkCounter int = 0

	for _, v := range fsnReport {
		var category string
		var turnoverRatio float64

		avgBal := float64((v.OpeningBalance + v.ClosingBalance) / 2)
		sales := math.Abs(float64((v.OpeningBalance - v.ClosingBalance) + v.Issued))
		if avgBal > 0.0 {
			turnoverRatio = sales / avgBal
			if turnoverRatio >= 0.0 && turnoverRatio <= 1.0 {
				category = "Non Moving"
			}
			if turnoverRatio > 1.0 && turnoverRatio <= 3.0 {
				category = "Slow Moving"
			}
			if turnoverRatio > 3.0 {
				category = "Fast Moving"
			}
		} else {
			turnoverRatio = 0.0
			category = "Non Moving"
		}

		fsnReportProto := inventory_proto.FSNReportDataResponse{
			ClientName:     v.ClientName,
			ItemId:         v.ItemID,
			Sku:            v.SKU,
			OpeningBalance: strconv.FormatFloat(float64(v.OpeningBalance), 'f', 2, 64),
			ClosingBalance: strconv.FormatFloat(float64(v.ClosingBalance), 'f', 2, 64),
			AverageBalance: strconv.FormatFloat(avgBal, 'f', 2, 64),
			Sales:          strconv.FormatFloat(sales, 'f', 2, 64),
			TurnoverRatio:  strconv.FormatFloat(turnoverRatio, 'f', 2, 64),
			Category:       category,
		}

		// # Append the 'row' into the 'chunk' list
		chunk = append(chunk, &fsnReportProto)

		// # Increment the 'chunk' counter
		chunkCounter++

		// # Send the report 'chunk' to the 'stream'
		// # Stream the 'chunk' if the chunk 'size' is reached
		if chunkCounter == chunkSize {
			err = stream.Send(&inventory_proto.FSNReportStreamResponse{
				Response: &inventory_proto.FSNReportStreamResponse_Chunk{
					Chunk: &inventory_proto.FSNReportDataResponseList{
						Data: chunk,
					},
				},
			})
			if err != nil {
				return errors.Wrap(err, "failed to stream the report chunk", &errors.DBError)
			}

			// # Clear the 'chunk' list
			chunk = nil

			// # Reset the 'chunk' counter
			chunkCounter = 0
		}
	}

	// # Stream the remaining report 'data' if any 'left' in the 'chunk' list
	if chunkCounter > 0 {
		err = stream.Send(&inventory_proto.FSNReportStreamResponse{
			Response: &inventory_proto.FSNReportStreamResponse_Chunk{
				Chunk: &inventory_proto.FSNReportDataResponseList{
					Data: chunk,
				},
			},
		})
		if err != nil {
			return errors.Wrap(err, "failed to stream the remaining report chunk", &errors.DBError)
		}

		// # Clear the 'chunk' list
		chunk = nil

		// # Reset the 'chunk' counter
		chunkCounter = 0
	}

	// # Send the report 'metadata' to the 'stream'
	err = stream.Send(&inventory_proto.FSNReportStreamResponse{
		Response: &inventory_proto.FSNReportStreamResponse_Metadata{
			Metadata: &inventory_proto.FSNReportMetaDataResponse{
				TotalDocuments: int32(resultCount),
				TotalPages:     int32(totalPages),
				CurrentPage:    int32(*pageNo),
			},
		},
	})
	if err != nil {
		return errors.Wrap(err, "failed to stream the report metadata", &errors.DBError)
	}

	return nil
}

// # LP Report -> stream the report 'data' and 'metadata'
func (s *Grpc) LPReport(req *inventory_proto.LPReportRequest, stream inventory_proto.Inventory_LPReportServer) error {
	ctx := stream.Context() // # Get the 'stream' context
	var query string
	var rows pgx.Rows
	var err error
	var pageNo, pageSize *int
	var offset *int64
	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	if len(req.GetClients()) > 0 {
		if len(req.GetItems()) > 0 {
			query = `SELECT c.client_name, c.container_id, c.code AS container_code, ct.name as container_type, c.ref_no, l.code AS location_code, l.on_hold, c.item_id, c.sku, c.batch_number, c.serial_number, c.expiration_date, c.quantity, c.initial_qty, c.received_at, i.base_unit, i.scannable, i.name, i.description
			FROM container_wise_inventory c
			FULL OUTER JOIN (
				SELECT DISTINCT ON (location_id) *
				FROM location_wise_inventory
			) AS l ON l.location_id = c.location_id
			INNER JOIN container_type ct on ct.id = c.container_type_id
			FULL OUTER JOIN inventory i on i.item_id = c.item_id
			WHERE c.warehouse_id= $1 AND c.client_id = ANY($2) AND c.item_id = ANY($3) AND c.quantity > 0 AND ct.name != 'loose'
			ORDER BY c.code ASC LIMIT $4 OFFSET $5`
			rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetClients(), req.GetItems(), pageSize, offset)
		} else {
			query = `SELECT c.client_name, c.container_id, c.code AS container_code, ct.name as container_type, c.ref_no, l.code AS location_code, l.on_hold, c.item_id, c.sku, c.batch_number, c.serial_number, c.expiration_date, c.quantity, c.initial_qty, c.received_at, i.base_unit, i.scannable, i.name, i.description
				FROM container_wise_inventory c
				FULL OUTER JOIN (
					SELECT DISTINCT ON (location_id) *
					FROM location_wise_inventory
				) AS l ON l.location_id = c.location_id
				INNER JOIN container_type ct on ct.id = c.container_type_id
				FULL OUTER JOIN inventory i on i.item_id = c.item_id
				WHERE c.warehouse_id= $1 AND c.client_id = ANY($2) AND c.quantity > 0 AND ct.name != 'loose'
				ORDER BY c.code ASC LIMIT $3 OFFSET $4`
			rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetClients(), pageSize, offset)
		}
	} else {
		if len(req.GetItems()) > 0 {
			query = `SELECT c.client_name, c.container_id, c.code AS container_code, ct.name as container_type, c.ref_no, l.code AS location_code, l.on_hold, c.item_id, c.sku, c.batch_number, c.serial_number, c.expiration_date, c.quantity, c.initial_qty, c.received_at, i.base_unit, i.scannable, i.name, i.description
				FROM container_wise_inventory c
				FULL OUTER JOIN (
					SELECT DISTINCT ON (location_id) *
					FROM location_wise_inventory
				) AS l ON l.location_id = c.location_id
				INNER JOIN container_type ct on ct.id = c.container_type_id
				FULL OUTER JOIN inventory i on i.item_id = c.item_id
				WHERE c.warehouse_id = $1 AND c.item_id = ANY($2) AND c.quantity > 0 AND ct.name != 'loose'
				ORDER BY c.code ASC LIMIT $3 OFFSET $4`
			rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetItems(), pageSize, offset)
		} else {
			query = `SELECT c.client_name, c.container_id, c.code AS container_code, ct.name as container_type, c.ref_no, l.code AS location_code, l.on_hold, c.item_id, c.sku, c.batch_number, c.serial_number, c.expiration_date, c.quantity, c.initial_qty, c.received_at, i.base_unit, i.scannable, i.name, i.description
				FROM container_wise_inventory c
				FULL OUTER JOIN (
					SELECT DISTINCT ON (location_id) *
					FROM location_wise_inventory
				) AS l ON l.location_id = c.location_id
				INNER JOIN container_type ct on ct.id = c.container_type_id
				FULL OUTER JOIN inventory i on i.item_id = c.item_id
				WHERE c.warehouse_id = $1 AND c.quantity > 0 AND ct.name != 'loose'
				ORDER BY c.code ASC LIMIT $2 OFFSET $3`
			rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), pageSize, offset)
		}
	}
	if err != nil {
		return errors.Wrap(err, "failed to query container wise inventory", &errors.DBError)
	}

	defer rows.Close()

	// # gRPC Server Streaming Optimization #
	// # Chunk Size
	const chunkSize = 10000

	// # Chunk List
	var chunk []*inventory_proto.LPReportDataResponse

	// # Chunk Counter
	var chunkCounter int = 0

	for rows.Next() {
		var inventory model.ContainerReportData
		var inventoryProto inventory_proto.LPReportDataResponse
		err := rows.Scan(
			&inventory.ClientName,
			&inventory.ContainerID,
			&inventory.ContainerCode,
			&inventory.ContainerType,
			&inventory.RefNo,
			&inventory.LocationCode,
			&inventory.OnHold,
			&inventory.ItemID,
			&inventory.Sku,
			&inventory.BatchNumber,
			&inventory.SerialNumber,
			&inventory.ExpirationDate,
			&inventory.Quantity,
			&inventory.InitialQty,
			&inventory.ReceivedAt,
			&inventory.BaseUnit,
			&inventory.Scannable,
			&inventory.Name,
			&inventory.Description,
		)
		if err != nil {
			return errors.Wrap(err, "unable to read resultant rows into inventory schema", &errors.DBError)
		}

		inventoryProto.ContainerId = inventory.ContainerID.String()
		inventoryProto.ContainerCode = inventory.ContainerCode
		inventoryProto.ContainerQr = "LP/" + inventory.ContainerID.String() + "/" + inventory.ContainerCode

		inventoryProto.ContainerType = inventory.ContainerType
		inventoryProto.Quantity = inventory.Quantity
		inventoryProto.InitialQty = inventory.InitialQty
		if inventory.ClientName != nil {
			inventoryProto.ClientName = *inventory.ClientName
		}
		if inventory.LocationCode != nil {
			inventoryProto.LocationCode = *inventory.LocationCode
		}
		if inventory.ItemID != nil {
			inventoryProto.ItemId = *inventory.ItemID
		}
		if inventory.Sku != nil {
			inventoryProto.Sku = *inventory.Sku
		}
		if inventory.Scannable != nil {
			inventoryProto.Scannable = *inventory.Scannable
		}
		if inventory.Name != nil {
			inventoryProto.Name = *inventory.Name
		}
		if inventory.Description != nil {
			inventoryProto.Description = *inventory.Description
		}
		if inventory.BatchNumber != nil {
			inventoryProto.BatchNumber = *inventory.BatchNumber
		}
		if inventory.SerialNumber != nil {
			inventoryProto.SerialNumber = *inventory.SerialNumber
		}
		if inventory.ExpirationDate != nil {
			inventoryProto.ExpirationDate = inventory.ExpirationDate.Format("2006-01-02")
		}
		if inventory.BaseUnit != nil {
			inventoryProto.BaseUnit = *inventory.BaseUnit
		}
		if inventory.RefNo != nil {
			inventoryProto.RefNo = *inventory.RefNo
		}
		if inventory.OnHold != nil {
			inventoryProto.OnHold = strconv.FormatBool(*inventory.OnHold)
		} else {
			inventoryProto.OnHold = "false"
		}

		if inventory.ReceivedAt != nil {
			inventoryProto.ReceivedAt = inventory.ReceivedAt.Format("2006-01-02")
		}

		// # Append the 'row' into the 'chunk' list
		chunk = append(chunk, &inventoryProto)

		// # Increment the 'chunk' counter
		chunkCounter++

		// # Send the report 'chunk' to the 'stream'
		// # Stream the 'chunk' if the chunk 'size' is reached
		if chunkCounter == chunkSize {
			err = stream.Send(&inventory_proto.LPReportStreamResponse{
				Response: &inventory_proto.LPReportStreamResponse_Chunk{
					Chunk: &inventory_proto.LPReportDataResponseList{
						Data: chunk,
					},
				},
			})
			if err != nil {
				return errors.Wrap(err, "failed to stream the report chunk", &errors.DBError)
			}

			// # Clear the 'chunk' list
			chunk = nil

			// # Reset the 'chunk' counter
			chunkCounter = 0
		}
	}

	// # Stream the remaining report 'data' if any 'left' in the 'chunk' list
	if chunkCounter > 0 {
		err = stream.Send(&inventory_proto.LPReportStreamResponse{
			Response: &inventory_proto.LPReportStreamResponse_Chunk{
				Chunk: &inventory_proto.LPReportDataResponseList{
					Data: chunk,
				},
			},
		})
		if err != nil {
			return errors.Wrap(err, "failed to stream the remaining report chunk", &errors.DBError)
		}

		// # Clear the 'chunk' list
		chunk = nil

		// # Reset the 'chunk' counter
		chunkCounter = 0
	}

	if row_err := rows.Err(); row_err != nil {
		return errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var resultCount, totalPages int
	if len(req.GetClients()) > 0 {
		if len(req.GetItems()) > 0 {
			query := `SELECT COUNT(c.container_id)
				FROM container_wise_inventory c
				FULL OUTER JOIN (
					SELECT DISTINCT ON (location_id) *
					FROM location_wise_inventory
				) AS l ON l.location_id = c.location_id
				INNER JOIN container_type ct on ct.id = c.container_type_id
				FULL OUTER JOIN inventory i on i.item_id = c.item_id
				WHERE c.warehouse_id= $1 AND c.client_id = ANY($2) AND c.item_id = ANY($3) AND c.quantity > 0 AND ct.name != 'loose'`
			err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), req.GetClients(), req.GetItems()).Scan(&resultCount)
		} else {
			query := `SELECT COUNT(c.container_id)
				FROM container_wise_inventory c
				FULL OUTER JOIN (
					SELECT DISTINCT ON (location_id) *
					FROM location_wise_inventory
				) AS l ON l.location_id = c.location_id
				INNER JOIN container_type ct on ct.id = c.container_type_id
				FULL OUTER JOIN inventory i on i.item_id = c.item_id
				WHERE c.warehouse_id= $1 AND c.client_id = ANY($2) AND c.quantity > 0 AND ct.name != 'loose'`
			err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), req.GetClients()).Scan(&resultCount)
		}
	} else {
		if len(req.GetItems()) > 0 {
			query := `SELECT COUNT(c.container_id)
				FROM container_wise_inventory c
				FULL OUTER JOIN (
					SELECT DISTINCT ON (location_id) *
					FROM location_wise_inventory
				) AS l ON l.location_id = c.location_id
				INNER JOIN container_type ct on ct.id = c.container_type_id
				FULL OUTER JOIN inventory i on i.item_id = c.item_id
				WHERE c.warehouse_id = $1 AND c.item_id = ANY($2) AND c.quantity > 0 AND ct.name != 'loose'`
			err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), req.GetItems()).Scan(&resultCount)
		} else {
			query := `SELECT COUNT(c.container_id)
				FROM container_wise_inventory c
				FULL OUTER JOIN (
					SELECT DISTINCT ON (location_id) *
					FROM location_wise_inventory
				) AS l ON l.location_id = c.location_id
				INNER JOIN container_type ct on ct.id = c.container_type_id
				FULL OUTER JOIN inventory i on i.item_id = c.item_id
				WHERE c.warehouse_id = $1 AND c.quantity > 0 AND ct.name != 'loose'`
			err = s.DB.QueryRow(ctx, query, req.GetWarehouseId()).Scan(&resultCount)
		}
	}
	if err != nil {
		return errors.Wrap(err, "unable to get count of inventory rows", &errors.DBError)
	}

	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	// # Send the report 'metadata' to the 'stream'
	err = stream.Send(&inventory_proto.LPReportStreamResponse{
		Response: &inventory_proto.LPReportStreamResponse_Metadata{
			Metadata: &inventory_proto.LPReportMetaDataResponse{
				TotalDocuments: int32(resultCount),
				TotalPages:     int32(totalPages),
				CurrentPage:    int32(*pageNo),
			},
		},
	})
	if err != nil {
		return errors.Wrap(err, "failed to stream the report metadata", &errors.DBError)
	}

	return nil
}

func (s *Grpc) UpdateAvailableQtyForWO(ctx context.Context, req *inventory_proto.UpdateAvailableQtyForWORequest) (*inventory_proto.UpdateAvailableQtyForWOResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	for _, item := range req.GetAllocatedInventory() {
		query := `UPDATE inventory SET available_qty = available_qty - $1 WHERE item_id = $2` // [TAP] - Only updating available quantity
		commandTag, err := tx.Exec(ctx, query, item.GetAlloatedQty(), item.GetItemId())
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Unable to update inventory", &errors.DBError)
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return nil, errors.New("Inventory wasn't updated", &errors.DBError)
		}

		go s.App.Operations.UpdateShopifyInventory(item.GetItemId())
	}

	// updating work order
	updateResp, err := s.App.GrpcClient.Core.Client.UpdateWorkOrder(ctx, &core_proto.UpdateWorkOrderRequest{WorkOrder: req.GetWorkOrder()})
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to request for update work order", &errors.DBError)
	}
	if !updateResp.GetSuccess() {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to update work order", &errors.DBError)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to commit transaction, rolling back", &errors.DBError)
	}

	return &inventory_proto.UpdateAvailableQtyForWOResponse{
		Success: true,
	}, nil
}

func (s *Grpc) UpdateInventoryForWorkOrder(ctx context.Context, req *inventory_proto.UpdateInventoryForWorkOrderRequest) (*inventory_proto.UpdateInventoryForWorkOrderResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)
	var containerInfo []*outbound_proto.UpdatePicklistForWORequest_ContainerInfo
	for _, item := range req.GetItemInfo() {
		// Get item from inventory
		itemInfo, err := s.App.Operations.GetItemFromInventoryByItemId(item.GetItemId())
		if err != nil {
			return nil, err
		}
		if itemInfo.PickableQty < item.GetQuantity() {
			return nil, errors.New("Not enough quantity to pick!!", &errors.DBError)
		}

		//checking if the item is tracked or not
		if itemInfo.IsBatchControlled || itemInfo.IsTrackedBySerialNo || itemInfo.IsPerishable {
			// Get pickable tracked items whose sum >= required qty
			trackedCounter := 0
			var trackedInventory []model.TrackedInventory
			query := `SELECT item_id, batch_number, serial_number, expiration_date, pickable_qty FROM tracked_inventory WHERE item_id = $1 ORDER BY pickable_qty DESC, expiration_date ASC`
			rows, err := tx.Query(context.TODO(), query, item.GetItemId())
			if err != nil {
				return nil, errors.Wrap(err, "Failed to query tracked inventory", &errors.DBError)
			}
			defer rows.Close()
			for rows.Next() {
				if int32(trackedCounter) >= item.GetQuantity() {
					rows.Close()
					break
				}
				var tracInv model.TrackedInventory
				err := rows.Scan(
					&tracInv.ItemID,
					&tracInv.BatchNumber,
					&tracInv.SerialNumber,
					&tracInv.ExpirationDate,
					&tracInv.PickableQty,
				)
				if err != nil {
					return nil, errors.Wrap(err, "Unable to read resultant rows into tracked inventory schema", &errors.DBError)
				}
				trackedCounter += tracInv.PickableQty
				trackedInventory = append(trackedInventory, tracInv)
			}
			if row_err := rows.Err(); row_err != nil {
				return nil, errors.Wrap(row_err, "Unable to read rows", &errors.DBError)
			}

			// Fetch location rows of item with tracked info whose sum >= required qty
			locationQtyCounter := int32(0)
			var locations []model.Location
			for _, inv := range trackedInventory {
				query := `SELECT location_id, item_id, batch_number, serial_number, expiration_date, quantity FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL) AND on_hold = $5 ORDER BY expiration_date ASC`
				rows, err := tx.Query(context.TODO(), query, inv.ItemID, inv.BatchNumber, inv.SerialNumber, inv.ExpirationDate, false)
				if err != nil {
					return nil, errors.Wrap(err, "Failed to query location_wise_inventory for tracked item.", &errors.DBError)
				}
				defer rows.Close()
				for rows.Next() {
					if int32(locationQtyCounter) >= item.GetQuantity() {
						rows.Close()
						break
					}
					var location model.Location
					err := rows.Scan(
						&location.LocationID,
						&location.ItemID,
						&location.BatchNumber,
						&location.SerialNumber,
						&location.ExpirationDate,
						&location.Quantity,
					)
					if err != nil {
						return nil, errors.Wrap(err, "Unable to read resultant rows into location schema", &errors.DBError)
					}
					locationQtyCounter += *location.Quantity
					locations = append(locations, location)
				}
				if row_err := rows.Err(); row_err != nil {
					return nil, errors.Wrap(row_err, "Unable to read rows", &errors.DBError)
				}
			}

			// Fetch container rows of item with tracked info whose sum >= required qty
			containerQtyCounter := int32(0)
			var containers []model.WorkOrderContainer
			for _, location := range locations {
				query := `SELECT c.container_id, c.location_id, c.code, c.item_id, c.batch_number, c.serial_number, c.expiration_date, c.quantity, c.sku, c.client_id, c.client_name, c.warehouse_id, l.code FROM container_wise_inventory c INNER JOIN location_wise_inventory l ON l.location_id = c.location_id WHERE c.item_id = $1 AND (c.batch_number = $2 OR $2 IS NULL) AND (c.serial_number = $3 OR $3 IS NULL) AND (c.expiration_date = $4 OR $4 IS NULL) AND c.location_id = $5 AND c.on_hold = $6 ORDER BY c.expiration_date ASC`
				rows, err := tx.Query(context.TODO(), query, location.ItemID, location.BatchNumber, location.SerialNumber, location.ExpirationDate, location.LocationID, false)
				if err != nil {
					return nil, errors.Wrap(err, "Failed to query container_wise_inventory for tracked item.", &errors.DBError)
				}
				defer rows.Close()
				for rows.Next() {
					if int32(containerQtyCounter) >= item.GetQuantity() {
						rows.Close()
						break
					}
					var container model.WorkOrderContainer
					err := rows.Scan(
						&container.ContainerID,
						&container.LocationID,
						&container.Code,
						&container.ItemID,
						&container.BatchNumber,
						&container.SerialNumber,
						&container.ExpirationDate,
						&container.Quantity,
						&container.SKU,
						&container.ClientID,
						&container.ClientName,
						&container.WarehouseID,
						&container.LocationCode,
					)
					if err != nil {
						return nil, errors.Wrap(err, "Unable to read resultant rows into container schema", &errors.DBError)
					}
					containerQtyCounter += container.Quantity
					containers = append(containers, container)
				}
				if row_err := rows.Err(); row_err != nil {
					return nil, errors.Wrap(row_err, "Unable to read rows", &errors.DBError)
				}
			}
			reqQty := item.GetQuantity()
			for _, container := range containers {
				var batchNo, serialNo string
				var qty int32
				var expDate *timestamppb.Timestamp
				if container.BatchNumber != nil {
					batchNo = *container.BatchNumber
				}
				if container.SerialNumber != nil {
					serialNo = *container.SerialNumber
				}
				if container.ExpirationDate != nil {
					expDate = timestamppb.New(*container.ExpirationDate)
				}
				if container.Quantity > reqQty {
					qty = reqQty
					reqQty = 0
				} else {
					qty = container.Quantity
					reqQty -= container.Quantity
				}
				// Call update location for pickup
				req := inventory_proto.UpdateLocationForPickUpRequest{
					LocationId:     container.LocationID,
					ContainerId:    container.ContainerID.String(),
					ItemId:         container.ItemID,
					BatchNumber:    batchNo,
					SerialNumber:   serialNo,
					ExpirationDate: expDate,
					Quantity:       qty,
					Username:       req.CreatedBy.Username,
					UserId:         req.CreatedBy.UserId,
					Sku:            container.SKU,
					ClientId:       container.ClientID,
					ClientName:     container.ClientName,
					WarehouseId:    container.WarehouseID,
					Source:         "self",
					Document:       "Work Order",
					DocumentCode:   req.GetWorkOrderCode(),
					DocumentId:     req.GetWorkOrderId(),
					RequestId:      req.GetRequestId(),
				}
				res, err := s.UpdateLocationForPickUp(ctx, &req)
				if err != nil {
					tx.Rollback(ctx)
					return nil, errors.Wrap(err, "Tracked items pickup failed", &errors.DBError)
				}
				if !res.GetSuccess() {
					tx.Rollback(ctx)
					return nil, errors.New("Tracked items pickup failed", &errors.DBError)
				}

				containerData := &outbound_proto.UpdatePicklistForWORequest_ContainerInfo{
					ContainerId:        container.ContainerID.String(),
					ContainerCode:      container.Code,
					LocationId:         container.LocationID,
					LocationCode:       container.LocationCode,
					ItemId:             container.ItemID,
					Quantity:           qty,
					BatchNumber:        batchNo,
					SerialNumber:       serialNo,
					Unit:               itemInfo.BaseUnit,
					BaseConversionRate: 1,
				}
				if container.ExpirationDate != nil {
					containerData.ExpirationDate = expDate
				}
				containerInfo = append(containerInfo, containerData)

				if reqQty == 0 {
					break
				}
			}
		} else {
			// Fetch location rows of item with tracked info whose sum >= required qty
			locationQtyCounter := int32(0)
			var locations []model.Location
			query := `SELECT location_id, item_id, quantity FROM location_wise_inventory WHERE item_id = $1 AND on_hold = $2 ORDER BY expiration_date ASC`
			rows, err := tx.Query(context.TODO(), query, item.GetItemId(), false)
			if err != nil {
				return nil, errors.Wrap(err, "Failed to query location_wise_inventory for non tracked item.", &errors.DBError)
			}
			defer rows.Close()
			for rows.Next() {
				if int32(locationQtyCounter) >= item.GetQuantity() {
					rows.Close()
					break
				}
				var location model.Location
				err := rows.Scan(
					&location.LocationID,
					&location.ItemID,
					&location.Quantity,
				)
				if err != nil {
					return nil, errors.Wrap(err, "Unable to read resultant rows into location schema", &errors.DBError)
				}
				locationQtyCounter += *location.Quantity
				locations = append(locations, location)
			}
			if row_err := rows.Err(); row_err != nil {
				return nil, errors.Wrap(row_err, "Unable to read rows", &errors.DBError)
			}

			// Fetch container rows of item with tracked info whose sum >= required qty
			containerQtyCounter := int32(0)
			var containers []model.WorkOrderContainer
			for _, location := range locations {
				query := `SELECT c.container_id, c.location_id, c.code, c.item_id, c.quantity, c.sku, c.client_id, c.client_name, c.warehouse_id, l.code FROM container_wise_inventory c INNER JOIN location_wise_inventory l ON l.location_id = c.location_id WHERE c.item_id = $1 AND (c.batch_number = $2 OR $2 IS NULL) AND (c.serial_number = $3 OR $3 IS NULL) AND (c.expiration_date = $4 OR $4 IS NULL) AND c.location_id = $5 AND c.on_hold = $6 ORDER BY c.expiration_date ASC`
				rows, err := tx.Query(context.TODO(), query, location.ItemID, location.BatchNumber, location.SerialNumber, location.ExpirationDate, location.LocationID, false)
				if err != nil {
					return nil, errors.Wrap(err, "Failed to query container_wise_inventory for non tracked item.", &errors.DBError)
				}
				defer rows.Close()
				for rows.Next() {
					if int32(containerQtyCounter) >= item.GetQuantity() {
						rows.Close()
						break
					}
					var container model.WorkOrderContainer
					err := rows.Scan(
						&container.ContainerID,
						&container.LocationID,
						&container.Code,
						&container.ItemID,
						&container.Quantity,
						&container.SKU,
						&container.ClientID,
						&container.ClientName,
						&container.WarehouseID,
						&container.LocationCode,
					)
					if err != nil {
						return nil, errors.Wrap(err, "Unable to read resultant rows into container schema", &errors.DBError)
					}
					containerQtyCounter += container.Quantity
					containers = append(containers, container)
				}
				if row_err := rows.Err(); row_err != nil {
					return nil, errors.Wrap(row_err, "Unable to read rows", &errors.DBError)
				}
			}
			reqQty := item.GetQuantity()
			for _, container := range containers {
				var batchNo, serialNo string
				var qty int32
				var expDate *timestamppb.Timestamp
				if container.BatchNumber != nil {
					batchNo = *container.BatchNumber
				}
				if container.SerialNumber != nil {
					serialNo = *container.SerialNumber
				}
				if container.ExpirationDate != nil {
					expDate = timestamppb.New(*container.ExpirationDate)
				}
				if container.Quantity > reqQty {
					qty = reqQty
					reqQty = 0
				} else {
					qty = container.Quantity
					reqQty = reqQty - container.Quantity
				}
				// Call update location for pickup
				req := inventory_proto.UpdateLocationForPickUpRequest{
					LocationId:     container.LocationID,
					ContainerId:    container.ContainerID.String(),
					ItemId:         container.ItemID,
					BatchNumber:    batchNo,
					SerialNumber:   serialNo,
					ExpirationDate: expDate,
					Quantity:       qty,
					Username:       req.CreatedBy.Username,
					UserId:         req.CreatedBy.UserId,
					Sku:            container.SKU,
					ClientId:       container.ClientID,
					ClientName:     container.ClientName,
					WarehouseId:    container.WarehouseID,
					Source:         "self",
					Document:       "Work Order",
					DocumentCode:   req.GetWorkOrderCode(),
					DocumentId:     req.GetWorkOrderId(),
					RequestId:      req.GetRequestId(),
				}
				res, err := s.UpdateLocationForPickUp(ctx, &req)
				if err != nil {
					tx.Rollback(ctx)
					return nil, errors.Wrap(err, "Non tracked items pickup failed", &errors.DBError)
				}
				if !res.GetSuccess() {
					tx.Rollback(ctx)
					return nil, errors.New("Non tracked items pickup failed", &errors.DBError)
				}

				containerData := &outbound_proto.UpdatePicklistForWORequest_ContainerInfo{
					ContainerId:        container.ContainerID.String(),
					ContainerCode:      container.Code,
					LocationId:         container.LocationID,
					LocationCode:       container.LocationCode,
					ItemId:             container.ItemID,
					Quantity:           qty,
					BatchNumber:        batchNo,
					SerialNumber:       serialNo,
					Unit:               itemInfo.BaseUnit,
					BaseConversionRate: 1,
				}
				if container.ExpirationDate != nil {
					containerData.ExpirationDate = expDate
				}
				containerInfo = append(containerInfo, containerData)
				if reqQty == 0 {
					break
				}
			}
		}
	}
	createdBy := &outbound_proto.CreatingUser{
		UserId:   req.GetCreatedBy().GetUserId(),
		Name:     req.GetCreatedBy().GetName(),
		Username: req.GetCreatedBy().GetUsername(),
		Email:    req.GetCreatedBy().GetEmail(),
	}
	UpdatePicklistReq := &outbound_proto.UpdatePicklistForWORequest{
		ContainerInfo: containerInfo,
		PicklistId:    req.GetPicklistId(),
		CreatedBy:     createdBy,
	}
	resp, err := s.App.GrpcClient.Outbound.Client.UpdatePicklistForWO(ctx, UpdatePicklistReq)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to request for update picklist", &errors.DBError)
	}
	if !resp.GetSuccess() {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to update picklist", &errors.DBError)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to commit transaction, rolling back", &errors.DBError)
	}

	return &inventory_proto.UpdateInventoryForWorkOrderResponse{
		Success: true,
	}, nil
}

func (s *Grpc) DeleteItem(ctx context.Context, req *inventory_proto.DeleteItemRequest) (*inventory_proto.DeleteItemResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	query := `DELETE FROM inventory WHERE item_id = $1 AND quantity = $2 AND available_qty = $2 AND pickable_qty = $2`
	commandTag, err := tx.Exec(ctx, query, req.GetItemId(), 0)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to delete item in inventory", &errors.DBError)
	}
	if commandTag.RowsAffected() != 1 {
		tx.Rollback(ctx)
		return nil, errors.New("item wasn't deleted", &errors.DBError)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to commit transaction, rolling back", &errors.DBError)
	}

	return &inventory_proto.DeleteItemResponse{
		Success: true,
	}, nil
}

// UpdateAssembledKitInventory updates inventory for assembled kit
func (s *Grpc) UpdateAssembledKitInventory(ctx context.Context, req *inventory_proto.UpdateAssembledKitInventoryRequest) (*inventory_proto.UpdateAssembledKitInventoryResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	// Check if location is on hold
	var locationOnHold bool
	var areaId string
	query := `SELECT on_hold, area_id FROM location_wise_inventory WHERE location_id = $1 LIMIT 1`
	err = tx.QueryRow(ctx, query, req.GetLocationId()).Scan(&locationOnHold, &areaId)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to fetch location details!", &errors.DBError)
	}
	if locationOnHold {
		tx.Rollback(ctx)
		return nil, errors.New("Location is on hold!", &errors.BadRequest)
	}

	// check if area is pickable and confirm the pickable quantity based on that
	getIsPickableRequest := core_proto.CheckAreaTypeRequest{
		AreaId:   areaId,
		Property: "picking",
	}
	areaInfo, err := s.App.GrpcClient.Core.Client.CheckAreaType(ctx, &getIsPickableRequest)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to identify area properties!", &errors.SomethingWentWrong)
	}

	var pickableQty int32
	if areaInfo.GetIsAreaPickable() {
		pickableQty = req.GetQuantity()
	} else {
		pickableQty = 0
	}

	var availableQty int32
	if areaInfo.GetIsAreaHold() {
		availableQty = 0
	} else {
		availableQty = req.GetQuantity()
	}

	// fetch loose container id from database using location id
	var containerID uuid.UUID
	var containerCode string
	// Get loose container type id
	type_id, err := s.App.Operations.GetContainerTypeID("loose", req.GetWarehouseId())
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Could not fetch loose container type id for this warehouse!", &errors.DBError)
	}
	// Get loose container from location
	query = `SELECT container_id, code FROM container_wise_inventory WHERE location_id = $1 AND container_type_id = $2 AND warehouse_id = $3 LIMIT 1`
	err = tx.QueryRow(context.TODO(), query, req.GetLocationId(), type_id, req.GetWarehouseId()).Scan(&containerID, &containerCode)
	if err != nil {
		if err == pgx.ErrNoRows {
			// Create an empty loose container on location
			// Previous container code
			prevCodeInt, err := s.App.Container.GetPreviousContainerCodeInt(req.GetWarehouseId())
			if err != nil {
				tx.Rollback(ctx)
				return &inventory_proto.UpdateAssembledKitInventoryResponse{
					Success: false,
				}, errors.Wrap(err, "Could not previous container code!", &errors.DBError)
			}
			code := "LP-" + strconv.Itoa(*prevCodeInt+1)
			container_id := uuid.NewV4()
			containerID = container_id
			containerCode = code

			query = `INSERT INTO container_wise_inventory (item_id, sku, batch_number, serial_number, expiration_date, scannable, container_id, container_type_id, location_id, warehouse_id, code, quantity, available_qty, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)`
			commandTag, err := tx.Exec(context.TODO(), query, nil, nil, nil, nil, nil, nil, container_id, type_id, req.GetLocationId(), req.GetWarehouseId(), code, 0, 0, time.Now())
			if err != nil {
				tx.Rollback(ctx)
				return &inventory_proto.UpdateAssembledKitInventoryResponse{
					Success: false,
				}, errors.Wrap(err, "Unable to insert new loose container in this location!", &errors.DBError)
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return &inventory_proto.UpdateAssembledKitInventoryResponse{
					Success: false,
				}, errors.New("New lose container wasn't inserted!", &errors.DBError)
			}

		} else {
			tx.Rollback(ctx)
			return &inventory_proto.UpdateAssembledKitInventoryResponse{
				Success: false,
			}, errors.Wrap(err, "Failed to get loose container from location!", &errors.DBError)
		}
	}

	// reporting data update request
	var auditAgeDataRequest reporting_proto.AuditInventoryUpdateRequest
	auditItem := reporting_proto.AuditItem{
		ItemId:     req.GetKitId(),
		LocationId: req.GetLocationId(),
		Quantity:   req.GetQuantity(),
	}

	var batchNo *string
	if req.GetTrackedInfo().GetBatchNumber() == "" {
		batchNo = nil
	} else {
		v := req.GetTrackedInfo().GetBatchNumber()
		batchNo = &v
		auditItem.BatchNumber = v
	}

	var serialNo *string
	if req.GetTrackedInfo().GetSingleSerialNumber() == "" {
		serialNo = nil
	} else {
		v := req.GetTrackedInfo().GetSingleSerialNumber()
		serialNo = &v
		auditItem.SerialNumber = v
	}

	var expDate *time.Time
	if req.GetTrackedInfo().GetExpirationDate() == nil {
		expDate = nil
	} else {
		v := req.GetTrackedInfo().GetExpirationDate().AsTime()
		expDate = &v
		auditItem.ExpirationDate = v.Format("2006-01-02")
	}

	auditAgeDataRequest.AuditItems = append(auditAgeDataRequest.AuditItems, &auditItem)

	// Get item from inventory
	inventory_item, err := s.App.Operations.GetItemFromInventory(req.GetKitId(), batchNo, serialNo, expDate)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to get item from inventory: "+req.GetKitId(), &errors.DBError)
	}

	// Update item on location
	// Check if the item exists on the location
	var exists bool
	query = `SELECT EXISTS(SELECT 1 FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND location_id = $4 AND warehouse_id = $5 AND (expiration_date = $6 OR $6 IS NULL)) AS "exists"`
	err = tx.QueryRow(ctx, query, inventory_item.ItemID, batchNo, serialNo, req.GetLocationId(), req.GetWarehouseId(), expDate).Scan(&exists)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to check if item exists on location!", &errors.DBError)
	}
	if exists {
		// Increment the quantity on the location since a item is getting added to a container on that location
		query := `UPDATE location_wise_inventory SET quantity = quantity + $1, available_qty = available_qty + $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)`
		commandTag, err := tx.Exec(ctx, query, req.GetQuantity(), inventory_item.ItemID, batchNo, serialNo, req.GetLocationId(), req.GetWarehouseId(), expDate)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Error incrementing item quantity on location!", &errors.DBError)
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return nil, errors.New("Item quantity wasn't incremented on the location!", &errors.DBError)
		}
	} else {
		// Item doesn't exist on the location, insert a new row
		err := s.App.Operations.InsertItemInLocation(tx, ctx, req.GetLocationId(), req.GetWarehouseId(), int(req.GetQuantity()), int(req.GetQuantity()), inventory_item, batchNo, serialNo, expDate)
		if err != nil {
			tx.Rollback(ctx)
			return nil, err
		}
	}

	// Update inventory
	query = `UPDATE inventory SET quantity = quantity + $1, available_qty = available_qty + $2, pickable_qty = pickable_qty + $3 WHERE item_id = $4`
	commandTag, err := tx.Exec(ctx, query, req.GetQuantity(), availableQty, pickableQty, inventory_item.ItemID)
	if err != nil {
		tx.Rollback(ctx)
		var e error
		e = errors.Wrap(err, "Unable to update inventory!", &errors.DBError)
		if err == pgx.ErrNoRows {
			e = errors.New("Item doesn't exist in the inventory!", &errors.DBError)
		}
		return nil, e
	}
	if commandTag.RowsAffected() != 1 {
		tx.Rollback(ctx)
		return nil, errors.New("Inventory wasn't updated!", &errors.DBError)
	}

	if batchNo != nil || serialNo != nil || expDate != nil {
		// Check if item_id, batch_number, serial_number, expiration_date exists in tracked inventory
		var tracked_item_exists bool
		query = `SELECT EXISTS(SELECT 1 FROM tracked_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL) AND warehouse_id = $5 AND client_id = $6) AS "exists"`
		err = tx.QueryRow(ctx, query, req.GetKitId(), batchNo, serialNo, expDate, req.GetWarehouseId(), req.GetClientId()).Scan(&tracked_item_exists)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Unable to check if item exists in tracked inventory!", &errors.DBError)
		}

		if tracked_item_exists {
			// Increment it's quantity and available quantity
			query := `UPDATE tracked_inventory SET quantity = quantity + $1, available_qty = available_qty + $2, pickable_qty = pickable_qty + $3 WHERE item_id = $4 AND (batch_number = $5 OR $5 IS NULL) AND (serial_number = $6 OR $6 IS NULL) AND (expiration_date = $7 OR $7 IS NULL) AND warehouse_id = $8 AND client_id = $9`
			commandTag, err := tx.Exec(ctx, query, req.GetQuantity(), availableQty, pickableQty, req.GetKitId(), batchNo, serialNo, expDate, req.GetWarehouseId(), req.GetClientId())
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "Error incrementing sku quantity on tracked inventory!", &errors.DBError)
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return nil, errors.New("Item quantity wasn't incremented on the location!", &errors.DBError)
			}
		} else {
			query := `INSERT INTO tracked_inventory (warehouse_id, client_id, item_id, batch_number, serial_number, expiration_date, quantity, available_qty, pickable_qty) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`
			commandTag, err := tx.Exec(ctx, query, req.GetWarehouseId(), req.GetClientId(), req.GetKitId(), batchNo, serialNo, expDate, req.GetQuantity(), availableQty, pickableQty)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "Unable to insert new row in tracked inventory!", &errors.DBError)
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return nil, errors.New("New row in tracked inventory wasn't inserted!", &errors.DBError)
			}
		}
	}

	// Put the item on container
	// Get container
	var container_client_id, item_id *string
	query = `SELECT client_id, item_id FROM container_wise_inventory WHERE container_id = $1 LIMIT 1`
	err = tx.QueryRow(ctx, query, containerID).Scan(&container_client_id, &item_id)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to query for container", &errors.DBError)
	}

	if container_client_id == nil {
		// Insert item in container
		err = s.App.Operations.InsertItemInContainer(tx, ctx, containerID, req.GetLocationId(), req.GetWarehouseId(), int(req.GetQuantity()), int(req.GetQuantity()), inventory_item, batchNo, serialNo, expDate, nil)
		if err != nil {
			tx.Rollback(ctx)
			return nil, err
		}
	} else {
		// Check if the item exists on the container
		var exists bool
		query := `SELECT EXISTS(SELECT 1 FROM container_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND container_id = $4 AND warehouse_id = $5 AND (expiration_date = $6 OR $6 IS NULL)) AS "exists"`
		err = tx.QueryRow(ctx, query, inventory_item.ItemID, batchNo, serialNo, containerID, req.GetWarehouseId(), expDate).Scan(&exists)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "unable to check if item exists on container", &errors.DBError)
		}
		if exists {
			// Increment the quantity on container since the item is getting added to it
			query := `UPDATE container_wise_inventory SET quantity = quantity + $1, available_qty = available_qty + $2 WHERE item_id = $3 AND (batch_number = $4 OR $4 IS NULL) AND (serial_number = $5 OR $5 IS NULL) AND container_id = $6 AND warehouse_id = $7 AND (expiration_date = $8 OR $8 IS NULL)`
			commandTag, err := tx.Exec(ctx, query, req.GetQuantity(), req.GetQuantity(), inventory_item.ItemID, batchNo, serialNo, containerID, req.GetWarehouseId(), expDate)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "error incrementing item quantity on container", &errors.DBError)
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return nil, errors.New("item quantity wasn't incremented on the container", &errors.DBError)
			}
		} else {
			// Sku doesn't exist on the container
			err = s.App.Operations.InsertItemInContainer(tx, ctx, containerID, req.GetLocationId(), req.GetWarehouseId(), int(req.GetQuantity()), int(req.GetQuantity()), inventory_item, batchNo, serialNo, expDate, nil)
			if err != nil {
				tx.Rollback(ctx)
				return nil, err
			}
		}
	}

	// TODO Check with deepak about updates in transaction

	// Add to transaction
	transactionData := &model.TransactionData{
		ClientID:       req.GetClientId(),
		WarehouseID:    req.GetWarehouseId(),
		RequestID:      req.GetRequestId(),
		ItemID:         req.GetKitId(),
		SKU:            req.GetSku(),
		Action:         "kit_assembly",
		Change:         int(req.GetQuantity()),
		Username:       req.GetAssembledBy().GetUsername(),
		UserID:         req.GetAssembledBy().GetUserId(),
		ChangedAt:      time.Now().UTC(),
		BatchNumber:    batchNo,
		SerialNumber:   serialNo,
		ExpirationDate: expDate,
		Source:         "self",
		Document:       "Work Order",
		DocumentCode:   req.GetWorkOrderCode(),
		ClientName:     req.GetClientName(),
		DocumentID:     req.GetWorkOrderId(),
		LocationID:     req.GetLocationId(),
		LocationCode:   req.GetLocationCode(),
		ContainerID:    &containerID,
		ContainerCode:  &containerCode,
		ContainerType:  "loose",
	}
	err = s.App.Operations.CreateTransaction(tx, transactionData)
	if err != nil {
		tx.Rollback(ctx)
		return nil, err
	}

	res, err := s.App.GrpcClient.Reporting.Client.AuditInventoryUpdate(ctx, &auditAgeDataRequest)
	if err != nil {
		sentry.CaptureException(errors.Wrap(err, "Failed to update inventory in reporting db!", &errors.DBError))
	}
	if !res.GetSuccess() {
		sentry.CaptureException(errors.Wrap(err, "Failed to update inventory in reporting db!", &errors.DBError))
	}

	go s.App.Operations.UpdateShopifyInventory(req.GetKitId())

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to commit transaction, rolling back!", &errors.DBError)
	}

	return &inventory_proto.UpdateAssembledKitInventoryResponse{
		Success: true,
	}, nil
}

func (s *Grpc) UpdateItemTrackedInfo(ctx context.Context, req *inventory_proto.UpdateItemTrackedInfoRequest) (*inventory_proto.UpdateItemTrackedInfoResponse, error) {

	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	query := `UPDATE inventory SET is_batch_controlled = $1, is_tracked_by_serial_no = $2, is_perishable = $3 WHERE item_id = $4 AND quantity = $5 AND available_qty = $5 AND pickable_qty = $5`
	commandTag, err := tx.Exec(ctx, query, req.GetIsBatchControlled(), req.GetIsTrackedBySerialNo(), req.GetIsPerishable(), req.GetItemId(), 0)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to update item tracking identifiers", &errors.DBError)
	}
	if commandTag.RowsAffected() != 1 {
		tx.Rollback(ctx)
		return nil, errors.New("item wasn't updated", &errors.DBError)
	}

	// Get Item from inventory
	var item model.Inventory

	query = `SELECT * FROM inventory WHERE item_id = $1`
	err = tx.QueryRow(ctx, query, req.GetItemId()).Scan(
		&item.ID,
		&item.ItemID,
		&item.ClientID,
		&item.WarehouseID,
		&item.Quantity,
		&item.AvailableQty,
		&item.PickableQty,
		&item.BaseUnit,
		&item.SKU,
		&item.Scannable,
		&item.CreatedAt,
		&item.UpdatedAt,
		&item.IsDeleted,
		&item.OnHold,
		&item.IsBatchControlled,
		&item.IsTrackedBySerialNo,
		&item.Name,
		&item.Description,
		&item.OnHoldBy,
		&item.IsPerishable,
		&item.TrackedBy,
		&item.Image,
		&item.BackOrders,
		&item.ClientName,
		&item.IsClientDeleted,
		&item.IsKit,
		&item.DynamicColumn,

		// # LeanShip #
		&item.IsLeanShip,
	)
	if err == pgx.ErrNoRows {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "item doesn't exist in the inventory", &errors.DBError)
	}
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "unable to query inventory", &errors.DBError)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back", &errors.DBError)
	}

	return &inventory_proto.UpdateItemTrackedInfoResponse{
		Success: true,
	}, nil
}

func (s *Grpc) SetLocationToPrime(ctx context.Context, req *inventory_proto.SetLocationToPrimeRequest) (*inventory_proto.SetLocationToPrimeResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	// Set locations to prime
	query := `UPDATE location_wise_inventory SET is_prime = $1 WHERE location_id = ANY($2)`
	commandTag, err := tx.Exec(ctx, query, req.GetIsPrime(), req.GetLocationId())
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to set locations to prime", &errors.DBError)
	}
	if commandTag.RowsAffected() == 0 {
		tx.Rollback(ctx)
		return nil, errors.New("Unable to set locations to prime", &errors.DBError)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to commit transaction, rolling back", &errors.DBError)
	}

	return &inventory_proto.SetLocationToPrimeResponse{
		Success: true,
	}, nil
}

func (s *Grpc) UpdateClientName(ctx context.Context, req *inventory_proto.UpdateClientNameRequest) (*inventory_proto.UpdateClientNameResponse, error) {
	if req.GetClientId() == "" || req.GetClientName() == "" {
		return nil, errors.New("Client id and client name are required", &errors.BadRequest)
	}

	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	query := `UPDATE inventory SET client_name = $1 WHERE client_id = $2`
	_, err = tx.Exec(ctx, query, req.GetClientName(), req.GetClientId())
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to set update client name in inventory", &errors.DBError)
	}

	query = `UPDATE container_wise_inventory SET client_name = $1 WHERE client_id = $2`
	_, err = tx.Exec(ctx, query, req.GetClientName(), req.GetClientId())
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to set client name in container_wise_inventory", &errors.DBError)
	}

	query = `UPDATE location_wise_inventory SET client_name = $1 WHERE client_id = $2`
	_, err = tx.Exec(ctx, query, req.GetClientName(), req.GetClientId())
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to set client name in location_wise_inventory", &errors.DBError)
	}

	query = `UPDATE transaction SET client_name = $1 WHERE client_id = $2`
	_, err = tx.Exec(ctx, query, req.GetClientName(), req.GetClientId())
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to set client name in transaction", &errors.DBError)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to commit transaction, rolling back", &errors.DBError)
	}

	return &inventory_proto.UpdateClientNameResponse{
		Success: true,
	}, nil
}

func (s *Grpc) UpsertInventory(ctx context.Context, req *inventory_proto.UpsertInventoryRequest) (*inventory_proto.UpsertInventoryResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	// Insert rows
	var rows [][]interface{}
	batch := &pgx.Batch{}

	for _, item := range req.GetItems() {
		if item.GetIsUpdated() {
			// Update item
			query := `UPDATE inventory SET name = $1, scannable = $2, description = $3, base_unit = $4 WHERE item_id = $5`
			batch.Queue(query, item.GetName(), item.GetScannable(), item.GetDescription(), item.GetBaseUnit(), item.GetItemId())
		} else {
			// Insert item
			// Dynamic Column
			var dynamic_column *map[string]string
			if item.DynamicColumns != nil {
				dynamicColumn := make(map[string]string)
				for _, column := range item.DynamicColumns {
					dynamicColumn[column.Name] = column.Value
				}
				dynamic_column = &dynamicColumn
			} else {
				dynamic_column = nil
			}
			rows = append(rows, []interface{}{item.GetItemId(), item.GetClientId(), item.GetClientName(), item.GetWarehouseId(), 0, 0, 0, item.GetBaseUnit(), item.GetSku(), item.GetScannable(), item.GetName(), item.GetDescription(), item.GetIsPerishable(), item.GetIsBatchControlled(), item.GetIsTrackedBySerialNo(), item.GetIsKit(), dynamic_column})
		}
	}

	// Batch Update
	batchResults := tx.SendBatch(ctx, batch)
	err = batchResults.Close()
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "unable to close batch", &errors.DBError)
	}

	// Bulk insert rows
	copyCount, err := tx.CopyFrom(
		ctx,
		// Table name
		pgx.Identifier{"inventory"},
		// Columns
		[]string{"item_id", "client_id", "client_name", "warehouse_id", "quantity", "available_qty", "pickable_qty", "base_unit", "sku", "scannable", "name", "description", "is_perishable", "is_batch_controlled", "is_tracked_by_serial_no", "is_kit", "dynamic_column"},
		// Rows to insert
		pgx.CopyFromRows(rows),
	)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "unable to bulk insert inventory", &errors.DBError)
	}
	if copyCount != int64(len(rows)) {
		tx.Rollback(ctx)
		return nil, errors.New("failed to insert some rows in the inventory database", &errors.DBError)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back", &errors.DBError)
	}

	return &inventory_proto.UpsertInventoryResponse{
		Success: true,
	}, nil
}

func (s *Grpc) DeallocateTrackedInventory(ctx context.Context, req *inventory_proto.DeallocateTrackedInventoryRequest) (*inventory_proto.DeallocateTrackedInventoryResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)
	// batch represents a variable that is used to bulk update in the database
	batch := &pgx.Batch{}
	fmt.Println("DATA :", req)
	for _, item := range req.GetTrackedInventory() {
		// Update available quantity in inventory
		var batchNo *string
		if item.GetBatchNumber() == "" {
			batchNo = nil
		} else {
			v := item.GetBatchNumber()
			batchNo = &v
		}

		var serialNo *string
		if item.GetSerialNumber() == "" {
			serialNo = nil
		} else {
			v := item.GetSerialNumber()
			serialNo = &v
		}

		var expDate *time.Time
		if item.GetExpirationDate() == "" {
			expDate = nil
		} else {
			v, err := time.Parse("2006-01-02", item.GetExpirationDate())
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "Unable to parse expiration date", &errors.DBError)
			}
			expDate = &v
		}
		fmt.Println("batchNo", item.GetBatchNumber(), "serialNo", item.GetSerialNumber(), "expDate", item.GetExpirationDate())
		// Update item
		query := `UPDATE tracked_inventory SET available_qty = available_qty + $1 WHERE warehouse_id = $2 AND client_id = $3 AND item_id = $4 AND (batch_number = $5 OR $5 IS NULL) AND (serial_number = $6 OR $6 IS NULL) AND (expiration_date = $7 OR $7 IS NULL)`
		batch.Queue(query, item.GetQuantity(), req.GetWarehouseId(), req.GetClientId(), item.GetItemId(), batchNo, serialNo, expDate)
	}

	// bulkUpdate Update
	batchResults := tx.SendBatch(ctx, batch)
	err = batchResults.Close()
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "unable to close batch", &errors.DBError)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to commit transaction, rolling back", &errors.DBError)
	}

	return &inventory_proto.DeallocateTrackedInventoryResponse{
		Success: true,
	}, nil
}

// # Update Available Qty In Inventory
func (s *Grpc) UpdateAvailableQtyInInventory(ctx context.Context, req *inventory_proto.UpdateAvailableQtyInInventoryRequest) (*inventory_proto.UpdateAvailableQtyInInventoryResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction", &errors.DBError)
	}
	defer tx.Rollback(ctx)
	var itemIDs []string
	var backOrderAutoAlloationItems []string
	fmt.Println("Update Available Qty In Inventory Request:", req)
	for _, item := range req.GetAllocatedInventory() {
		// Get item from inventory
		inventory_item, err := s.App.Operations.GetItemFromInventoryByItemId(item.GetItemId())
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Failed to get item from inventory "+item.GetItemId(), &errors.DBError)
		}
		if item.GetAlloatedQty() != 0 {
			if inventory_item.OnHold {
				tx.Rollback(ctx)
				return nil, errors.New("The item "+inventory_item.Name+" is on hold", &errors.DBError)
			}
		}

		query := `UPDATE inventory SET available_qty = available_qty - $1 WHERE item_id = $2` // [TAP] - Only updating available quantity
		commandTag, err := tx.Exec(ctx, query, item.GetAlloatedQty(), item.GetItemId())
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Unable to update inventory", &errors.DBError)
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return nil, errors.New("Inventory wasn't updated", &errors.DBError)
		}

		if item.GetAlloatedQty() < 0 {
			backOrderAutoAlloationItems = append(backOrderAutoAlloationItems, item.GetItemId())
		}
		itemIDs = append(itemIDs, item.GetItemId())
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to commit transaction, rolling back", &errors.DBError)
	}

	for _, itemID := range itemIDs {
		go s.App.Operations.UpdateShopifyInventory(itemID)
	}
	fmt.Println("backOrderAutoAlloationItems", backOrderAutoAlloationItems)
	// Auto allocation of back orders
	if len(backOrderAutoAlloationItems) > 0 {
		go s.App.Operations.BackOrderAutoAlloation(backOrderAutoAlloationItems, req.GetExcludedOrderIds(), req.GetEventType(), req.GetAllowReallocation())
	}

	return &inventory_proto.UpdateAvailableQtyInInventoryResponse{
		Success: true,
	}, nil
}

func (s *Grpc) ProductLPCountReport(ctx context.Context, req *inventory_proto.ProductLPCountReportRequest) (*inventory_proto.ProductLPCountReportResponse, error) {
	var lpCount []*inventory_proto.LPCount
	var pageNo, pageSize *int
	var offset *int64
	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	var query string
	var rows pgx.Rows
	var err error
	if req.GetClients() != nil {
		query = `SELECT container_wise_inventory.item_id, container_wise_inventory.warehouse_id,container_wise_inventory.client_name, container_wise_inventory.sku, container_type.name AS lp_type, COUNT(container_type.name) AS count FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id WHERE container_wise_inventory.warehouse_id = $1 AND container_wise_inventory.client_id = ANY($2) AND item_id IS NOT NULL AND container_type.name != 'loose' GROUP BY container_wise_inventory.item_id, container_wise_inventory.warehouse_id, container_wise_inventory.client_name, container_wise_inventory.sku, container_type.name ORDER BY client_name ASC LIMIT $3 OFFSET $4`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetClients(), pageSize, offset)
	} else {
		query = `SELECT container_wise_inventory.item_id, container_wise_inventory.warehouse_id,container_wise_inventory.client_name, container_wise_inventory.sku, container_type.name AS lp_type, COUNT(container_type.name) AS count FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id WHERE container_wise_inventory.warehouse_id = $1 AND item_id IS NOT NULL AND container_type.name != 'loose' GROUP BY container_wise_inventory.item_id, container_wise_inventory.warehouse_id, container_wise_inventory.client_name, container_wise_inventory.sku, container_type.name ORDER BY client_name ASC LIMIT $2 OFFSET $3`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), pageSize, offset)
	}
	if err != nil {
		return nil, errors.Wrap(err, "Failed to query container wise inventory for LP count report", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var container model.LPCount
		var containerProto inventory_proto.LPCount
		err := rows.Scan(
			&container.ItemID,
			&container.WarehouseID,
			&container.ClientName,
			&container.SKU,
			&container.LPType,
			&container.Count,
		)
		if err != nil {
			return nil, errors.Wrap(err, "Unable to read resultant rows into LP Count schema", &errors.DBError)
		}

		containerProto.ItemId = container.ItemID
		containerProto.WarehouseId = container.WarehouseID
		containerProto.ClientName = container.ClientName
		containerProto.Sku = container.SKU
		containerProto.LpType = container.LPType
		containerProto.Count = container.Count

		lpCount = append(lpCount, &containerProto)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "Unable to read rows", &errors.DBError)
	}

	var resultCount, totalPages int
	if req.GetClients() != nil {
		query = `SELECT COUNT(*) FROM (SELECT container_wise_inventory.item_id, container_wise_inventory.warehouse_id,container_wise_inventory.client_name, container_wise_inventory.sku, container_type.name AS lp_type, COUNT(container_type.name) AS count FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id WHERE container_wise_inventory.warehouse_id = $1 AND container_wise_inventory.client_id = ANY($2) AND item_id IS NOT NULL AND container_type.name != 'loose' GROUP BY container_wise_inventory.item_id, container_wise_inventory.warehouse_id, container_wise_inventory.client_name, container_wise_inventory.sku, container_type.name ) AS subquery`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), req.GetClients()).Scan(&resultCount)
	} else {
		query = `SELECT COUNT(*) FROM (SELECT container_wise_inventory.item_id, container_wise_inventory.warehouse_id,container_wise_inventory.client_name, container_wise_inventory.sku, container_type.name AS lp_type FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id WHERE container_wise_inventory.warehouse_id = $1 AND item_id IS NOT NULL AND container_type.name != 'loose' GROUP BY container_wise_inventory.item_id, container_wise_inventory.warehouse_id, container_wise_inventory.client_name, container_wise_inventory.sku, container_type.name ) AS subquery;`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId()).Scan(&resultCount)
	}
	if err != nil {
		return nil, errors.Wrap(err, "unable to get count of container rows", &errors.DBError)
	}

	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	return &inventory_proto.ProductLPCountReportResponse{
		TotalDocuments: int32(resultCount),
		TotalPages:     int32(totalPages),
		CurrentPage:    int32(*pageNo),
		Report:         lpCount,
	}, nil
}

func (s *Grpc) TrackedInventoryReport(ctx context.Context, req *inventory_proto.TrackedInventoryReportRequest) (*inventory_proto.TrackedInventoryReportResponse, error) {
	var trackedInventory []*inventory_proto.TrackedInventory
	var query string
	var rows pgx.Rows
	var err error
	var pageNo, pageSize *int
	var offset *int64
	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	if len(req.GetClients()) > 0 {
		query = `SELECT inventory.id, inventory.sku, inventory.name, inventory.description, inventory.base_unit, inventory.client_name, inventory.quantity AS total_quantity, tracked_inventory.item_id, tracked_inventory.batch_number, tracked_inventory.serial_number, tracked_inventory.expiration_date, tracked_inventory.quantity, tracked_inventory.available_qty, tracked_inventory.pickable_qty FROM inventory INNER JOIN tracked_inventory on inventory.item_id = tracked_inventory.item_id WHERE tracked_inventory.quantity > 0 AND inventory.warehouse_id = $1 AND inventory.client_id = ANY($2) ORDER BY inventory.name DESC, tracked_inventory.batch_number DESC, tracked_inventory.serial_number DESC, tracked_inventory.expiration_date DESC LIMIT $3 OFFSET $4`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetClients(), pageSize, offset)
	} else {
		query = `SELECT inventory.id, inventory.sku, inventory.name, inventory.description, inventory.base_unit, inventory.client_name, inventory.quantity AS total_quantity, tracked_inventory.item_id, tracked_inventory.batch_number, tracked_inventory.serial_number, tracked_inventory.expiration_date, tracked_inventory.quantity, tracked_inventory.available_qty, tracked_inventory.pickable_qty FROM inventory INNER JOIN tracked_inventory on inventory.item_id = tracked_inventory.item_id WHERE tracked_inventory.quantity > 0 AND inventory.warehouse_id = $1 ORDER BY inventory.name DESC, tracked_inventory.batch_number DESC, tracked_inventory.serial_number DESC, tracked_inventory.expiration_date DESC LIMIT $2 OFFSET $3`
		rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), pageSize, offset)
	}
	if err != nil {
		return nil, errors.Wrap(err, "failed to query inventory", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var inventory model.TrackedInventory
		var inventoryProto inventory_proto.TrackedInventory
		err := rows.Scan(
			&inventory.ID,
			&inventory.SKU,
			&inventory.Name,
			&inventory.Description,
			&inventory.BaseUnit,
			&inventory.ClientName,
			&inventory.TotalQuantity,
			&inventory.ItemID,
			&inventory.BatchNumber,
			&inventory.SerialNumber,
			&inventory.ExpirationDate,
			&inventory.Quantity,
			&inventory.AvailableQty,
			&inventory.PickableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into inventory schema", &errors.DBError)
		}

		inventoryProto.Id = inventory.ID
		inventoryProto.ItemId = inventory.ItemID
		inventoryProto.Quantity = strconv.Itoa(inventory.Quantity)
		inventoryProto.AvailableQty = strconv.Itoa(inventory.AvailableQty)
		inventoryProto.PickableQty = strconv.Itoa(inventory.PickableQty)
		if inventory.ExpirationDate != nil {
			inventoryProto.ExpirationDate = inventory.ExpirationDate.Format("2006-01-02 15:04:05")
		}
		inventoryProto.Sku = inventory.SKU
		inventoryProto.Name = inventory.Name
		inventoryProto.Description = inventory.Description
		inventoryProto.BaseUnit = inventory.BaseUnit
		if inventory.ClientName != nil {
			inventoryProto.ClientName = *inventory.ClientName
		}
		inventoryProto.TotalInventory = strconv.Itoa(inventory.TotalQuantity)
		if inventory.BatchNumber != nil {
			inventoryProto.BatchNumber = *inventory.BatchNumber
		}
		if inventory.SerialNumber != nil {
			inventoryProto.SerialNumber = *inventory.SerialNumber
		}

		trackedInventory = append(trackedInventory, &inventoryProto)
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var resultCount, totalPages int
	if len(req.GetClients()) > 0 {
		query := `SELECT COUNT(inventory.id) FROM inventory INNER JOIN tracked_inventory on inventory.item_id = tracked_inventory.item_id WHERE inventory.warehouse_id = $1 AND inventory.client_id = ANY($2)`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), req.GetClients()).Scan(&resultCount)
	} else {
		query := `SELECT COUNT(inventory.id) FROM inventory INNER JOIN tracked_inventory on inventory.item_id = tracked_inventory.item_id WHERE inventory.warehouse_id = $1`
		err = s.DB.QueryRow(ctx, query, req.GetWarehouseId()).Scan(&resultCount)
	}
	if err != nil {
		return nil, errors.Wrap(err, "unable to get count of tracked inventory rows", &errors.DBError)
	}

	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	return &inventory_proto.TrackedInventoryReportResponse{
		TotalDocuments: int32(resultCount),
		TotalPages:     int32(totalPages),
		CurrentPage:    int32(*pageNo),
		Report:         trackedInventory,
	}, nil
}

func (s *Grpc) InventoryClosingReport(ctx context.Context, req *inventory_proto.InventoryClosingReportRequest) (*inventory_proto.InventoryClosingReportResponse, error) {
	var report []*inventory_proto.InventoryClosing
	var query string
	var rows pgx.Rows
	var err error
	var pageNo, pageSize *int
	var offset *int64
	all := false
	if req.GetPageNo() < 1 || req.GetPageSize() < 1 {
		all = true
		no := 1
		pageNo = &no
		size := 10
		pageSize = &size
	} else {
		no := int(req.GetPageNo())
		pageNo = &no
		size := int(req.GetPageSize())
		pageSize = &size
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	closingDate, err := time.Parse(time.DateOnly, req.GetDate())
	if err != nil {
		return nil, errors.Wrap(err, "failed to convert date", &errors.BadRequest)
	}
	closingDate = closingDate.AddDate(0, 0, 1)

	query = `
		WITH ranked_transactions AS (
			SELECT
				t.client_id,
				t.client_name,
				t.item_id,
				t.sku,
				i. "name",
				t.changed_at,
				t. "current",
				i.base_unit,
				ROW_NUMBER() OVER (PARTITION BY t.item_id,
					t.client_id ORDER BY t.changed_at DESC) AS rnk
			FROM
				"transaction" AS t
				JOIN inventory AS i ON t.item_id = i.item_id
			WHERE
				t.warehouse_id = $1
				AND t.client_id = ANY($2)
				AND t.changed_at < $3
		)
		SELECT
			client_id, client_name, item_id, sku, "name", changed_at, "current", base_unit
		FROM
			ranked_transactions
		WHERE
			rnk = 1
		LIMIT $4
		OFFSET $5
	`
	rows, err = s.DB.Query(ctx, query, req.GetWarehouseId(), req.GetClients(), closingDate, pageSize, offset)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query transaction", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var inventoryClosing model.InventoryClosing
		err := rows.Scan(
			&inventoryClosing.ClientID,
			&inventoryClosing.ClientName,
			&inventoryClosing.ItemID,
			&inventoryClosing.SKU,
			&inventoryClosing.Name,
			&inventoryClosing.ChangedAt,
			&inventoryClosing.Quantity,
			&inventoryClosing.Unit,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into closing inventory schema", &errors.DBError)
		}

		closingInventoryProto := &inventory_proto.InventoryClosing{
			ClientId:   inventoryClosing.ClientID,
			ClientName: inventoryClosing.ClientName,
			ItemId:     inventoryClosing.ItemID,
			Sku:        inventoryClosing.SKU,
			Name:       inventoryClosing.Name,
			ChangedAt:  inventoryClosing.ChangedAt.Format("January 02, 2006"),
			Quantity:   strconv.FormatInt(inventoryClosing.Quantity, 10),
			Unit:       inventoryClosing.Unit,
		}

		report = append(report, closingInventoryProto)
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	var resultCount, totalPages int
	query = `
		WITH ranked_transactions AS (
			SELECT
				t.client_id,
				t.client_name,
				t.item_id,
				t.sku,
				i. "name",
				t.changed_at,
				t. "current",
				i.base_unit,
				ROW_NUMBER() OVER (PARTITION BY t.item_id,
					t.client_id ORDER BY t.changed_at DESC) AS rnk
			FROM
				"transaction" AS t
				JOIN inventory AS i ON t.item_id = i.item_id
			WHERE
				t.warehouse_id = $1
				AND t.client_id = ANY($2)
				AND t.changed_at < $3
		)
		SELECT
			COUNT(item_id)
		FROM
			ranked_transactions
		WHERE
			rnk = 1;
	`
	err = s.DB.QueryRow(ctx, query, req.GetWarehouseId(), req.GetClients(), closingDate).Scan(&resultCount)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get count of tracked inventory rows", &errors.DBError)
	}

	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	return &inventory_proto.InventoryClosingReportResponse{
		TotalDocuments: int32(resultCount),
		TotalPages:     int32(totalPages),
		CurrentPage:    int32(*pageNo),
		Report:         report,
	}, nil

}

// GetItemsLocation fetches all locations of items sent in request and return them
func (s *Grpc) GetItemsLocation(ctx context.Context, req *inventory_proto.GetItemsLocationRequest) (*inventory_proto.GetItemsLocationResponse, error) {
	// Handle panic
	defer s.App.Utils.HandlePanic(nil)

	var itemLocations []*inventory_proto.GetItemsLocationResponse_ItemLocation

	pickableAreaIds := req.GetPickableAreaIds()

	// loop over items and fetch their locations
	for _, item := range req.GetItems() {
		// fetch all allocatable locations for the item
		query := `
			SELECT 
				lwi.id, 
				lwi.location_id, 
				lwi.batch_number, 
				lwi.serial_number, 
				lwi.expiration_date, 
				lwi.available_qty, 
				cwi.received_at,
				cwi.first_used_at,
				cwi.created_at
			FROM 
				location_wise_inventory lwi
			JOIN 
				container_wise_inventory cwi
			ON 
				lwi.item_id = cwi.item_id AND
				lwi.location_id = cwi.location_id AND
				lwi.batch_number IS NOT DISTINCT FROM cwi.batch_number AND
				lwi.serial_number IS NOT DISTINCT FROM cwi.serial_number AND
				lwi.expiration_date IS NOT DISTINCT FROM cwi.expiration_date
			WHERE 
				lwi.item_id = $1 AND
				lwi.area_id = ANY($2) AND
				lwi.on_hold = false AND
				lwi.available_qty > 0 AND
				(lwi.expiration_date IS NULL OR lwi.expiration_date > NOW())
		`

		params := []any{item.ItemId, pickableAreaIds}
		paramIndex := 3
		locationAdded := make(map[int]bool)

		var queryBuilder strings.Builder
		queryBuilder.WriteString(query)

		if batchNumber := item.GetBatchNumber(); batchNumber != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND lwi.batch_number = $%d", paramIndex))
			params = append(params, batchNumber)
			paramIndex++
		}
		if serialNumber := item.GetSerialNumber(); serialNumber != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND lwi.serial_number = $%d", paramIndex))
			params = append(params, serialNumber)
			paramIndex++
		}
		if expirationDate := item.GetExpirationDate(); expirationDate != nil {
			queryBuilder.WriteString(fmt.Sprintf(" AND lwi.expiration_date = $%d", paramIndex))
			params = append(params, expirationDate.AsTime())
			paramIndex++
		}

		finalQuery := queryBuilder.String()
		rows, err := s.DB.Query(ctx, finalQuery, params...)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to query location wise inventory!", &errors.DBError)
		}
		defer rows.Close()

		// loop over rows and append location_id to itemLocations
		for rows.Next() {
			var id int
			var locationID string
			var batchNumber, serialNumber *string
			var expirationDate *time.Time
			var availableQty int32
			var receivedAt, firstUsedAt, createdAt *time.Time
			err := rows.Scan(&id, &locationID, &batchNumber, &serialNumber, &expirationDate, &availableQty, &receivedAt, &firstUsedAt, &createdAt)
			if err != nil {
				return nil, errors.Wrap(err, "Failed to scan location wise inventory rows!", &errors.DBError)
			}

			// check if location row id already exists in locationAdded map
			if locationAdded[id] || availableQty == 0 {
				continue
			} else {
				locationAdded[id] = true
			}

			itemLocation := &inventory_proto.GetItemsLocationResponse_ItemLocation{
				ItemId:     item.ItemId,
				LocationId: locationID,
				Quantity:   availableQty,
			}

			if batchNumber != nil {
				itemLocation.BatchNumber = *batchNumber
			}

			if serialNumber != nil {
				itemLocation.SerialNumber = *serialNumber
			}

			if expirationDate != nil {
				itemLocation.ExpirationDate = timestamppb.New(*expirationDate)
			}

			if receivedAt != nil {
				itemLocation.ReceivedAt = timestamppb.New(*receivedAt)
			} else if firstUsedAt != nil {
				itemLocation.ReceivedAt = timestamppb.New(*firstUsedAt)
			} else if createdAt != nil {
				itemLocation.ReceivedAt = timestamppb.New(*createdAt)
			}

			itemLocations = append(itemLocations, itemLocation)
		}
	}

	return &inventory_proto.GetItemsLocationResponse{
		ItemLocations: itemLocations,
	}, nil
}

// AllocateLocationWiseInventory allocates inventory from location wise inventory
func (s *Grpc) AllocateLocationWiseInventory(ctx context.Context, req *inventory_proto.AllocateLocationWiseInventoryRequest) (*inventory_proto.AllocateLocationWiseInventoryResponse, error) {
	// Handle panic
	defer s.App.Utils.HandlePanic(nil)

	fmt.Println("Allocate Location Wise Inventory", req)

	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction!", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	batch := &pgx.Batch{}

	// loop over item locations and allocate inventory
	for i, item := range req.GetItemLocations() {
		fmt.Println("############################################")
		fmt.Println("item "+strconv.Itoa(i)+": -------------", item)
		// update available_qty in location_wise_inventory
		query := `UPDATE location_wise_inventory SET available_qty = available_qty - $1 WHERE item_id = $2 AND location_id = $3 AND warehouse_id = $4`
		params := []any{item.GetQuantity(), item.GetItemId(), item.GetLocationId(), req.GetWarehouseId()}
		paramIndex := 5

		var queryBuilder strings.Builder
		queryBuilder.WriteString(query)

		if batchNumber := item.GetBatchNumber(); batchNumber != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND batch_number = $%d", paramIndex))
			params = append(params, batchNumber)
			paramIndex++
		}
		if serialNumber := item.GetSerialNumber(); serialNumber != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND serial_number = $%d", paramIndex))
			params = append(params, serialNumber)
			paramIndex++
		}
		if expirationDate := item.GetExpirationDate(); expirationDate != nil {
			queryBuilder.WriteString(fmt.Sprintf(" AND expiration_date = $%d", paramIndex))
			params = append(params, expirationDate.AsTime())
			paramIndex++
		}

		finalQuery := queryBuilder.String()
		batch.Queue(finalQuery, params...)
	}

	// Batch Update
	batchResults := tx.SendBatch(ctx, batch)
	err = batchResults.Close()
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to close batch(bulk) operation update!", &errors.DBError)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to commit transaction, rolling back!", &errors.DBError)
	}

	return &inventory_proto.AllocateLocationWiseInventoryResponse{
		Success: true,
	}, nil
}

// DeallocateLocationWiseInventory deallocates inventory from location wise inventory
func (s *Grpc) DeallocateLocationWiseInventory(ctx context.Context, req *inventory_proto.DeallocateLocationWiseInventoryRequest) (*inventory_proto.DeallocateLocationWiseInventoryResponse, error) {
	// Handle panic
	defer s.App.Utils.HandlePanic(nil)

	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction!", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	batch := &pgx.Batch{}

	// loop over item locations and deallocate inventory
	for i, item := range req.GetItemLocations() {
		fmt.Println("############################################")
		fmt.Println("item "+strconv.Itoa(i)+": -------------", item)
		// update available_qty in location_wise_inventory
		query := `UPDATE location_wise_inventory SET available_qty = available_qty + $1 WHERE item_id = $2 AND location_id = $3 AND warehouse_id = $4`
		params := []any{item.GetQuantity(), item.GetItemId(), item.GetLocationId(), req.GetWarehouseId()}
		paramIndex := 5

		var queryBuilder strings.Builder
		queryBuilder.WriteString(query)

		if batchNumber := item.GetBatchNumber(); batchNumber != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND batch_number = $%d", paramIndex))
			params = append(params, batchNumber)
			paramIndex++
		}
		if serialNumber := item.GetSerialNumber(); serialNumber != "" {
			queryBuilder.WriteString(fmt.Sprintf(" AND serial_number = $%d", paramIndex))
			params = append(params, serialNumber)
			paramIndex++
		}
		if expirationDate := item.GetExpirationDate(); expirationDate != nil {
			queryBuilder.WriteString(fmt.Sprintf(" AND expiration_date = $%d", paramIndex))
			params = append(params, expirationDate.AsTime())
			paramIndex++
		}

		finalQuery := queryBuilder.String()
		batch.Queue(finalQuery, params...)
	}

	// Batch Update
	batchResults := tx.SendBatch(ctx, batch)
	err = batchResults.Close()
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Unable to close batch(bulk) operation update!", &errors.DBError)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to commit transaction, rolling back!", &errors.DBError)
	}

	return &inventory_proto.DeallocateLocationWiseInventoryResponse{
		Success: true,
	}, nil
}

func (g *Grpc) GetExistingLocationsForUpload(ctx context.Context, req *inventory_proto.GetExistingLocationsForUploadRequest) (*inventory_proto.GetExistingLocationsForUploadResponse, error) {
	// {Area: []Locations}
	areaLocationsMap := make(map[string][]string)
	for _, areaLocation := range req.GetLocationArea() {
		areaLocationsMap[areaLocation.GetArea()] = append(areaLocationsMap[areaLocation.GetArea()], areaLocation.GetLocation())
	}

	// Response
	var response inventory_proto.GetExistingLocationsForUploadResponse

	// Loop over area location map
	for area, locations := range areaLocationsMap {
		query := `
			SELECT 
				location_id, 
				code, 
				area_name, 
				SUM(quantity)
			FROM location_wise_inventory
			WHERE warehouse_id = $1 
			AND area_name = $2
			AND code = ANY($3)
			GROUP BY location_id, code, area_name;
		`
		rows, err := g.DB.Query(context.TODO(), query, req.GetWarehouseId(), area, locations)
		if err != nil {
			return nil, errors.Wrap(err, "failed to query for locations", &errors.DBError)
		}
		defer rows.Close()

		for rows.Next() {
			var location schema.ExistingLocations
			err := rows.Scan(
				&location.LocationID,
				&location.Code,
				&location.AreaName,
				&location.Quantity,
			)
			if err != nil {
				return nil, errors.Wrap(err, "unable to read resultant rows into location schema", &errors.DBError)
			}

			var isEmpty bool
			if location.Quantity == 0 {
				isEmpty = true
			}

			response.LocationArea = append(response.LocationArea, &inventory_proto.LocationArea{
				LocationId: location.LocationID,
				Location:   location.Code,
				Area:       location.AreaName,
				IsEmpty:    isEmpty,
			})
		}

		if row_err := rows.Err(); row_err != nil {
			return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
		}
	}

	return &response, nil
}

func (s *Grpc) DeleteLocations(ctx context.Context, req *inventory_proto.DeleteLocationsRequest) (*inventory_proto.DeleteLocationsResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction!", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	// Query to check if any of the locations contain an item_id
	var hasItem bool
	query := `Select exists (SELECT 1 FROM location_wise_inventory WHERE area_id = $1 AND item_id IS NOT NULL)`
	err = tx.QueryRow(ctx, query, req.GetAreaId()).Scan(&hasItem)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to check if any of the area locations contain an item_id", &errors.DBError)
	}

	if hasItem {
		if req.GetDeleteOverride() {
			// Query to fetch locations which have items
			query := `SELECT item_id, SUM(quantity) AS total_quantity FROM location_wise_inventory WHERE area_id = $1 GROUP BY item_id`
			rows, err := tx.Query(ctx, query, req.GetAreaId())
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "Failed to query locations which have items", &errors.DBError)
			}
			defer rows.Close()
			var itemIDs []string
			itemQuantities := make(map[string]int)
			for rows.Next() {
				var itemID string
				var totalQuantity int
				if err := rows.Scan(&itemID, &totalQuantity); err != nil {
					log.Fatalf("Failed to scan row: %v", err)
				}
				itemIDs = append(itemIDs, itemID)
				itemQuantities[itemID] = totalQuantity
			}
			if row_err := rows.Err(); row_err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(row_err, "Failed to read rows", &errors.DBError)
			}

			// query to fetch items from inventory which are in the locations
			query = `SELECT item_id, available_qty FROM inventory WHERE item_id = ANY($1)`
			rows, err = tx.Query(ctx, query, itemIDs)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "Failed to query items from inventory", &errors.DBError)
			}
			defer rows.Close()
			var itemQuantitiesInInventory = make(map[string]int)
			for rows.Next() {
				var itemID string
				var availableQty int
				if err := rows.Scan(&itemID, &availableQty); err != nil {
					log.Fatalf("Failed to scan row: %v", err)
				}
				itemQuantitiesInInventory[itemID] = availableQty
			}
			if row_err := rows.Err(); row_err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(row_err, "Failed to read rows", &errors.DBError)
			}

			// Sort items that have quantity in locations more than available quantity in inventory
			// and store the difference in itemsToDeallocate
			itemsToDeallocate := make(map[string]int)
			for itemID, quantity := range itemQuantities {
				if quantity > itemQuantitiesInInventory[itemID] {
					difference := quantity - itemQuantitiesInInventory[itemID]
					itemsToDeallocate[itemID] = difference
				}
			}
			// TODO - Deallocate items from orders

			// Update available_qty in inventory
			for itemID, quantity := range itemQuantities {
				query := `UPDATE inventory SET available_qty = available_qty - $1 WHERE item_id = $2`
				_, err := tx.Exec(ctx, query, quantity, itemID)
				if err != nil {
					tx.Rollback(ctx)
					return nil, errors.Wrap(err, "Failed to update available quantity in inventory", &errors.DBError)
				}
			}

			// Fetch all containers in the locations
			query = `SELECT * FROM container_wise_inventory WHERE location_id = ANY($1)`
			rows, err = tx.Query(ctx, query, req.GetAreaId())
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "Failed to query containers in locations", &errors.DBError)
			}
			defer rows.Close()
			var containers []model.Container
			for rows.Next() {
				var container model.Container
				if err := rows.Scan(
					&container.ID,
					&container.ItemID,
					&container.ContainerID,
					&container.ContainerTypeID,
					&container.WarehouseID,
					&container.LocationID,
					&container.Quantity,
					&container.BatchNumber,
					&container.SerialNumber,
					&container.ExpirationDate,
				); err != nil {
					log.Fatalf("Failed to scan row: %v", err)
				}
				containers = append(containers, container)
			}
			if row_err := rows.Err(); row_err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(row_err, "Failed to read rows", &errors.DBError)
			}

			// Remove items from containers in the locations
			for _, container := range containers {
				// remove item from container
				err := s.App.Operations.RemoveItemFromContainer(tx, ctx, *container.ContainerID, *container.ItemID, *container.LocationID, *container.WarehouseID, container.BatchNumber, container.SerialNumber, container.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return nil, err
				}
			}

			// Query to delete locations
			query = `DELETE FROM location_wise_inventory WHERE location_id = ANY($1)`
			_, err = tx.Exec(ctx, query, req.GetAreaId())
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "Failed to delete area locations", &errors.DBError)
			}

		} else {
			tx.Rollback(ctx)
			return nil, errors.New("Cannot delete area as it's locations contain items", &errors.BadRequest)
		}
	} else {
		// Query to delete locations
		query := `DELETE FROM location_wise_inventory WHERE location_id = ANY($1)`
		_, err := tx.Exec(ctx, query, req.GetAreaId())
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Failed to delete area locations", &errors.DBError)
		}
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to commit transaction, rolling back!", &errors.DBError)
	}

	return &inventory_proto.DeleteLocationsResponse{
		Success: true,
	}, nil
}

func (s *Grpc) UpdateAreaName(ctx context.Context, req *inventory_proto.UpdateAreaNameRequest) (*inventory_proto.UpdateAreaNameResponse, error) {
	// Increment the quantity on location since a item is getting added to a container on that location
	query := `UPDATE location_wise_inventory SET area_name = $1 WHERE area_id = $2`
	commandTag, err := s.DB.Exec(ctx, query, req.GetAreaName(), req.GetAreaId())
	if err != nil {
		return nil, errors.Wrap(err, "failed to update area name in location wise inventory", &errors.DBError)
	}
	if commandTag.RowsAffected() == 0 {
		return nil, errors.New("area name wasn't updated in locations", &errors.DBError)
	}

	return &inventory_proto.UpdateAreaNameResponse{Success: true}, nil
}

func (s *Grpc) GetLocationItemCount(ctx context.Context, req *inventory_proto.GetLocationItemCountRequest) (*inventory_proto.GetLocationItemCountResponse, error) {
	var resp inventory_proto.GetLocationItemCountResponse

	query := `SELECT location_id, COUNT(DISTINCT item_id) as item_count FROM location_wise_inventory WHERE location_id = ANY($1) GROUP BY location_id`
	rows, err := s.DB.Query(context.TODO(), query, req.GetLocationIds())
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for locations", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var locationID string
		var itemCount int32
		err := rows.Scan(
			&locationID,
			&itemCount,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into variables", &errors.DBError)
		}

		resp.LocationItemsCount = append(resp.LocationItemsCount, &inventory_proto.LocationItemsCount{
			LocationId: locationID,
			ItemsCount: itemCount,
		})
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	return &resp, nil
}

func (s *Grpc) UpdateInventoryForAreaHold(ctx context.Context, req *inventory_proto.UpdateInventoryForAreaHoldRequest) (*inventory_proto.UpdateInventoryForAreaHoldResponse, error) {
	// Create transaction
	tx, err := s.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction!", &errors.DBError)
	}
	defer tx.Rollback(ctx)

	// Query to check if any of the locations contain an item_id
	var hasItem bool
	query := `Select exists (SELECT 1 FROM location_wise_inventory WHERE area_id = $1 AND item_id IS NOT NULL)`
	err = tx.QueryRow(ctx, query, req.GetAreaId()).Scan(&hasItem)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to check if any of the area locations contain an item_id", &errors.DBError)
	}

	if hasItem {
		// Query to fetch locations which have items
		query := `SELECT item_id, SUM(quantity) AS total_quantity FROM location_wise_inventory WHERE area_id = $1 GROUP BY item_id`
		rows, err := tx.Query(ctx, query, req.GetAreaId())
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Failed to query locations which have items", &errors.DBError)
		}
		defer rows.Close()

		itemQuantities := make(map[string]int)
		for rows.Next() {
			var itemID string
			var totalQuantity int
			if err := rows.Scan(&itemID, &totalQuantity); err != nil {
				log.Fatalf("Failed to scan row: %v", err)
			}
			if req.GetHold() {
				itemQuantities[itemID] = totalQuantity
			} else {
				itemQuantities[itemID] = -totalQuantity
			}
		}
		if row_err := rows.Err(); row_err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(row_err, "Failed to read rows", &errors.DBError)
		}

		// Query for tracked items by area
		var itemsMarkedPickableTracked []schema.MakeAreaPickableTracked
		query = `SELECT item_id, batch_number, serial_number, expiration_date, SUM(available_qty) FROM location_wise_inventory WHERE area_id = $1 AND on_hold = $2 GROUP BY item_id, batch_number, serial_number, expiration_date`
		rows, err = s.DB.Query(context.TODO(), query, req.GetAreaId(), false)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "failed to query for tracked location items", &errors.DBError)
		}
		defer rows.Close()

		for rows.Next() {
			var itemMarkedPickableTracked schema.MakeAreaPickableTracked
			err := rows.Scan(
				&itemMarkedPickableTracked.ItemID,
				&itemMarkedPickableTracked.BatchNumber,
				&itemMarkedPickableTracked.SerialNumber,
				&itemMarkedPickableTracked.ExpirationDate,
				&itemMarkedPickableTracked.Quantity,
			)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "unable to read resultant rows into tracked item schema", &errors.DBError)
			}
			if itemMarkedPickableTracked.ItemID != nil {
				if !(itemMarkedPickableTracked.BatchNumber == nil && itemMarkedPickableTracked.SerialNumber == nil && itemMarkedPickableTracked.ExpirationDate == nil) {
					itemsMarkedPickableTracked = append(itemsMarkedPickableTracked, itemMarkedPickableTracked)
				}
			}
		}
		if row_err := rows.Err(); row_err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
		}

		// Update available_qty in inventory
		for itemID, quantity := range itemQuantities {
			query := `UPDATE inventory SET available_qty = available_qty - $1 WHERE item_id = $2`
			_, err := tx.Exec(ctx, query, quantity, itemID)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "Failed to update available quantity in inventory", &errors.DBError)
			}
			go s.App.Operations.UpdateShopifyInventory(itemID)
		}

		// Update available_qty in tracked inventory
		for _, item := range itemsMarkedPickableTracked {
			var quantity int
			if req.GetHold() {
				quantity = item.Quantity
			} else {
				quantity = -item.Quantity
			}
			query := `UPDATE tracked_inventory SET available_qty = available_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL)`
			commandTag, err := tx.Exec(ctx, query, quantity, item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "error incrementing pickable quantity in tracked inventory", &errors.DBError)
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return nil, errors.New("no rows were affected while incrementing pickable quantity in tracked inventory", &errors.DBError)
			}
		}
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "Failed to commit transaction, rolling back!", &errors.DBError)
	}

	return &inventory_proto.UpdateInventoryForAreaHoldResponse{Success: true}, nil
}

func (s *Grpc) GetLocationHoldStatusByArea(ctx context.Context, req *inventory_proto.GetLocationHoldStatusByAreaRequest) (*inventory_proto.GetLocationHoldStatusByAreaResponse, error) {
	var res inventory_proto.GetLocationHoldStatusByAreaResponse

	query := `SELECT location_id, COUNT(item_id), on_hold FROM location_wise_inventory WHERE area_id = $1 GROUP BY location_id, on_hold`
	rows, err := s.DB.Query(context.TODO(), query, req.GetAreaId())
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for distinct locations", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var locationID string
		var count int64
		var onHold bool
		err := rows.Scan(
			&locationID,
			&count,
			&onHold,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into location variable", &errors.DBError)
		}

		occupied := false
		if count > 0 {
			occupied = true
		}

		res.Locations = append(res.Locations, &inventory_proto.LocationHoldStatus{
			LocationId: locationID,
			Hold:       onHold,
			Occupied:   occupied,
		})

	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	return &res, nil
}

func (s *Grpc) GetClientInventoryV1(ctx context.Context, req *inventory_proto.GetClientInventoryV1Request) (*inventory_proto.GetClientInventoryV1Response, error) {
	var response inventory_proto.GetClientInventoryV1Response

	query := `SELECT item_id, sku, quantity, available_qty, pickable_qty FROM inventory WHERE item_id = ANY($1)`
	rows, err := s.DB.Query(context.TODO(), query, req.GetItemIds())
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for client inventory", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var item model.ItemV1
		err := rows.Scan(
			&item.ItemID,
			&item.SKU,
			&item.Quantity,
			&item.AvailableQty,
			&item.PickableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into item v1 variable", &errors.DBError)
		}

		response.Items = append(response.Items, &inventory_proto.ItemV1{
			ItemId:       item.ItemID,
			Sku:          item.SKU,
			Quantity:     &item.Quantity,
			AvailableQty: &item.AvailableQty,
			PickableQty:  &item.PickableQty,
		})
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	return &response, nil
}

func (s *Grpc) GetAllContainersWithReferenceNo(ctx context.Context, req *inventory_proto.GetAllContainersWithReferenceNoRequest) (*inventory_proto.GetAllContainersWithReferenceNoResponse, error) {
	var containers []*inventory_proto.ContainerWithRefNo

	// Fetch all distinct containers of a warehouse in which reference no exists
	query := `SELECT DISTINCT container_id, code, ref_no FROM container_wise_inventory WHERE warehouse_id = $1 AND ref_no IS NOT NULL`
	rows, err := s.DB.Query(context.TODO(), query, req.GetWarehouseId())
	if err != nil {
		return nil, errors.Wrap(err, "Failed to query for containers with reference no", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var lp uuid.UUID
		var lpCode, refNo string
		err := rows.Scan(&lp, &lpCode, &refNo)
		if err != nil {
			return nil, errors.Wrap(err, "Unable to read resultant rows into container schema", &errors.DBError)
		}

		lpWithRefNo := &inventory_proto.ContainerWithRefNo{
			LpCode: lp.String(),
			RefNo:  refNo,
		}
		containers = append(containers, lpWithRefNo)
	}

	return &inventory_proto.GetAllContainersWithReferenceNoResponse{
		Containers: containers,
	}, nil
}

func (s *Grpc) GetCartByID(ctx context.Context, req *inventory_proto.GetCartByIDRequest) (*inventory_proto.GetCartByIDResponse, error) {
	cartID := uuid.FromStringOrNil(req.GetCartId())
	if cartID == uuid.Nil {
		return nil, errors.New("invalid cart id", &errors.BadRequest)
	}

	// Get Cart and it's totes
	query := `
		SELECT
			cart.code AS cart_code,
			tote.tote_id,
			tote.code AS tote_code
		FROM
			cart_totes
			INNER JOIN tote ON cart_totes.tote_id = tote.tote_id
			INNER JOIN cart ON cart_totes.cart_id = cart.cart_id
		WHERE
			cart_totes.cart_id = $1
	
	`
	rows, err := s.DB.Query(context.TODO(), query, cartID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for cart totes", &errors.DBError)
	}
	defer rows.Close()

	var CartCode string
	var Totes []*inventory_proto.Tote

	for rows.Next() {
		var cartCode, toteCode string
		var toteID uuid.UUID
		err := rows.Scan(
			&cartCode,
			&toteID,
			&toteCode,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into cart and tote variable", &errors.DBError)
		}
		if CartCode == "" {
			CartCode = cartCode
		}
		Totes = append(Totes, &inventory_proto.Tote{
			Id:   toteID.String(),
			Code: toteCode,
		})

	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	// Get the number of items in cart totes
	query = `
		SELECT
			COUNT(item_id)
		FROM
			cart_totes
			INNER JOIN tote ON cart_totes.tote_id = tote.tote_id
			INNER JOIN tote_items ON tote.tote_id = tote_items.tote_id
		WHERE
			cart_totes.cart_id = $1
	`
	var itemsCount int
	err = s.DB.QueryRow(ctx, query, cartID).Scan(&itemsCount)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get count of cart tote items", &errors.DBError)
	}

	res := &inventory_proto.GetCartByIDResponse{
		Cart: &inventory_proto.Cart{
			Id:   req.GetCartId(),
			Code: CartCode,
		},
		Totes: Totes,
	}
	if itemsCount == 0 {
		res.IsEmpty = true
	}

	return res, nil
}

func (s *Grpc) DeletePicklist(ctx context.Context, req *inventory_proto.DeletePicklistRequest) (*inventory_proto.DeletePicklistResponse, error) {
	// Delete rows from tote_items matched by picklist id
	query := `DELETE FROM tote_items WHERE picklist_id = $1`
	_, err := s.DB.Exec(ctx, query, req.GetPicklistId())
	if err != nil {
		return nil, errors.Wrap(err, "unable to delete tote items by picklist", &errors.DBError)
	}

	return &inventory_proto.DeletePicklistResponse{Success: true}, nil
}

func (s *Grpc) DeletePicklistOrder(ctx context.Context, req *inventory_proto.DeletePicklistOrderRequest) (*inventory_proto.DeletePicklistOrderResponse, error) {
	// Delete rows from tote_items matched by picklist id
	query := `DELETE FROM tote_items WHERE picklist_id = $1 AND order_id = $2`
	commandTag, err := s.DB.Exec(ctx, query, req.GetPicklistId(), req.GetOrderId())
	if err != nil {
		return nil, errors.Wrap(err, "unable to delete tote items by picklist order", &errors.DBError)
	}
	if commandTag.RowsAffected() == 0 {
		return nil, errors.New("no rows were affected", &errors.DBError)
	}

	return &inventory_proto.DeletePicklistOrderResponse{Success: true}, nil
}

func (s *Grpc) DeletePicklistOrderItems(ctx context.Context, req *inventory_proto.DeletePicklistOrderItemsRequest) (*inventory_proto.DeletePicklistOrderItemsResponse, error) {
	// Delete rows from tote_items matched by picklist id
	query := `DELETE FROM tote_items WHERE picklist_id = $1 AND order_id = $2 AND item_id = ANY($3)`
	commandTag, err := s.DB.Exec(ctx, query, req.GetPicklistId(), req.GetOrderId(), req.GetItemIds())
	if err != nil {
		return nil, errors.Wrap(err, "unable to delete tote items by picklist order items", &errors.DBError)
	}
	if commandTag.RowsAffected() == 0 {
		return nil, errors.New("no rows were affected", &errors.DBError)
	}

	return &inventory_proto.DeletePicklistOrderItemsResponse{Success: true}, nil
}

func (s *Grpc) GetContainerReferenceNo(ctx context.Context, req *inventory_proto.GetContainerReferenceNoRequest) (*inventory_proto.GetContainerReferenceNoResponse, error) {
	containerID := uuid.FromStringOrNil(req.GetContainerId())
	if containerID == uuid.Nil {
		return nil, errors.New("invalid cart id", &errors.BadRequest)
	}
	// fetch container reference no
	query := `SELECT ref_no FROM container_wise_inventory WHERE container_id = $1 AND ref_no IS NOT NULL LIMIT 1`
	var refNo string
	err := s.DB.QueryRow(context.TODO(), query, containerID).Scan(&refNo)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return &inventory_proto.GetContainerReferenceNoResponse{RefNo: ""}, nil
		}
		return nil, errors.Wrap(err, "Failed to query for container reference no", &errors.DBError)
	}

	return &inventory_proto.GetContainerReferenceNoResponse{RefNo: refNo}, nil
}

// # Helper Method: Get In Between LPs
func (s *Grpc) GetInBetweenLPs(startingLP, endingLP string) ([]string, error) {
	var err error

	startingLP = strings.TrimPrefix(startingLP, "LP-")
	startingLPNum, err := strconv.Atoi(startingLP)
	if err != nil {
		err = errors.Wrap(err, "Invalid starting LP format", &errors.BadRequest)
		return nil, err
	}

	endingLP = strings.TrimPrefix(endingLP, "LP-")
	endingLPNum, err := strconv.Atoi(endingLP)
	if err != nil {
		err = errors.Wrap(err, "Invalid ending LP format", &errors.BadRequest)
		return nil, err
	}

	// # Validate the 'LP' range
	if startingLPNum > endingLPNum {
		err = errors.New("starting LP must be less than or equal to ending LP", &errors.BadRequest)
		return nil, err
	}

	// # Generate the LPs
	var lp string
	var lps []string
	for i := startingLPNum; i <= endingLPNum; i++ {
		lp = fmt.Sprintf("LP-%d", i)
		lps = append(lps, lp)
	}

	// # Return the LPs
	return lps, nil
}

// # Get In Between Containers
func (s *Grpc) GetInBetweenContainers(ctx context.Context, req *inventory_proto.GetInBetweenContainersRequest) (*inventory_proto.GetInBetweenContainersResponse, error) {
	context := context.TODO()

	var err error

	var rows pgx.Rows
	var query string

	startingLP := req.GetStartingLp()
	endingLP := req.GetEndingLp()

	lps, err := s.GetInBetweenLPs(startingLP, endingLP)
	if err != nil {
		return nil, err
	}

	labelType := req.GetLabelType()
	warehouseID := req.GetWarehouseId()

	looseContainerType := "loose"

	var locationMap map[string]string

	if labelType == "detailed" {
		query = `SELECT DISTINCT ON ("code", location_id)
				location_id,
				"code" AS location_code
			FROM
				location_wise_inventory
			WHERE
				warehouse_id = $1
			ORDER BY
				"code",
				location_id`

		rows, err = s.DB.Query(context, query, warehouseID)
		if err != nil {
			err = errors.Wrap(err, "failed to query location wise inventory for all warehouse distinct locations", &errors.DBError)
			return nil, err
		}

		defer rows.Close()

		locationMap = make(map[string]string)

		for rows.Next() {
			var locationID, locationCode string

			err = rows.Scan(
				&locationID,
				&locationCode,
			)
			if err != nil {
				err = errors.Wrap(err, "unable to read resultant rows into location variables", &errors.DBError)
				return nil, err
			}

			if locationID != "" {
				locationMap[locationID] = locationCode
			}
		}

		err = rows.Err()
		if err != nil {
			err = errors.Wrap(err, "unable to read rows for all warehouse distinct locations", &errors.DBError)
			return nil, err
		}
	}

	if labelType == "detailed" {
		query = `SELECT
					c.container_id,
					c.item_id,
					c.location_id,
					c.warehouse_id,
					i.client_id,
					i.client_name,
					i."name" AS item_name,
					c."code" AS lp_code,
					t."name" AS lp_type,
					c.quantity,
					c.available_qty,
					c.sku,
					c.scannable,
					c.batch_number,
					c.serial_number,
					c.expiration_date,
					i.base_unit AS unit,
					i.description
				FROM
					container_wise_inventory c
					INNER JOIN container_type t ON c.container_type_id = t."id"
					LEFT JOIN inventory i ON c.item_id = i.item_id
				WHERE
					c.warehouse_id = $1
					AND t."name" != $2
					AND c."code" = ANY ($3)
				ORDER BY
					NULLIF(regexp_replace(c."code", '\D', '', 'g'), '')::int ASC,
					c.item_id`
	} else {
		query = `SELECT DISTINCT ON (c."code")
					c.container_id,
					c.item_id,
					c.location_id,
					c.warehouse_id,
					i.client_id,
					i.client_name,
					i."name" AS item_name,
					c."code" AS lp_code,
					t."name" AS lp_type,
					c.quantity,
					c.available_qty,
					c.sku,
					c.scannable,
					c.batch_number,
					c.serial_number,
					c.expiration_date,
					i.base_unit AS unit,
					i.description
				FROM
					container_wise_inventory c
					INNER JOIN container_type t ON c.container_type_id = t."id"
					LEFT JOIN inventory i ON c.item_id = i.item_id
				WHERE
					c.warehouse_id = $1
					AND t."name" != $2
					AND c."code" = ANY ($3)
				ORDER BY
					c."code",
					NULLIF(regexp_replace(c."code", '\D', '', 'g'), '')::int ASC`
	}

	rows, err = s.DB.Query(context, query, warehouseID, looseContainerType, lps)
	if err != nil {
		err = errors.Wrap(err, "failed to query container wise inventory for in between containers", &errors.DBError)
		return nil, err
	}

	defer rows.Close()

	var containers []*inventory_proto.ContainerDetails

	containerMap := make(map[string]int)

	for rows.Next() {
		var container model.ContainerDetails
		var containerProto inventory_proto.ContainerDetails

		err = rows.Scan(
			&container.ContainerID,
			&container.ItemID,
			&container.LocationID,
			&container.WarehouseID,
			&container.ClientID,
			&container.ClientName,
			&container.ItemName,
			&container.LPCode,
			&container.LPType,
			&container.Quantity,
			&container.AvailableQty,
			&container.SKU,
			&container.Scannable,
			&container.BatchNumber,
			&container.SerialNumber,
			&container.ExpirationDate,
			&container.Unit,
			&container.Description,
		)
		if err != nil {
			err = errors.Wrap(err, "unable to read resultant rows into container schema", &errors.DBError)
			return nil, err
		}

		var skipContainer bool

		if labelType == "detailed" {
			if container.ContainerID != nil {
				containerID := (*container.ContainerID).String()

				if container.ItemID != nil {
					containerMap[containerID]++
					containerProto.IsEmptyContainer = false
				} else {
					_, isContainerExists := containerMap[containerID]
					if isContainerExists {
						skipContainer = true
					} else {
						containerMap[containerID] = 1
						containerProto.IsEmptyContainer = true
					}
				}
			}
		}

		if !skipContainer {
			if container.ClientName != nil {
				containerProto.ClientName = *container.ClientName
			}
			if container.ItemName != nil {
				containerProto.ItemName = *container.ItemName
			}
			if container.SKU != nil {
				containerProto.Sku = *container.SKU
			}
			if container.Scannable != nil {
				containerProto.Scannable = *container.Scannable
			}

			if container.Quantity != nil {
				// # Convert the 'quantity' from type 'int32' to 'string'
				quantity := *container.Quantity
				quantityStr := fmt.Sprintf("%d", quantity)

				containerProto.Quantity = quantityStr
			}

			if container.Unit != nil {
				containerProto.Unit = *container.Unit
			}

			if container.ContainerID != nil && container.LPCode != nil {
				// # Form container 'QR Code' string
				containerID := (*container.ContainerID).String()
				lpCode := *container.LPCode
				containerQR := fmt.Sprintf("LP/%s/%s", containerID, lpCode)

				containerProto.ContainerQr = containerQR
			}

			if container.LPType != nil {
				containerProto.LpType = *container.LPType
			}

			if labelType == "detailed" {
				if container.LocationID != nil {
					locationID := *container.LocationID
					locationCode, isLocationExists := locationMap[locationID]
					if isLocationExists {
						containerProto.LocationCode = locationCode
					} else {
						containerProto.LocationCode = ""
					}
				}
			}

			if container.BatchNumber != nil {
				containerProto.BatchNumber = *container.BatchNumber
			}
			if container.SerialNumber != nil {
				containerProto.SerialNumber = *container.SerialNumber
			}

			if container.ExpirationDate != nil {
				// # Format the 'expiration date' in "dd-mm-yyyy" format
				expirationDate := *container.ExpirationDate
				expirationDateStr := expirationDate.Format("02 Jan 2006")

				containerProto.ExpirationDate = expirationDateStr
			}

			// # Append the 'container proto' to the 'containers' list
			containers = append(containers, &containerProto)
		}
	}

	err = rows.Err()
	if err != nil {
		err = errors.Wrap(err, "unable to read rows for in between containers", &errors.DBError)
		return nil, err
	}

	response := inventory_proto.GetInBetweenContainersResponse{
		Containers: containers,
	}

	// # Return the response
	return &response, nil
}

func (s *Grpc) CheckARNFulfilled(ctx context.Context, req *inventory_proto.CheckARNFulfilledRequest) (*inventory_proto.CheckARNFulfilledResponse, error) {
	query := `SELECT
				COUNT(id) AS total_count
			FROM
				transaction
			WHERE
				client_id = $1
				AND warehouse_id = $2
				AND document_id = $3
				AND action = 'arn_fulfillment'`

	var totalCount int32
	err := s.DB.QueryRow(ctx, query, req.GetClientId(), req.GetWarehouseId(), req.GetArnId()).Scan(&totalCount)
	if err != nil {
		err = errors.Wrap(err, "failed to query transaction for arn fulfillment", &errors.DBError)
		return nil, err
	}

	var isFulfilled bool
	if totalCount == req.GetScannedLength() {
		isFulfilled = true
	}

	return &inventory_proto.CheckARNFulfilledResponse{
		IsFulfilled: isFulfilled,
	}, nil
}

// # Get LeanShip Inventory: It fetches all the 'LeanShip' warehouse 'inventory'
func (s *Grpc) GetLeanShipInventory(ctx context.Context, req *inventory_proto.GetLeanShipInventoryRequest) (*inventory_proto.GetLeanShipInventoryResponse, error) {
	var err error

	warehouseID := req.GetWarehouseId()
	clientID := req.GetClientId()
	pageNo := req.GetPageNo()
	pageSize := req.GetPageSize()
	onHold := req.GetOnHold()
	sortingValue := req.GetSortingValue()
	sortingOrder := req.GetSortingOrder()
	search := req.GetSearch()
	searchModifier := req.GetSearchModifier()

	if pageNo == 0 {
		pageNo = 1
	}
	if pageSize == 0 {
		pageSize = 50
	}

	// # Call 'Get Inventory' method
	inventory, err := s.App.Inventory.GetInventory(warehouseID, clientID, onHold, sortingValue, sortingOrder, search, searchModifier, pageNo, pageSize)
	if err != nil {
		err = errors.Wrap(err, "failed to get inventory", &errors.DBError)
		return nil, err
	}

	GetStringData := func(data *string) string {
		if data == nil {
			return ""
		} else {
			return *data
		}
	}

	var leanShipItems []*inventory_proto.LeanShipItem
	var inventorySearchParams []*inventory_proto.InventorySearchParam

	items := inventory.Data
	for _, item := range items {
		leanShipItem := inventory_proto.LeanShipItem{
			Id:            item.ID,
			ItemId:        item.ItemID,
			WarehouseId:   item.WarehouseID,
			ClientId:      item.ClientID,
			Name:          item.Name,
			WarehouseName: item.WarehouseName,
			ClientName:    GetStringData(item.ClientName),
			Sku:           item.SKU,
			Scannable:     item.Scannable,
			BaseUnit:      item.BaseUnit,
			Description:   item.Description,
			Image:         GetStringData(item.Image),
			Quantity:      item.Quantity,
			AvailableQty:  item.AvailableQty,
			OnHold:        item.OnHold,
			CreatedAt:     util.GetStringDate(item.CreatedAt),
			UpdatedAt:     util.GetStringDate(item.UpdatedAt),
			IsLeanship:    item.IsLeanShip,
		}

		leanShipItems = append(leanShipItems, &leanShipItem)
	}

	searchParams := inventory.SearchParams
	for _, searchParam := range searchParams {
		inventorySearchParam := inventory_proto.InventorySearchParam{
			Level:       searchParam.Level,
			Key:         searchParam.Key,
			RequestKey:  searchParam.RequestKey,
			Description: searchParam.Description,
		}

		inventorySearchParams = append(inventorySearchParams, &inventorySearchParam)
	}

	response := inventory_proto.GetLeanShipInventoryResponse{
		TotalItems:   inventory.TotalDocuments,
		TotalPages:   inventory.TotalPages,
		CurrentPage:  inventory.CurrentPage,
		Items:        leanShipItems,
		SearchParams: inventorySearchParams,
	}

	return &response, nil
}

func (s *Grpc) UpdateItemQuantity(ctx context.Context, req *inventory_proto.UpdateItemQuantityRequest) (*inventory_proto.UpdateItemQuantityResponse, error) {
	for _, item := range req.GetItems() {
		updateQtyReq := &schema.ValidateUpdateItemQuantity{
			WarehouseID:       req.GetWarehouseId(),
			ClientID:          req.GetClientId(),
			UserID:            req.GetUserId(),
			Username:          req.GetUsername(),
			RequestID:         req.GetRequestId(),
			ItemID:            item.GetItemId(),
			Quantity:          int(item.GetQuantity()),
			IsFrontendRequest: req.GetIsFrontendRequest(),
		}
		_, err := s.App.Inventory.UpdateItemQuantity(updateQtyReq)
		if err != nil {
			return nil, err
		}
	}
	return &inventory_proto.UpdateItemQuantityResponse{}, nil
}

func (s *Grpc) CheckInventoryForShipment(ctx context.Context, req *inventory_proto.CheckInventoryForShipmentRequest) (*inventory_proto.CheckInventoryForShipmentResponse, error) {
	// # Map to store the 'item ID' and 'required quantity'
	itemRequiredQtyMap := make(map[string]int32)
	// # List to store the 'item IDs'
	itemList := []string{}

	for _, item := range req.GetItems() {
		itemRequiredQtyMap[item.GetItemId()] = item.GetQuantity()
		itemList = append(itemList, item.GetItemId())
	}

	query := `SELECT item_id, sku, quantity FROM inventory WHERE item_id = ANY($1) AND warehouse_id = $2`
	rows, err := s.DB.Query(ctx, query, itemList, req.GetWarehouseId())
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for inventory items", &errors.DBError)
	}
	defer rows.Close()

	for rows.Next() {
		var itemID, sku string
		var quantity int32
		err := rows.Scan(
			&itemID,
			&sku,
			&quantity,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into item variable", &errors.DBError)
		}
		if quantity < itemRequiredQtyMap[itemID] {
			return nil, errors.New("not enough quantity available for item: "+sku, &errors.DBError)
		}
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows", &errors.DBError)
	}

	return &inventory_proto.CheckInventoryForShipmentResponse{}, nil
}
