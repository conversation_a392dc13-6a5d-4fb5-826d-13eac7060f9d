package app

import (
	"context"
	"fmt"
	"inventory/constants"
	"inventory/model"
	"strings"
	"time"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/pkg/errors"
	"github.com/rs/zerolog"
)

// # Transaction is an interface for 'Transaction' service
type Transaction interface {
	GetItemTransactions(string, string, string, int, int, time.Time, time.Time) (any, error)
}

// # TransactionOpts is a struct to hold 'Transaction' service dependencies
type TransactionOpts struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// # TransactionImpl is a struct to implement 'Transaction' service
type TransactionImpl struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// # InitTransaction initializes a new instance of 'Transaction' service
func InitTransaction(opts *TransactionOpts) Transaction {
	t := &TransactionImpl{
		App:    opts.App,
		DB:     opts.DB,
		Logger: opts.Logger,
	}
	return t
}

// # Get Item Transactions
func (t *TransactionImpl) GetItemTransactions(itemID, search, searchModifier string, pageNo, pageSize int, startDate, endDate time.Time) (any, error) {
	ctx := context.TODO()

	var err error

	var response any

	var rows pgx.Rows
	var query, searchQuery string
	var partialSearch, cleanSearch string

	var queryParams []any

	var limit, offset *int

	if pageNo < 1 {
		pageNo = 1
	}

	if pageSize == -1 {
		limit = nil
		offset = nil
	} else {
		limit = &pageSize
		offset_ := ((pageNo - 1) * (pageSize))
		offset = &offset_
	}

	var searchPresent, searchModifierPresent, isExactSearch bool

	if search != "" {
		// # Search Filter: Search by 'changed by user name', 'txn type (action)', 'lot number', 'serial number', 'expiry date', 'location code', 'container code', 'document', 'document code', and 'document prn'
		searchPresent = true

		// # Utility to detect the 'exact' search -> if the 'search' query 'string' is 'wrapped' in the 'double' quotes
		isExactSearch = strings.HasPrefix(search, "\"") && strings.HasSuffix(search, "\"")
		cleanSearch = strings.Trim(search, "\"")
		partialSearch = "%" + cleanSearch + "%"
	}

	if searchModifier != "" {
		searchModifierPresent = true
	}

	// # Base Query For Item Transactions
	query = `SELECT
				t.*,
				i.base_unit AS unit
			FROM
				"transaction" t
				INNER JOIN inventory i ON t.item_id = i.item_id
			WHERE
				t.item_id = $1
				AND (t.changed_at BETWEEN $2 AND $3)`

	// # Query Parameters For Item Transactions
	queryParams = append(queryParams, itemID, startDate, endDate)
	paramIndex := 4

	/* # Note:
	1. For the 'partial' search, 'enter' the string 'as-is'
	2. For the 'exact' search, 'enclose' the string in the 'double' quotes
	*/
	if searchPresent {
		if searchModifierPresent {
			// # Modifier Based Search: Search by the provided 'search modifier'
			if isExactSearch {
				// # Exact Search
				searchQuery = fmt.Sprintf(" AND (%s = $%d)", searchModifier, paramIndex)
				search = cleanSearch
			} else {
				// # Partial Search
				searchQuery = fmt.Sprintf(" AND (%s ILIKE $%d)", searchModifier, paramIndex)
				search = partialSearch
			}
		} else {
			// # Default Search: Search by the 'changed by user name' and 'txn type (action)'
			if isExactSearch {
				// # Exact Search
				searchQuery = fmt.Sprintf(" AND (t.changed_by_username = $%d OR t.action = $%d)", paramIndex, paramIndex)
				search = cleanSearch
			} else {
				// # Partial Search
				searchQuery = fmt.Sprintf(" AND (t.changed_by_username ILIKE $%d OR t.action ILIKE $%d)", paramIndex, paramIndex)
				search = partialSearch
			}
		}
		query += searchQuery
		queryParams = append(queryParams, search)
		paramIndex++
	}

	// # Pagination Filter
	orderBy := "t.id DESC"
	searchQuery = fmt.Sprintf(" ORDER BY %s LIMIT $%d OFFSET $%d", orderBy, paramIndex, paramIndex+1)
	query += searchQuery
	queryParams = append(queryParams, limit, offset)

	// # Execute Query
	rows, err = t.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query transaction")
	}

	defer rows.Close()

	var transactions []*model.Transaction

	for rows.Next() {
		var transaction model.Transaction

		err = rows.Scan(
			&transaction.ID,
			&transaction.ClientID,
			&transaction.WarehouseID,
			&transaction.RequestID,
			&transaction.ItemID,
			&transaction.SKU,
			&transaction.Action,
			&transaction.Change,
			&transaction.Current,
			&transaction.ChangedByUsername,
			&transaction.ChangedByID,
			&transaction.ChangedAt,
			&transaction.BatchNumber,
			&transaction.SerialNumber,
			&transaction.ExpirationDate,
			&transaction.Source,
			&transaction.Document,
			&transaction.DocumentCode,
			&transaction.ClientName,
			&transaction.DocumentID,
			&transaction.Remark,
			&transaction.DocumentPrn,
			&transaction.LocationID,
			&transaction.LocationCode,
			&transaction.ContainerID,
			&transaction.ContainerCode,
			&transaction.ContainerType,
			&transaction.CartID,
			&transaction.CartCode,
			&transaction.ToteID,
			&transaction.ToteCode,
			&transaction.CurrentTrackedQty,
			&transaction.Unit,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into transaction schema")
		}

		transaction.ChangedBy = &model.ChangedBy{
			ID:       &transaction.ChangedByID,
			Username: &transaction.ChangedByUsername,
		}

		transactions = append(transactions, &transaction)
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	var resultCount, totalPages int

	// # Base Query For Item Transactions Count
	query = `SELECT
				COUNT(item_id)
			FROM
				"transaction"
			WHERE
				item_id = $1
				AND (changed_at BETWEEN $2 AND $3)`

	// # Query Parameters For Item Transactions Count
	// # Reset Query Params: length becomes '3' but capacity remains the 'same'
	queryParams = queryParams[:3]
	paramIndex = 4

	/* # Note:
	1. For the 'partial' search, 'enter' the string 'as-is'
	2. For the 'exact' search, 'enclose' the string in the 'double' quotes
	*/
	if searchPresent {
		if searchModifierPresent {
			// # Modifier Based Search: Search by the provided 'search modifier'
			if isExactSearch {
				// # Exact Search
				searchQuery = fmt.Sprintf(" AND (%s = $%d)", searchModifier, paramIndex)
				search = cleanSearch
			} else {
				// # Partial Search
				searchQuery = fmt.Sprintf(" AND (%s ILIKE $%d)", searchModifier, paramIndex)
				search = partialSearch
			}
		} else {
			// # Default Search: Search by the 'changed by user name' and 'txn type (action)'
			if isExactSearch {
				// # Exact Search
				searchQuery = fmt.Sprintf(" AND (t.changed_by_username = $%d OR t.action = $%d)", paramIndex, paramIndex)
				search = cleanSearch
			} else {
				// # Partial Search
				searchQuery = fmt.Sprintf(" AND (t.changed_by_username ILIKE $%d OR t.action ILIKE $%d)", paramIndex, paramIndex)
				search = partialSearch
			}
		}
		query += searchQuery
		queryParams = append(queryParams, search)
	}

	// # Execute Query
	err = t.DB.QueryRow(ctx, query, queryParams...).Scan(&resultCount)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.Wrap(err, "no rows found for transaction count")
		}
		return nil, errors.Wrap(err, "unable to get count of transaction rows")
	}

	if pageSize > 1 {
		if (resultCount % pageSize) == 0 {
			totalPages = resultCount / pageSize
		} else {
			totalPages = resultCount/pageSize + 1
		}
	} else {
		totalPages = 1
	}

	response = map[string]any{
		"totalDocuments": resultCount,
		"totalPages":     totalPages,
		"currentPage":    pageNo,
		"data":           transactions,
		"search_params":  constants.ItemTransactionSearchParams,
	}

	// # Return the 'item transactions'
	return response, nil
}
