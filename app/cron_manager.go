package app

import (
	"context"
	"fmt"
	"inventory/model"
	"sync"
	"time"

	core_proto "proto/core"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/go-co-op/gocron"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/rs/zerolog"
)

// CronManager defines methods of CronManager service to be implemented
type CronManager interface {
	EoMLPSnapShotCron()
}

// CronManagerOpts contains arguments to be accepted for new instance of CronManager service
type CronManagerOpts struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// CronManagerImpl implements CronManager service
type CronManagerImpl struct {
	App                              *App
	DB                               *pgxpool.Pool
	Logger                           *zerolog.Logger
	SchedulerLock                    sync.Mutex
	ScheduledEoMLPSnapshotWarehouses map[string]bool // to track scheduled warehouses
}

// InitCronManager returns initializes CronManager service
func InitCronManager(opts *CronManagerOpts) CronManager {
	i := &CronManagerImpl{
		App:                              opts.App,
		DB:                               opts.DB,
		Logger:                           opts.Logger,
		ScheduledEoMLPSnapshotWarehouses: make(map[string]bool),
	}
	return i
}

// EoMLPSnapShotCron is a method to be implemented for CronManager service
func (c *CronManagerImpl) EoMLPSnapShotCron() {
	fmt.Println("Starting End of Month LP Snapshot Cron")

	loc, _ := time.LoadLocation("UTC")
	mainScheduler := gocron.NewScheduler(loc)

	// This job runs every 1 day to evaluate and schedule warehouse jobs
	mainScheduler.Every(1).Day().At("23:00").Do(c.ScheduleEoMLPSnapShotCron)

	mainScheduler.StartAsync()
}

func isEndOfMonth(t time.Time) bool {
	return t.AddDate(0, 0, 1).Month() != t.Month()
}

func (c *CronManagerImpl) ScheduleEoMLPSnapShotCron() {
	fmt.Println("Scheduling End of Month LP Snapshot Cron")
	ctx := context.Background()

	var warehouseIDs []string
	query := `SELECT DISTINCT warehouse_id FROM container_wise_inventory WHERE item_id IS NOT NULL`
	rows, err := c.DB.Query(ctx, query)
	if err != nil {
		c.Logger.Error().Err(err).Msg("Failed to fetch distinct warehouse IDs")
		return
	}
	defer rows.Close()

	for rows.Next() {
		var warehouseID string
		if err := rows.Scan(&warehouseID); err != nil {
			c.Logger.Error().Err(err).Msg("Failed to scan warehouse ID")
			return
		}
		warehouseIDs = append(warehouseIDs, warehouseID)
	}
	if err := rows.Err(); err != nil {
		c.Logger.Error().Err(err).Msg("Error occurred while iterating over rows")
		return
	}

	if len(warehouseIDs) == 0 {
		c.Logger.Info().Msg("No warehouses found for EoM LP snapshot")
		return
	}

	warehousesResp, err := c.App.GrpcClient.Core.Client.GetWarehouses(ctx, &core_proto.GetWarehousesRequest{
		WarehouseId: warehouseIDs,
	})
	if err != nil {
		c.Logger.Error().Err(err).Msg("Failed to fetch warehouse data from core service")
		return
	}

	for _, warehouse := range warehousesResp.Warehouses {
		c.SchedulerLock.Lock()
		if c.ScheduledEoMLPSnapshotWarehouses[warehouse.GetWarehouseId()] {
			c.SchedulerLock.Unlock()
			continue // already scheduled
		}
		c.ScheduledEoMLPSnapshotWarehouses[warehouse.GetWarehouseId()] = true
		c.SchedulerLock.Unlock()

		location, err := time.LoadLocation(warehouse.GetTimezone())
		if err != nil {
			c.Logger.Error().Str("WarehouseID", warehouse.WarehouseId).Str("TimeZone", warehouse.GetTimezone()).Err(err).Msg("Invalid time zone")
			continue
		}

		scheduler := gocron.NewScheduler(location)

		// Schedule daily at 23:50 in warehouse local time
		_, err = scheduler.Every(1).Day().At("23:50").Do(func(warehouseID string, loc *time.Location) {
			now := time.Now().In(loc)
			if isEndOfMonth(now) {
				go c.runEoMLPSnapshotJob(warehouseID, now)
			}
		}, warehouse.GetWarehouseId(), location)

		if err != nil {
			c.Logger.Error().Str("WarehouseID", warehouse.GetWarehouseId()).Err(err).Msg("Failed to schedule EoM cron")
			continue
		}

		scheduler.StartAsync()

		c.Logger.Info().Str("WarehouseID", warehouse.GetWarehouseId()).Str("TimeZone", warehouse.GetTimezone()).Msg("Scheduled EoM LP snapshot cron")
	}
}

func (c *CronManagerImpl) runEoMLPSnapshotJob(warehouseID string, now time.Time) {
	fmt.Println("Running EoM LP Snapshot Job for Warehouse:", warehouseID)

	// Creating required sheet
	f := excelize.NewFile()
	headers := []string{"Client Name", "Container Code", "Container Type", "Ref No", "Location Code", "On Hold", "Item ID", "SKU", "Scannable", "Name", "Description", "Batch Number", "Serial Number", "Expiration Date", "Quantity", "Initial Qty", "Base Unit", "Received At"}

	// fetch all containers for the warehouse containing items where lp type is not loose
	ctx := context.Background()

	query := `SELECT c.client_name, c.container_id, c.code AS container_code, ct.name as container_type, c.ref_no, l.code AS location_code, l.on_hold, c.item_id, c.sku, c.batch_number, c.serial_number, c.expiration_date, c.quantity, c.initial_qty, c.received_at, i.base_unit, i.scannable, i.name, i.description
			FROM container_wise_inventory c
			FULL OUTER JOIN (
				SELECT DISTINCT ON (location_id) *
				FROM location_wise_inventory
			) AS l ON l.location_id = c.location_id
			INNER JOIN container_type ct on ct.id = c.container_type_id
			FULL OUTER JOIN inventory i on i.item_id = c.item_id
			WHERE c.warehouse_id = $1 AND c.quantity > 0 AND ct.name != 'loose'`
	rows, err := c.DB.Query(ctx, query, warehouseID)
	if err != nil {
		c.Logger.Error().Err(err).Str("WarehouseID", warehouseID).Msg("Failed to fetch containers for EoM LP snapshot")
		return
	}
	defer rows.Close()

	clientMap := make(map[string]bool)
	sheetRowMap := make(map[string]int) // track current row per sheet

	for rows.Next() {
		var lpData model.ContainerReportData
		err := rows.Scan(
			&lpData.ClientName,
			&lpData.ContainerID,
			&lpData.ContainerCode,
			&lpData.ContainerType,
			&lpData.RefNo,
			&lpData.LocationCode,
			&lpData.OnHold,
			&lpData.ItemID,
			&lpData.Sku,
			&lpData.BatchNumber,
			&lpData.SerialNumber,
			&lpData.ExpirationDate,
			&lpData.Quantity,
			&lpData.InitialQty,
			&lpData.ReceivedAt,
			&lpData.BaseUnit,
			&lpData.Scannable,
			&lpData.Name,
			&lpData.Description,
		)
		if err != nil {
			c.Logger.Error().Err(err).Str("WarehouseID", warehouseID).Msg("Failed to scan container data for EoM LP snapshot")
			return
		}

		sheetName := *lpData.ClientName
		if _, created := clientMap[sheetName]; !created {
			f.NewSheet(sheetName)

			for i, header := range headers {
				cell := fmt.Sprintf("%s1", excelize.ToAlphaString(i))
				f.SetCellValue(sheetName, cell, header)
			}
			clientMap[sheetName] = true
			sheetRowMap[sheetName] = 2
		}

		row := sheetRowMap[sheetName]

		// If completely empty (like no ItemID, Sku), skip row
		if lpData.ItemID == nil && lpData.Sku == nil {
			continue
		}

		values := []any{
			str(lpData.ClientName),
			lpData.ContainerCode,
			lpData.ContainerType,
			str(lpData.RefNo),
			str(lpData.LocationCode),
			boolStr(lpData.OnHold),
			str(lpData.ItemID),
			str(lpData.Sku),
			str(lpData.Scannable),
			str(lpData.Name),
			str(lpData.Description),
			str(lpData.BatchNumber),
			str(lpData.SerialNumber),
			timeStr(lpData.ExpirationDate),
			lpData.Quantity,
			lpData.InitialQty,
			str(lpData.BaseUnit),
			timeStr(lpData.ReceivedAt),
		}

		for i, v := range values {
			cell := fmt.Sprintf("%s%d", excelize.ToAlphaString(i), row)
			f.SetCellValue(sheetName, cell, v)
		}

		sheetRowMap[sheetName]++
	}
	if err := rows.Err(); err != nil {
		c.Logger.Error().Err(err).Str("WarehouseID", warehouseID).Msg("Error occurred while iterating over rows for EoM LP snapshot")
		return
	}

	f.DeleteSheet("Sheet1") // Remove default sheet

	// Write to buffer or save to S3
	buffer, err := f.WriteToBuffer()
	if err != nil {
		c.Logger.Error().Err(err).Str("WarehouseID", warehouseID).Msg("Failed to write Excel to buffer")
		return
	}

	date := now.Format("January-2006")
	filename := "EoM_LP_Snapshot-" + warehouseID + "-" + date + ".xlsx"

	_, err = c.App.SSS.AddFileToS3New(filename, c.App.Config.AWSConfig.LPSnapShotBucket, int64(len(buffer.Bytes())), buffer.Bytes())
	if err != nil {
		c.Logger.Error().Err(err).Str("WarehouseID", warehouseID).Msg("Failed to upload EoM LP snapshot to S3")
		return
	}

	fmt.Println("Completing EoM LP Snapshot Job for Warehouse:", warehouseID)
}

// Convert nil pointer to empty string
func str(v *string) string {
	if v != nil {
		return *v
	}
	return ""
}

// Convert nil time to string
func timeStr(t *time.Time) string {
	if t != nil {
		return t.Format("Jan 2, 2006")
	}
	return ""
}

// Convert nil bool to string
func boolStr(b *bool) string {
	if b != nil {
		if *b {
			return "true"
		}
		return "false"
	}
	return ""
}
