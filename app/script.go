package app

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/rs/zerolog"
	errors "github.com/vasupal1996/goerror"

	core_proto "proto/core"
)

// Script defines methods of Script service to be implemented
type Script interface {
	UpdateDynamicColumns(string, string) (bool, error)
}

// ScriptOpts contains arguments to be accepted for new instance of Script service
type ScriptOpts struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// ScriptImpl implements Script service
type ScriptImpl struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// InitScript returns initializes Script service
func InitScript(opts *ScriptOpts) Script {
	i := &ScriptImpl{
		App:    opts.App,
		DB:     opts.DB,
		Logger: opts.Logger,
	}
	return i
}

func (s *ScriptImpl) UpdateDynamicColumns(clientID, columnName string) (bool, error) {
	ctx := context.TODO()
	res, err := s.App.GrpcClient.Core.Client.UpdateItemMasterDynamicColumns(ctx, &core_proto.UpdateItemMasterDynamicColumnsRequest{
		ClientId:   clientID,
		ColumnName: columnName,
	})
	if err != nil {
		return false, err
	}

	for _, item := range res.GetDynamicItemColumnValues() {
		dynamicColumn := make(map[string]string)
		dynamicColumn[columnName] = item.GetValue()

		fmt.Println(item.GetItemId())
		fmt.Println(dynamicColumn)

		query := `UPDATE inventory SET dynamic_column = $1 WHERE item_id = $2`
		commandTag, err := s.DB.Exec(ctx, query, &dynamicColumn, item.GetItemId())
		if err != nil {
			return false, errors.Wrap(err, "Failed to update dynamic column", &errors.DBError)
		}
		if commandTag.RowsAffected() != 1 {
			return false, errors.New("Dynamic column wasn't updated for item "+item.GetItemId(), &errors.DBError)
		}
	}

	return true, nil
}
