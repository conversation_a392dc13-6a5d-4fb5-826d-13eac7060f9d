package app

import (
	"inventory/server/handler"

	"github.com/getsentry/sentry-go"
	"github.com/pkg/errors"
	"github.com/skip2/go-qrcode"
)

type Utils interface {
	HandlePanic(*handler.RequestContext)
	// # Generate QR Code
	GenerateQrCode(string) ([]byte, error)
}

// UtilsOpts contains arguments to be accepted for new instance of Utility service
type UtilsOpts struct {
	App *App
}

// UtilsImpl implements Utility service
type UtilsImpl struct {
	App *App
}

// InitUtils returns initializes Utility service
func InitUtils(opts *UtilsOpts) Utils {
	i := &UtilsImpl{
		App: opts.App,
	}
	return i
}

// Global function for handling any panics
func (u *UtilsImpl) HandlePanic(requestCTX *handler.RequestContext) {
	panic := recover()
	if panic != nil {
		sentry.CurrentHub().Recover(panic)
		if requestCTX != nil {
			err := errors.New("Panic: unhandled exception occured")
			requestCTX.SetErr(err, 500)
		}
	}
}

// # Generate QR Code
func (u *UtilsImpl) GenerateQrCode(LpCode string) ([]byte, error) {
	// # Generate the QR code
	qrCode, err := qrcode.New(LpCode, qrcode.Medium)
	if err != nil {
		return nil, err
	}

	// # Create a PNG image of the QR code
	pngBytes, err := qrCode.PNG(256)
	if err != nil {
		return nil, err
	}

	// # Return the PNG image
	return pngBytes, nil
}
