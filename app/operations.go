package app

import (
	"context"
	"encoding/json"
	"fmt"
	"inventory/model"
	core_proto "proto/core"
	integrations_proto "proto/integrations"
	outbound_proto "proto/outbound"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/pkg/errors"
	"github.com/rs/zerolog"
	uuid "github.com/satori/go.uuid"
	"go.mongodb.org/mongo-driver/bson"
)

// Operations defines methods of Operations service to be implemented
type Operations interface {
	CheckClientDeleted(string) error
	GetItemFromInventory(string, *string, *string, *time.Time) (*model.Inventory, error)
	GetItemFromInventoryByItemId(string) (*model.Inventory, error)
	InsertItemInLocation(pgx.Tx, context.Context, string, string, int, int, *model.Inventory, *string, *string, *time.Time) error
	RemoveItemFromLocation(pgx.Tx, context.Context, string, string, string, *string, *string, *time.Time) error
	InsertItemInContainer(pgx.Tx, context.Context, uuid.UUID, string, string, int, int, *model.Inventory, *string, *string, *time.Time, *time.Time) error
	RemoveItemFromContainer(pgx.Tx, context.Context, uuid.UUID, string, string, string, *string, *string, *time.Time) error
	GetSkuQuantityFromContainer(pgx.Tx, context.Context, uuid.UUID, string, string, string) (map[string]int, error)
	GetContainerTypeID(string, string) (*int, error)
	GetContainerNameByID(int) (*string, error)
	GetLocationWiseInventory(string, string, []string, *int, *int) (any, error)
	BackOrderAutoAlloation([]string, []string, string, bool)
	BackOrderAutoDeAlloation([]model.OrderDeAllocationItems)
	CreateTransaction(pgx.Tx, *model.TransactionData) error
	UpdateShopifyInventory(string)
}

// OperationsOpts contains arguments to be accepted for new instance of Operations service
type OperationsOpts struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// OperationsImpl implements Operations service
type OperationsImpl struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// InitOperations returns initializes Operations service
func InitOperations(opts *OperationsOpts) Operations {
	i := &OperationsImpl{
		App:    opts.App,
		DB:     opts.DB,
		Logger: opts.Logger,
	}
	return i
}

func (o *OperationsImpl) CheckClientDeleted(clientID string) error {
	var is_client_deleted bool
	query := `SELECT is_client_deleted FROM inventory WHERE client_id = $1 LIMIT 1`
	err := o.DB.QueryRow(context.TODO(), query, clientID).Scan(&is_client_deleted)
	if err != nil {
		if err == pgx.ErrNoRows {
			return errors.New("client does not exist")
		}
		return errors.Wrap(err, "client does not exist")
	}

	if is_client_deleted {
		return errors.New("this client is deleted")
	}

	return nil
}

func (o *OperationsImpl) GetItemFromInventoryByItemId(itemID string) (*model.Inventory, error) {
	var inventory model.Inventory

	query := `SELECT * FROM inventory WHERE item_id = $1`
	err := o.DB.QueryRow(context.TODO(), query, itemID).Scan(
		&inventory.ID,
		&inventory.ItemID,
		&inventory.ClientID,
		&inventory.WarehouseID,
		&inventory.Quantity,
		&inventory.AvailableQty,
		&inventory.PickableQty,
		&inventory.BaseUnit,
		&inventory.SKU,
		&inventory.Scannable,
		&inventory.CreatedAt,
		&inventory.UpdatedAt,
		&inventory.IsDeleted,
		&inventory.OnHold,
		&inventory.IsBatchControlled,
		&inventory.IsTrackedBySerialNo,
		&inventory.Name,
		&inventory.Description,
		&inventory.OnHoldBy,
		&inventory.IsPerishable,
		&inventory.TrackedBy,
		&inventory.Image,
		&inventory.BackOrders,
		&inventory.ClientName,
		&inventory.IsClientDeleted,
		&inventory.IsKit,
		&inventory.DynamicColumn,

		// # LeanShip #
		&inventory.IsLeanShip,
	)
	if err == pgx.ErrNoRows {
		return nil, errors.Wrap(err, "item doesn't exist in the inventory")
	}
	if err != nil {
		return nil, errors.Wrap(err, "unable to query inventory")
	}

	return &inventory, nil
}

// Get item details from inventory
func (o *OperationsImpl) GetItemFromInventory(itemID string, batchNo, serialNo *string, expDate *time.Time) (*model.Inventory, error) {
	var inventory model.Inventory

	query := `SELECT * FROM inventory WHERE item_id = $1`
	err := o.DB.QueryRow(context.TODO(), query, itemID).Scan(
		&inventory.ID,
		&inventory.ItemID,
		&inventory.ClientID,
		&inventory.WarehouseID,
		&inventory.Quantity,
		&inventory.AvailableQty,
		&inventory.PickableQty,
		&inventory.BaseUnit,
		&inventory.SKU,
		&inventory.Scannable,
		&inventory.CreatedAt,
		&inventory.UpdatedAt,
		&inventory.IsDeleted,
		&inventory.OnHold,
		&inventory.IsBatchControlled,
		&inventory.IsTrackedBySerialNo,
		&inventory.Name,
		&inventory.Description,
		&inventory.OnHoldBy,
		&inventory.IsPerishable,
		&inventory.TrackedBy,
		&inventory.Image,
		&inventory.BackOrders,
		&inventory.ClientName,
		&inventory.IsClientDeleted,
		&inventory.IsKit,
		&inventory.DynamicColumn,

		// # LeanShip #
		&inventory.IsLeanShip,
	)
	if err == pgx.ErrNoRows {
		return nil, errors.Wrap(err, "item doesn't exist in the inventory")
	}
	if err != nil {
		return nil, errors.Wrap(err, "unable to query inventory")
	}

	if inventory.IsBatchControlled {
		if batchNo == nil {
			return nil, errors.New("this item is batch controlled")
		}
	}
	if !inventory.IsBatchControlled {
		if batchNo != nil {
			return nil, errors.New("this item is not batch controlled")
		}
	}
	if inventory.IsTrackedBySerialNo {
		if serialNo == nil {
			return nil, errors.New("this item is tracked by serial number")
		}
	}
	if !inventory.IsTrackedBySerialNo {
		if serialNo != nil {
			return nil, errors.New("this item is not tracked by serial number")
		}
	}
	if inventory.IsPerishable {
		if expDate == nil {
			return nil, errors.New("this item is comes with an expiration date")
		}
	}
	if !inventory.IsPerishable {
		if expDate != nil {
			return nil, errors.New("this item doesn't come with an expiration date")
		}
	}

	return &inventory, nil
}

// Inserts item in location
func (o *OperationsImpl) InsertItemInLocation(tx pgx.Tx, ctx context.Context, locationID, warehouseID string, quantity, available_qty int, inventoryItem *model.Inventory, batchNo, serialNo *string, expDate *time.Time) error {
	// If the location is empty, it will have only one row with sku column containing null
	// Get the destination location row matching location id
	var location_item, on_hold_by_id *string
	var code, area_name, area_id string
	var on_hold bool
	query := `SELECT item_id, code, area_name, area_id, on_hold, on_hold_by_id FROM location_wise_inventory WHERE location_id = $1 AND warehouse_id = $2 LIMIT 1`
	err := tx.QueryRow(ctx, query, locationID, warehouseID).Scan(&location_item, &code, &area_name, &area_id, &on_hold, &on_hold_by_id)
	if err != nil {
		tx.Rollback(ctx)
		return errors.Wrap(err, "unable to get the location by id")
	}

	if location_item == nil {
		// Location is empty. Insert item and update quantity
		query = `UPDATE location_wise_inventory SET item_id = $1, sku = $2, scannable = $3, quantity = $4, available_qty = $5, last_updated_at = $6, batch_number = $7, serial_number = $8, expiration_date = $9, client_id = $10, client_name = $11 WHERE location_id = $12 AND warehouse_id = $13`
		commandTag, err := tx.Exec(ctx, query, inventoryItem.ItemID, inventoryItem.SKU, inventoryItem.Scannable, quantity, available_qty, time.Now().UTC(), batchNo, serialNo, expDate, inventoryItem.ClientID, inventoryItem.ClientName, locationID, warehouseID)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "unable to update sku in location")
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return errors.New("sku didn't get updated on the location")
		}

	} else {
		// Location is not empty but does not contain this specific item. So insert a row containing this item
		query = `INSERT INTO location_wise_inventory (item_id, sku, scannable, location_id, warehouse_id, client_id, client_name, code, quantity, available_qty, batch_number, serial_number, expiration_date, area_name, area_id, created_at, on_hold, on_hold_by_id) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)`
		commandTag, err := tx.Exec(ctx, query, inventoryItem.ItemID, inventoryItem.SKU, inventoryItem.Scannable, locationID, warehouseID, inventoryItem.ClientID, inventoryItem.ClientName, code, quantity, available_qty, batchNo, serialNo, expDate, area_name, area_id, time.Now().UTC(), on_hold, on_hold_by_id)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "unable to insert new row in location")
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return errors.New("new row in wasn't inserted")
		}
	}

	return nil
}

// Removes item from Location
func (o *OperationsImpl) RemoveItemFromLocation(tx pgx.Tx, ctx context.Context, itemID, locationID, warehouseID string, batchNo, serialNo *string, expDate *time.Time) error {
	// remove row or set null in case of last row

	// Get Count of rows containing location id
	var count int
	query := `SELECT COUNT(location_id) FROM location_wise_inventory WHERE location_id = $1 AND warehouse_id = $2`
	err := tx.QueryRow(ctx, query, locationID, warehouseID).Scan(&count)
	if err != nil {
		tx.Rollback(ctx)
		return errors.Wrap(err, "unable to get count of rows containing source location id")
	}

	if count > 1 {
		// Location contains other items

		// Delete row containing item, batch, serial location_id, warehouse_id and client_id
		query := `DELETE FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND location_id = $4 AND warehouse_id = $5 AND (expiration_date = $6 OR $6 IS NULL)`
		commandTag, err := tx.Exec(ctx, query, itemID, batchNo, serialNo, locationID, warehouseID, expDate)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "unable to delete location row")
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return errors.New("location row wasn't deleted")
		}

	} else if count == 1 {
		// Location is going to get empty after this item is moved
		// At this point, only one location row should exist

		// Set item details to null and Update quantity to 0
		query = `UPDATE location_wise_inventory SET item_id = $1, sku = $2, batch_number = $3, serial_number = $4, expiration_date = $5, scannable = $6, quantity = $7, available_qty = $8, client_id = $9, client_name = $10 WHERE item_id = $11 AND (batch_number = $12 OR $12 IS NULL) AND (serial_number = $13 OR $13 IS NULL) AND (expiration_date = $14 OR $14 IS NULL) AND location_id = $15 AND warehouse_id = $16`
		commandTag, err := tx.Exec(ctx, query, nil, nil, nil, nil, nil, nil, 0, 0, nil, nil, itemID, batchNo, serialNo, expDate, locationID, warehouseID)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "unable to empty location")
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return errors.New("row containing last location wasn't updated")
		}

	} else {
		// location does not exist
		tx.Rollback(ctx)
		return errors.New("something is wrong - location does not exist")
	}

	return nil
}

// Inserts item in container
func (o *OperationsImpl) InsertItemInContainer(tx pgx.Tx, ctx context.Context, containerID uuid.UUID, locationID, warehouseID string, quantity, available_qty int, inventoryItem *model.Inventory, batchNo, serialNo *string, expDate, receivedAt *time.Time) error {
	// If the container is empty, it will have only one row with sku column containing null

	if receivedAt == nil {
		now := time.Now().UTC()
		receivedAt = &now
	}

	// Get the container row matching container id
	var container_item, ref_no *string
	var container_code string
	var container_type_id *int
	var created_at time.Time
	var first_used_at *time.Time
	query := `SELECT item_id, code, container_type_id, created_at, first_used_at, ref_no FROM container_wise_inventory WHERE container_id = $1 AND warehouse_id = $2 LIMIT 1`
	err := tx.QueryRow(ctx, query, containerID, warehouseID).Scan(&container_item, &container_code, &container_type_id, &created_at, &first_used_at, &ref_no)
	if err != nil {
		tx.Rollback(ctx)
		return errors.Wrap(err, "unable to get the container by id")
	}

	if container_item == nil {
		// Container is empty. Insert sku and update quantity
		query = `UPDATE container_wise_inventory SET item_id = $1, sku = $2, scannable = $3, quantity = $4, available_qty = $5, last_updated_at = $6, batch_number = $7, serial_number = $8, expiration_date = $9, client_id = $10, client_name = $11, location_id = $12, first_used_at = $13, ref_no = $14, received_at = $15, initial_qty = $4 WHERE container_id = $16 AND warehouse_id = $17`
		commandTag, err := tx.Exec(ctx, query, inventoryItem.ItemID, inventoryItem.SKU, inventoryItem.Scannable, quantity, available_qty, time.Now().UTC(), batchNo, serialNo, expDate, inventoryItem.ClientID, inventoryItem.ClientName, locationID, time.Now().UTC(), ref_no, receivedAt, containerID, warehouseID)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "unable to update sku in container")
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return errors.New("sku didn't get updated on the container")
		}

	} else {
		// Container is not empty but does not contain this specific item. So insert a row containing this item
		query = `INSERT INTO container_wise_inventory (item_id, sku, scannable, container_id, container_type_id, location_id, warehouse_id, client_id, client_name, code, quantity, available_qty, batch_number, serial_number, expiration_date, created_at, first_used_at, ref_no, last_updated_at, received_at, initial_qty) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21)`
		commandTag, err := tx.Exec(ctx, query, inventoryItem.ItemID, inventoryItem.SKU, inventoryItem.Scannable, containerID, container_type_id, locationID, warehouseID, inventoryItem.ClientID, inventoryItem.ClientName, container_code, quantity, available_qty, batchNo, serialNo, expDate, created_at, first_used_at, ref_no, time.Now().UTC(), receivedAt, quantity)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "unable to insert new row in container")
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return errors.New("new row in wasn't inserted in container")
		}
	}

	return nil
}

// Removes item from container
func (o *OperationsImpl) RemoveItemFromContainer(tx pgx.Tx, ctx context.Context, containerID uuid.UUID, itemID, locationID, warehouseID string, batchNo, serialNo *string, expDate *time.Time) error {
	// If container is going to get empty after the item is moved, only one row should exist with item_id null and quantity = 0
	// If container contains other items, then delete the row matched by item_id, container_id and location_id

	// Get Count of rows containing container id
	var count int
	query := `SELECT COUNT(container_id) FROM container_wise_inventory WHERE container_id = $1 AND location_id = $2 AND warehouse_id = $3`
	err := tx.QueryRow(ctx, query, containerID, locationID, warehouseID).Scan(&count)
	if err != nil {
		tx.Rollback(ctx)
		return errors.Wrap(err, "unable to get count of rows containing source location id")
	}

	if count > 1 {
		// Container contains other items

		// Delete row containing container id, item_id, location_id, warehouse_id
		query := `DELETE FROM container_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND container_id = $4 AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)`
		commandTag, err := tx.Exec(ctx, query, itemID, batchNo, serialNo, containerID, locationID, warehouseID, expDate)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "unable to delete row containing container id, location id and sku")
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return errors.New("row containing container id, location id and sku was not deleted")
		}

	} else if count == 1 {
		// Container is going to get empty after this item is moved
		// At this point, only one container row should exist

		// Set sku details to null and Update quantity to 0
		query = `UPDATE container_wise_inventory SET item_id = $1, batch_number = $2, serial_number = $3, sku = $4, scannable = $5, quantity = $6, available_qty = $7, expiration_date = $8, client_id = $9, client_name = $10, location_id = $11, first_used_at = $12 WHERE item_id = $13 AND (batch_number = $14 OR $14 IS NULL) AND (serial_number = $15 OR $15 IS NULL) AND container_id = $16 AND location_id = $17 AND warehouse_id = $18 AND (expiration_date = $19 OR $19 IS NULL)`
		commandTag, err := tx.Exec(ctx, query, nil, nil, nil, nil, nil, 0, 0, nil, nil, nil, nil, nil, itemID, batchNo, serialNo, containerID, locationID, warehouseID, expDate)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "unable to empty container")
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return errors.New("row containing last container wasn't updated")
		}

	} else {
		// container does not exist
		tx.Rollback(ctx)
		return errors.New("something is wrong - container does not exist")
	}

	return nil
}

func (o *OperationsImpl) GetSkuQuantityFromContainer(tx pgx.Tx, ctx context.Context, containerID uuid.UUID, locationID, warehouseID, clientID string) (map[string]int, error) {
	// Items and their quantity present in the container
	// map {sku: quantity}
	sku_qty_map := make(map[string]int)

	// Get sku and quantity from the container
	query := `SELECT sku, quantity FROM container_wise_inventory WHERE container_id = $1 AND location_id = $2 AND warehouse_id = $3 AND client_id = $4`
	rows, err := tx.Query(ctx, query, containerID, locationID, warehouseID, clientID)
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "error querying all container rows")
	}
	for rows.Next() {
		var sku *string
		var batch_number *string
		var serial_number *string
		var quantity int
		if err := rows.Scan(&sku, &batch_number, &serial_number, &quantity); err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "error scanning container rows into variables")
		}

		if sku != nil {
			// Item exists in the container
			sku_qty_map[*sku] = quantity
		}
	}
	if err_rows := rows.Err(); err_rows != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err_rows, "error reading container rows")
	}

	return sku_qty_map, nil
}

func (o *OperationsImpl) GetContainerTypeID(name, whID string) (*int, error) {
	var container_type_id int
	query := `SELECT id FROM container_type WHERE name = $1 AND warehouse_id = $2 LIMIT 1`
	err := o.DB.QueryRow(context.TODO(), query, name, whID).Scan(&container_type_id)
	if err != nil {
		return nil, errors.Wrap(err, "container type not found")
	}

	return &container_type_id, nil
}

func (o *OperationsImpl) GetContainerNameByID(type_id int) (*string, error) {
	var name string
	query := `SELECT name FROM container_type WHERE id = $1 LIMIT 1`
	err := o.DB.QueryRow(context.TODO(), query, type_id).Scan(&name)
	if err != nil {
		return nil, errors.Wrap(err, "container type not found")
	}

	return &name, nil
}

// GetLocationWiseInventory returns location wise inventory
func (o *OperationsImpl) GetLocationWiseInventory(warehouseID, search string, clientIDs []string, pageNo, pageSize *int) (any, error) {
	var locationWiseInventory []model.LocationWiseInventory
	var offset *int64
	searchQuery := "%" + search + "%"
	all := false
	if *pageNo < 1 || *pageSize < 1 {
		all = true
		*pageNo = 1
		*pageSize = 10
	}
	offset_ := int64((*pageNo - 1) * *pageSize)
	offset = &offset_
	if all {
		pageSize = nil
		offset = nil
	}

	var query string
	var rows pgx.Rows
	var err error
	if len(clientIDs) > 0 {
		query = `SELECT location_wise_inventory.*, inventory.name, inventory.base_unit, inventory.is_kit, inventory.on_hold FROM location_wise_inventory INNER JOIN inventory ON location_wise_inventory.item_id = inventory.item_id WHERE (location_wise_inventory.sku ILIKE $1 OR inventory.name ILIKE $1) AND location_wise_inventory.warehouse_id = $2 AND location_wise_inventory.client_id = ANY($3) ORDER BY location_wise_inventory.code ASC LIMIT $4 OFFSET $5`
		rows, err = o.DB.Query(context.TODO(), query, searchQuery, warehouseID, clientIDs, pageSize, offset)
	} else {
		query = `SELECT location_wise_inventory.*, inventory.name, inventory.base_unit, inventory.is_kit, inventory.on_hold FROM location_wise_inventory INNER JOIN inventory ON location_wise_inventory.item_id = inventory.item_id WHERE (location_wise_inventory.sku ILIKE $1 OR inventory.name ILIKE $1) AND location_wise_inventory.warehouse_id = $2 ORDER BY location_wise_inventory.code ASC LIMIT $3 OFFSET $4`
		rows, err = o.DB.Query(context.TODO(), query, searchQuery, warehouseID, pageSize, offset)
	}
	if err != nil {
		return nil, errors.Wrap(err, "failed to query location wise inventory")
	}
	defer rows.Close()

	for rows.Next() {
		var location model.LocationWiseInventory
		err := rows.Scan(
			&location.ID,
			&location.ItemID,
			&location.WarehouseID,
			&location.ClientID,
			&location.LocationID,
			&location.Code,
			&location.Quantity,
			&location.AvailableQty,
			&location.AreaName,
			&location.AreaID,
			&location.OnHold,
			&location.OnHoldByID,
			&location.LastUpdatedAt,
			&location.LastUpdatedByID,
			&location.LastUpdatedByName,
			&location.SKU,
			&location.Scannable,
			&location.BatchNumber,
			&location.SerialNumber,
			&location.ExpirationDate,
			&location.CreatedAt,
			&location.ClientName,
			&location.IsPrime,
			&location.Name,
			&location.BaseUnit,
			&location.IsKit,
			&location.ItemOnHold,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into location schema")
		}
		locationWiseInventory = append(locationWiseInventory, location)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	var resultCount, totalPages int
	if len(clientIDs) > 0 {
		query := `SELECT COUNT(location_wise_inventory.id) FROM location_wise_inventory INNER JOIN inventory ON location_wise_inventory.item_id = inventory.item_id WHERE (location_wise_inventory.sku ILIKE $1 OR inventory.name ILIKE $1) AND location_wise_inventory.warehouse_id = $2 AND location_wise_inventory.client_id = ANY($3)`
		err = o.DB.QueryRow(context.TODO(), query, searchQuery, warehouseID, clientIDs).Scan(&resultCount)
	} else {
		query := `SELECT COUNT(location_wise_inventory.id) FROM location_wise_inventory INNER JOIN inventory ON location_wise_inventory.item_id = inventory.item_id WHERE (location_wise_inventory.sku ILIKE $1 OR inventory.name ILIKE $1) AND location_wise_inventory.warehouse_id = $2`
		err = o.DB.QueryRow(context.TODO(), query, searchQuery, warehouseID).Scan(&resultCount)
	}
	if err != nil {
		return nil, errors.Wrap(err, "unable to get count of location wise inventory rows")
	}

	if !all {
		if (resultCount % *pageSize) == 0 {
			totalPages = resultCount / *pageSize
		} else {
			totalPages = resultCount / *pageSize + 1
		}
	} else {
		totalPages = 1
	}

	result := map[string]any{
		"totalDocuments": resultCount,
		"totalPages":     totalPages,
		"currentPage":    pageNo,
		"data":           locationWiseInventory,
	}
	return result, nil
}

func (o *OperationsImpl) BackOrderAutoAlloation(items, orderIDs []string, eventType string, allowReallocation bool) {
	fmt.Println("ITEMS :", items)
	fmt.Println("ORDER IDS :", orderIDs)
	fmt.Println("EVENT TYPE :", eventType)
	fmt.Println("ALLOW REALLOCATION :", allowReallocation)

	if len(items) == 0 {
		return
	}

	var reqItems []*outbound_proto.BackOrderAutoAllocationRequest_Items
	var inventoryItems []model.ItemsInvenotry
	var clientID string

	// Step 1: Fetch inventory for provided items
	query := `SELECT item_id, client_id, available_qty FROM inventory WHERE item_id = ANY($1)`
	rows, err := o.DB.Query(context.TODO(), query, items)
	if err != nil {
		sentry.CaptureException(err)
		o.Logger.Error().Err(err).Msg("Unable to fetch items from inventory")
		return
	}
	defer rows.Close()

	for rows.Next() {
		var inventoryItem model.ItemsInvenotry
		if err := rows.Scan(
			&inventoryItem.ItemID,
			&inventoryItem.ClientID,
			&inventoryItem.AvailableQty,
		); err != nil {
			sentry.CaptureException(err)
			o.Logger.Error().Err(err).Msg("Unable to scan items from inventory")
			continue
		}
		inventoryItems = append(inventoryItems, inventoryItem)
	}
	if err_rows := rows.Err(); err_rows != nil {
		sentry.CaptureException(err_rows)
		o.Logger.Error().Err(err_rows).Msg("Unable to read items from inventory")
		return
	}
	if len(inventoryItems) == 0 {
		o.Logger.Warn().Msg("No inventory items found")
		return
	}
	clientID = inventoryItems[0].ClientID

	// Step 2: Fetch kit items for the client
	kitItemRequest := &core_proto.GetClientKitItemsRequest{
		ClientId: clientID,
	}
	kitItemResponse, err := o.App.GrpcClient.Core.Client.GetClientKitItems(context.TODO(), kitItemRequest)
	if err != nil {
		sentry.CaptureException(err)
		o.Logger.Error().Err(err).Msg("Unable to fetch kit items")
		return
	}

	// Step 3: Build a set of all required sub-item IDs from kits
	requiredSubItemIDs := make(map[string]struct{})
	for _, itemID := range kitItemResponse.GetAllSubItems() {
		requiredSubItemIDs[itemID] = struct{}{}
	}

	// Step 4: Convert set to slice for querying
	subItemIDList := kitItemResponse.GetAllSubItems()

	// Step 5: Fetch inventory for all required sub-items
	subItemQtyMap := make(map[string]int32)
	if len(subItemIDList) > 0 {
		query := `SELECT item_id, available_qty FROM inventory WHERE item_id = ANY($1)`
		rows, err := o.DB.Query(context.TODO(), query, subItemIDList)
		if err != nil {
			sentry.CaptureException(err)
			o.Logger.Error().Err(err).Msg("Unable to fetch sub-item inventory")
			return
		}
		defer rows.Close()

		for rows.Next() {
			var itemID string
			var qty int32
			if err := rows.Scan(&itemID, &qty); err != nil {
				sentry.CaptureException(err)
				o.Logger.Error().Err(err).Msg("Unable to scan sub-item inventory")
				continue
			}
			subItemQtyMap[itemID] = qty
		}
		if err_rows := rows.Err(); err_rows != nil {
			sentry.CaptureException(err_rows)
			o.Logger.Error().Err(err_rows).Msg("Unable to read sub-item inventory")
			return
		}
	}

	// Step 6: Map item ID to its available quantity from initial inventory fetch
	availableQtyMap := make(map[string]int32)
	for _, item := range inventoryItems {
		availableQtyMap[item.ItemID] = item.AvailableQty
	}

	// Step 7: Track kits already added to avoid duplicates
	addedKits := make(map[string]bool)

	// Step 8: Process each item
	for _, item := range inventoryItems {
		if _, ok := requiredSubItemIDs[item.ItemID]; ok {
			// This is a sub-item. Check which kits it belongs to.
			for _, kitItem := range kitItemResponse.Kits {
				kitID := kitItem.GetKitId()
				kitSubItems := kitItem.GetSubItems()

				// Check if this kit contains our current sub-item
				contains := false
				for _, subItem := range kitSubItems {
					if subItem.GetItemId() == item.ItemID {
						contains = true
						break
					}
				}
				if !contains {
					continue
				}

				// Calculate how many full kits can be made
				minPossible := int32(-1)
				for _, subItem := range kitSubItems {
					requiredQty := subItem.GetQuantity()
					availableQty := subItemQtyMap[subItem.GetItemId()]
					if requiredQty == 0 {
						continue
					}
					count := availableQty / requiredQty
					if minPossible == -1 || count < minPossible {
						minPossible = count
					}
				}

				// Add kit allocation if possible
				if minPossible > 0 && !addedKits[kitID] {
					reqItems = append(reqItems, &outbound_proto.BackOrderAutoAllocationRequest_Items{
						ItemId:   kitID,
						Quantity: int32(minPossible),
					})
					addedKits[kitID] = true
				}
			}
			// Append the sub-item itself
			reqItems = append(reqItems, &outbound_proto.BackOrderAutoAllocationRequest_Items{
				ItemId:   item.ItemID,
				Quantity: item.AvailableQty,
			})
		} else {
			// Non-sub-items go directly
			reqItems = append(reqItems, &outbound_proto.BackOrderAutoAllocationRequest_Items{
				ItemId:   item.ItemID,
				Quantity: item.AvailableQty,
			})
		}
	}

	// Step 9: Prepare and send the allocation request
	req := outbound_proto.BackOrderAutoAllocationRequest{
		Items:             reqItems,
		ExcludedOrderIds:  orderIDs,
		EventType:         eventType,
		ClientId:          clientID,
		AllowReallocation: allowReallocation,
	}
	fmt.Println("Back Order AutoAllocation Request :", &req)
	response, err := o.App.GrpcClient.Outbound.Client.BackOrderAutoAllocation(context.TODO(), &req)
	if err != nil {
		sentry.CaptureException(err)
		o.Logger.Error().Err(err).Msg("Back Order Auto Allocation failed!!")
		return
	}
	if !response.GetSuccess() {
		err := errors.New("Back Order Auto Allocation failed!!!")
		sentry.CaptureException(err)
		o.Logger.Error().Err(err).Msg("Back Order Auto Allocation failed!!!")
	}
}

func (o *OperationsImpl) BackOrderAutoDeAlloation(data []model.OrderDeAllocationItems) {
	if len(data) > 0 {
		var reqItems []*outbound_proto.BackOrderAutoDeAllocationRequest_Items
		for _, item := range data {
			data := outbound_proto.BackOrderAutoDeAllocationRequest_Items{
				ItemId:   item.ItemID,
				Quantity: item.Quantity,
			}
			reqItems = append(reqItems, &data)
		}

		req := outbound_proto.BackOrderAutoDeAllocationRequest{
			Items: reqItems,
		}
		response, err := o.App.GrpcClient.Outbound.Client.BackOrderAutoDeAllocation(context.TODO(), &req)
		if err != nil {
			sentry.CaptureException(err)
			o.Logger.Error().Err(err).Msg("Back Order Auto DeAlloation failed!!")
		}
		if !response.GetSuccess() {
			err := errors.New("Back Order Auto DeAlloation failed!!!")
			sentry.CaptureException(err)
			o.Logger.Error().Err(err).Msg("Back Order Auto DeAlloation failed!!!")
		}
	}
}

func (o *OperationsImpl) CreateTransaction(tx pgx.Tx, data *model.TransactionData) error {
	ctx := context.TODO()
	if data.ClientID == "" || data.ClientName == "" {
		tx.Rollback(ctx)
		return errors.New("Client is missing from request data")
	}

	// Set optional variables
	var documentID, documentCode, documentPrn *string
	if data.DocumentCode != "" {
		v := data.DocumentCode
		documentCode = &v
	}
	if data.DocumentID != "" {
		v := data.DocumentID
		documentID = &v
	}
	if data.DocumentPrn != "" {
		v := data.DocumentPrn
		documentPrn = &v
	}

	// Get current quantity of item
	var current_qty int
	query := `SELECT quantity FROM inventory WHERE item_id = $1 LIMIT 1`
	err := tx.QueryRow(ctx, query, data.ItemID).Scan(&current_qty)
	if err != nil {
		tx.Rollback(ctx)
		return errors.Wrap(err, "unable to get the current qty of item from inventory")
	}

	var current_tracked_qty *int
	if data.BatchNumber != nil || data.SerialNumber != nil || data.ExpirationDate != nil {
		query = `SELECT quantity FROM tracked_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL)`
		err = tx.QueryRow(ctx, query, data.ItemID, data.BatchNumber, data.SerialNumber, data.ExpirationDate).Scan(&current_tracked_qty)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "unable to get the current qty of item from tracked inventory")
		}
	}

	if data.Action == "transfer_from_source" {
		// data.Change is already negative in this case hence used +
		current_qty += data.Change
	}
	if data.Action == "item_pickup" && data.CartID != nil && data.QtyToAddInCurrentQty != nil {
		current_qty += int(*data.QtyToAddInCurrentQty)
	}

	// Construct dynamic column if data exists
	var remark *map[string]interface{}
	if len(data.Remarks) > 0 {
		remarkData := make(map[string]interface{})
		for _, remark := range data.Remarks {
			remarkData[remark.Key] = remark.Value
		}
		remark = &remarkData
	}

	// Insert into transaction table
	query = `INSERT INTO transaction (client_id, warehouse_id, request_id, item_id, sku, action, change, current, changed_by_username, changed_by_id, changed_at, batch_number, serial_number, expiration_date, source, document, document_code, client_name, document_id, document_prn, remark, location_id, location_code, container_id, container_code, container_type, cart_id, cart_code, tote_id, tote_code, current_tracked_qty) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31)`
	commandTag, err := tx.Exec(ctx, query, data.ClientID, data.WarehouseID, data.RequestID, data.ItemID, data.SKU, data.Action, data.Change, current_qty, data.Username, data.UserID, data.ChangedAt.UTC(), data.BatchNumber, data.SerialNumber, data.ExpirationDate, data.Source, data.Document, documentCode, data.ClientName, documentID, documentPrn, remark, data.LocationID, data.LocationCode, data.ContainerID, data.ContainerCode, data.ContainerType, data.CartID, data.CartCode, data.ToteID, data.ToteCode, current_tracked_qty)
	if err != nil {
		tx.Rollback(ctx)
		return errors.Wrap(err, "unable to insert new row in transaction")
	}
	if commandTag.RowsAffected() != 1 {
		tx.Rollback(ctx)
		return errors.New("new row wasn't inserted in transaction")
	}

	return nil
}

func (o *OperationsImpl) UpdateShopifyInventory(itemID string) {

	var availableQty int32
	var clientID, sku string
	var isKit bool
	query := `SELECT client_id, sku, available_qty, is_kit FROM inventory WHERE item_id = $1 LIMIT 1`
	err := o.DB.QueryRow(context.TODO(), query, itemID).Scan(&clientID, &sku, &availableQty, &isKit)
	if err != nil {
		return
	}

	client_resp, err := o.App.GrpcClient.Core.Client.GetClient(context.TODO(), &core_proto.GetClientRequest{ClientId: clientID, Sku: sku})
	if err != nil {
		return
	}
	reqID := uuid.NewV4().String()
	//Logging response data for tracking
	reqData := bson.M{
		"client_id":     clientID,
		"client_name":   client_resp.GetClientName(),
		"sku":           sku,
		"available_qty": availableQty,
	}
	data, err := json.Marshal(reqData)
	if err != nil {
		return
	}
	o.App.Logger.Log().Str("request_id", reqID).Str("request_status", "initiating").Str("action", "update_shopify_inventory").Hex("request_data", data).Msg("Update Shopify Inventory [through inventory service] request data.")
	if client_resp.GetShopify() == nil || !client_resp.GetShopifyInventorySync() {
		return
	}
	if !client_resp.GetDynamicInventorySync() {
		return
	}
	if isKit && !client_resp.GetKitInventorySync() {
		return
	}
	req := &integrations_proto.UpdateShopifyInventoryRequest{
		Shop:        client_resp.GetShopify().GetShop(),
		AccessToken: client_resp.GetShopify().GetAccessToken(),
		LocationId:  client_resp.GetShopify().GetLocationId(),
		Quantity:    availableQty,
	}
	if client_resp.GetShopify().GetShopifySku() != "" {
		req.Sku = client_resp.GetShopify().GetShopifySku()
	} else {
		req.Sku = sku
	}
	updateShopify_resp, err := o.App.GrpcClient.Integrations.Client.UpdateShopifyInventory(context.TODO(), req)
	if err != nil || !updateShopify_resp.GetSuccess() {
		o.App.Logger.Log().Str("request_id", reqID).Str("request_status", "failed").Str("action", "update_shopify_inventory").Hex("request_data", data).Msg("Failed to Update Shopify Inventory [through inventory service] data.")
		return
	}
	if updateShopify_resp.GetSuccess() {
		o.App.Logger.Log().Str("request_id", reqID).Str("request_status", "success").Str("action", "update_shopify_inventory").Hex("response_data", data).Msg("Update Shopify Inventory [through inventory service] response data.")

		// Update last sync time in client collection on core service
		_, err1 := o.App.GrpcClient.Core.Client.UpdateLastShopifySyncTime(context.TODO(), &core_proto.UpdateLastShopifySyncTimeRequest{
			ClientId:             clientID,
			DynamicInventorySync: true,
		})
		if err1 != nil {
			sentry.CaptureException(err)
		}
	}
}
