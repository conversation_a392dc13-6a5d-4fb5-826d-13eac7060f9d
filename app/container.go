package app

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"inventory/model"
	"inventory/schema"
	"inventory/server/kafka"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/pkg/errors"
	"github.com/rs/zerolog"
	uuid "github.com/satori/go.uuid"
	segKafka "github.com/segmentio/kafka-go"
	"go.mongodb.org/mongo-driver/bson/primitive"

	core_proto "proto/core"
	inbound_proto "proto/inbound"
	outbound_proto "proto/outbound"
	reporting_proto "proto/reporting"
)

// Container defines methods of Container service to be implemented
type Container interface {
	DeleteContainerConsumer(kafka.Message)
	GetItemContainers(*schema.ValidateGetItemContainers) ([]schema.Container, error)
	CreateContainerType(*schema.ValidateCreateContainerType, string) (bool, error)
	GetContainerTypes(string) (*[]model.ContainerType, error)
	GetPreviousContainerCodeInt(string) (*int, error)
	// # Generate Container QR Codes
	GenerateContainerQRCodes(*schema.ValidateContainerQRCode, string, string) ([]string, error)
	GetContainer(uuid.UUID) (*schema.ContainerResponse, error)
	MoveContainerBtwnLocations(string, *string, *string, *uuid.UUID, *schema.SessionDetails) (bool, error)
	MoveItemsBtwnContainers(string, *uuid.UUID, *uuid.UUID, *string, []schema.ContainerItem, *schema.SessionDetails) (bool, error)

	MoveItems(*schema.ValidateMoveItems, string, string, *schema.SessionDetails) (bool, error)
	CheckReceivable(uuid.UUID, string, string, string, string, string) (map[string]any, error)
	GetReceivableContainers(string, string) ([]model.SearchResponse, error)
	GetBrutePickingSuggestion(string, string) ([]model.ContainerReportData, error)
	DeleteContainers(io.Reader, string, string, primitive.ObjectID, *model.UserModel) (bool, error)
	DeleteContainer(*schema.ValidateDeleteContainer, string) (bool, error)
	GetAllWarehouseContainers(string, string) (*model.ContainerResponse, error)
	GetContainerDetailsLayout(string, string, string) (*model.ContainerDetailsLayout, error)
}

// ContainerOpts contains arguments to be accepted for new instance of Container service
type ContainerOpts struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// ContainerImpl implements Container service
type ContainerImpl struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// InitContainer returns initializes Container service
func InitContainer(opts *ContainerOpts) Container {
	i := &ContainerImpl{
		App:    opts.App,
		DB:     opts.DB,
		Logger: opts.Logger,
	}
	return i
}

func (c *ContainerImpl) GetPreviousContainerCodeInt(whID string) (*int, error) {
	// LP-0001
	// Previous container code
	var prevCode string
	var prevCodeInt int
	query := `SELECT code FROM container_wise_inventory WHERE warehouse_id = $1 ORDER BY NULLIF(regexp_replace(code, '\D', '', 'g'), '')::int DESC`
	err := c.DB.QueryRow(context.TODO(), query, whID).Scan(&prevCode)
	if err != nil {
		if err == pgx.ErrNoRows {
			prevCodeInt = 1000
		} else {
			return nil, errors.Wrap(err, "failed to query for containers")
		}
	} else {
		if len(prevCode) != 0 {
			prevCodeInt, err = strconv.Atoi(prevCode[3:])
			if err != nil {
				return nil, errors.Wrap(err, "failed to convert licence plate number to int")
			}
		} else {
			prevCodeInt = 1000
		}
	}
	return &prevCodeInt, nil
}

// # Generate QR codes to identify containers
func (c *ContainerImpl) GenerateContainerQRCodes(data *schema.ValidateContainerQRCode, whID string, whName string) ([]string, error) {
	// # Create Context
	ctx := context.Background()

	// # Get container name by ID
	containerName, err := c.App.Operations.GetContainerNameByID(data.ContainerTypeID)
	if err != nil {
		return nil, errors.Wrap(err, "Invalid container ID")
	}

	// # Previous container code
	prevCodeInt, err := c.GetPreviousContainerCodeInt(whID)
	if err != nil {
		return nil, err
	}

	// # Create Containers
	now := time.Now().UTC()
	var container_ids []uuid.UUID
	var rows [][]any
	for i := 1; i <= data.Count; i++ {
		code := "LP-" + strconv.Itoa(*prevCodeInt+i)
		container_id := uuid.NewV4()
		rows = append(rows, []any{whID, code, container_id, 0, 0, data.ContainerTypeID, now})
		container_ids = append(container_ids, container_id)
	}

	// # Bulk insert rows
	copyCount, err1 := c.DB.CopyFrom(
		ctx,
		// # Table name
		pgx.Identifier{"container_wise_inventory"},
		// # Columns
		[]string{"warehouse_id", "code", "container_id", "quantity", "available_qty", "container_type_id", "created_at"},
		// # Rows to insert
		pgx.CopyFromRows(rows),
	)
	if err1 != nil {
		return nil, errors.Wrap(err1, "unable to bulk insert containers")
	}
	if copyCount != int64(len(rows)) {
		return nil, errors.New("failed to insert some rows in the database")
	}

	// # Fetch the containers - Sort the 'containers' by 'code' (LP Code) in 'ascending' order
	result_rows, err2 := c.DB.Query(ctx, `SELECT code, container_id FROM container_wise_inventory WHERE container_id = ANY($1) ORDER BY NULLIF(regexp_replace("code", '\D', '', 'g'), '')::int ASC`, container_ids)
	if err2 != nil {
		return nil, errors.Wrap(err2, "unable to fetch containers")
	}

	// # Close rows
	defer result_rows.Close()

	// # File Type
	fileType := data.FileType // # File Type: csv OR pdf

	// # Label Type
	labelType := data.LabelType // # Label Type: 1.25x3.5 OR 1.5x4 OR 4x6 (minimal labels)

	// # List to store QR codes
	var qrCodes []string

	// # QR Struct
	var QR schema.QRCode

	// # QR Data Struct
	Data := []schema.QRData{}

	// # Validate if the label type is one of the allowed values if the file type is pdf
	if (fileType == "pdf") && (labelType != "minimal_1.25x3.5") && (labelType != "minimal_1.5x4") && (labelType != "minimal_4x6") {
		err := errors.New("label_type must be one of [minimal_1.25x3.5 minimal_1.5x4 minimal_4x6]")
		return nil, err
	}

	// # Loop through resultant rows
	for result_rows.Next() {
		var code string
		var container_id string
		err := result_rows.Scan(&code, &container_id)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows to generate qr-codes")
		}

		// # Construct QR code string
		qrCode := "LP/" + container_id + "/" + code

		// # CSV - QR Code Strings | PDF - QR Code Images
		if fileType == "csv" {
			// # Append to QR codes list
			qrCodes = append(qrCodes, qrCode)
		} else {
			// # Generate QR code PNG image
			bufBytes, err := c.App.Utils.GenerateQrCode(qrCode)
			if err != nil {
				return nil, errors.Wrap(err, "Failed to generate QR code")
			}

			// # Encode image to base64 string
			imgBase64Str := base64.StdEncoding.EncodeToString(bufBytes)

			// # Construct QR
			qr := schema.QRData{
				QRcode: imgBase64Str,
				Code:   code,
			}

			// # Append to QR Data struct
			Data = append(Data, qr)
		}
	}

	// # Check if there was an error in reading rows
	row_err := result_rows.Err()
	if row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	if fileType == "pdf" {
		// # Construct Final QR Struct
		QR.Type = *containerName
		QR.WarehouseName = whName
		QR.Data = &Data

		// # Template File Variables
		var file, path string

		// # Check Label Type
		if labelType == "minimal_1.25x3.5" {
			// # Label Type: minimal_1.25x3.5
			// # Template File Name
			file = c.App.Config.PDFConfig.LPLabel1x3MinimalTemplateFileName
			// # Template File Path
			path = c.App.Config.PDFConfig.LPLabel1x3MinimalTemplateFilePath
		} else if labelType == "minimal_1.5x4" {
			// # Label Type: minimal_1.5x4
			// # Template File Name
			file = c.App.Config.PDFConfig.LPLabel1x4MinimalTemplateFileName
			// # Template File Path
			path = c.App.Config.PDFConfig.LPLabel1x4MinimalTemplateFilePath
		} else {
			// # Label Type: minimal_4x6
			// # Template File Name
			file = c.App.Config.PDFConfig.LPLabel4x6MinimalTemplateFileName
			// # Template File Path
			path = c.App.Config.PDFConfig.LPLabel4x6MinimalTemplateFilePath
		}

		// # Generate QR PDF
		buf, err := GenerateQrPDF(&QR, file, path, labelType)
		if err != nil {
			msg := "Failed to generate PDF"
			c.Logger.Err(err).Msg(msg)
			return nil, errors.Wrap(err, msg)
		}

		// # Base File Name
		baseName := "wms_lp_label.pdf"

		// # File Name Separator
		sep := "_"

		// # Construct file name for PDF file
		fileName := whName + sep + labelType + sep + baseName

		// # S3 Bucket Name
		bucket := c.App.Config.AWSConfig.TempBucket

		// # Upload PDF File to S3
		fileUrl, err := c.App.SSS.AddQRPDFToS3(fileName, bucket, buf.Bytes())
		if err != nil {
			err = errors.Wrap(err, "Failed to upload QR codes PDF to S3!")
			return nil, err
		}

		// # Return S3 URL of PDF file
		return []string{fileUrl}, nil
	}

	// # Return list of QR codes
	return qrCodes, nil
}

func (c *ContainerImpl) GetContainer(container_id uuid.UUID) (*schema.ContainerResponse, error) {
	var container model.Container

	// Query for container details
	query := `SELECT container_id, container_wise_inventory.warehouse_id, location_id, code, last_updated_at, created_at, is_shipped, ref_no, container_type.name FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id WHERE container_id = $1 LIMIT 1`
	err := c.DB.QueryRow(context.TODO(), query, container_id).Scan(
		&container.ContainerID,
		&container.WarehouseID,
		&container.LocationID,
		&container.Code,
		&container.LastUpdatedAt,
		&container.CreatedAt,
		&container.IsShipped,
		&container.RefNo,
		&container.ContainerTypeName,
	)
	if err == pgx.ErrNoRows {
		return nil, errors.Wrap(err, "container doesn't exist")
	}
	if err != nil {
		return nil, errors.Wrap(err, "unable to read row")
	}

	// Get location code from location id
	var locationCode string
	if container.LocationID != nil {
		query = `SELECT code FROM location_wise_inventory WHERE location_id = $1 LIMIT 1`
		err = c.DB.QueryRow(context.TODO(), query, container.LocationID).Scan(&locationCode)
		if err != nil {
			return nil, errors.Wrap(err, "unable to get location code")
		}
	}

	// Query for container items
	query = `SELECT container_wise_inventory.item_id, container_wise_inventory.quantity, container_wise_inventory.available_qty, container_wise_inventory.sku, container_wise_inventory.scannable, container_wise_inventory.batch_number, container_wise_inventory.serial_number, container_wise_inventory.expiration_date, inventory.base_unit, inventory.name, inventory.description, inventory.client_id, inventory.client_name FROM container_wise_inventory INNER JOIN inventory ON container_wise_inventory.item_id = inventory.item_id WHERE container_id = $1`
	rows, err := c.DB.Query(context.TODO(), query, container_id)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for containers")
	}
	defer rows.Close()

	// Container items
	var items []schema.Item

	for rows.Next() {
		var item schema.Item
		err := rows.Scan(
			&item.ItemID,
			&item.Quantity,
			&item.AvailableQty,
			&item.SKU,
			&item.Scannable,
			&item.BatchNumber,
			&item.SerialNumber,
			&item.ExpirationDate,
			&item.BaseUnit,
			&item.Name,
			&item.Description,
			&item.ClientID,
			&item.ClientName,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into item schema")
		}
		if item.ItemID != nil {
			items = append(items, item)
		}
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	// Response structure
	containerResponse := &schema.ContainerResponse{
		ContainerID:   container.ContainerID,
		Code:          container.Code,
		ContainerType: container.ContainerTypeName,
		WarehouseID:   container.WarehouseID,
		LocationID:    container.LocationID,
		LocationCode:  &locationCode,
		LastUpdatedAt: container.LastUpdatedAt,
		CreatedAt:     container.CreatedAt,
		IsShipped:     container.IsShipped,
		RefNo:         container.RefNo,
		Items:         items,
	}

	return containerResponse, nil
}

// move a container from source location to destination location
func (c *ContainerImpl) MoveContainerBtwnLocations(warehouseID string, sourceLocation, destinationLocation *string, containerID *uuid.UUID, sessionDetails *schema.SessionDetails) (bool, error) {
	ctx := context.Background()

	// Create transaction
	tx, err := c.DB.Begin(ctx)
	if err != nil {
		return false, errors.Wrap(err, "unable to begin transaction")
	}
	defer tx.Rollback(ctx)

	// Get source location
	var source_on_hold bool
	var sourceAreaID, sourceLocationCode string
	query := `SELECT code, on_hold, area_id FROM location_wise_inventory WHERE location_id = $1 LIMIT 1`
	err = tx.QueryRow(ctx, query, sourceLocation).Scan(&sourceLocationCode, &source_on_hold, &sourceAreaID)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to set details of source location")
	}
	if source_on_hold {
		tx.Rollback(ctx)
		return false, errors.New("source location is on hold")
	}

	// Get dest location
	var dest_on_hold bool
	var destAreaID, destLocationCode string
	query = `SELECT code, on_hold, area_id FROM location_wise_inventory WHERE location_id = $1 LIMIT 1`
	err = tx.QueryRow(ctx, query, destinationLocation).Scan(&destLocationCode, &dest_on_hold, &destAreaID)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to set details of destination location")
	}
	if dest_on_hold {
		tx.Rollback(ctx)
		return false, errors.New("destination location is on hold")
	}

	var getIsPickableRequest core_proto.CheckAreaTypeRequest
	getIsPickableRequest.AreaId = sourceAreaID
	getIsPickableRequest.Property = "picking"
	sourceArea, err := c.App.GrpcClient.Core.Client.CheckAreaType(ctx, &getIsPickableRequest)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to get property from core areas")
	}

	getIsPickableRequest.AreaId = destAreaID
	destArea, err := c.App.GrpcClient.Core.Client.CheckAreaType(ctx, &getIsPickableRequest)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to get property from core areas")
	}

	// Update the destination location in all the rows of the container in container_wise_inventory
	query = `UPDATE container_wise_inventory SET location_id = $1 WHERE container_id = $2`
	commandTag, err := tx.Exec(ctx, query, destinationLocation, containerID)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to update destination location in the container")
	}
	if !(commandTag.RowsAffected() > 0) {
		tx.Rollback(ctx)
		return false, errors.New("destination location_id wasn't updated on the container rows")
	}

	var containerItems []schema.ContainerItemObj
	// Get sku and quantity of all the container rows from container_wise_inventory
	query = `SELECT c.code, c.item_id, c.sku, c.batch_number, c.serial_number, c.expiration_date, c.quantity, c.available_qty, ct.name FROM container_wise_inventory c INNER JOIN container_type ct ON ct.id = c.container_type_id WHERE c.container_id = $1`
	rows, err := tx.Query(ctx, query, containerID)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "error querying all container rows")
	}
	for rows.Next() {
		var containerItem schema.ContainerItemObj
		if err := rows.Scan(
			&containerItem.Code,
			&containerItem.ItemID,
			&containerItem.SKU,
			&containerItem.BatchNumber,
			&containerItem.SerialNumber,
			&containerItem.ExpirationDate,
			&containerItem.Quantity,
			&containerItem.AvailableQty,
			&containerItem.ContainerType,
		); err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "error scanning container rows into variables")
		}

		if containerItem.ItemID != nil {
			// Item exists in the container
			containerItems = append(containerItems, containerItem)
		}
	}

	if err_rows := rows.Err(); err_rows != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err_rows, "error reading container rows")
	}

	for _, item := range containerItems {
		// Get item details from inventory
		inventory_item, err := c.App.Operations.GetItemFromInventory(*item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to get the item details from inventory "+*item.ItemID)
		}

		// Check if any row exists in location_wise_inventory that contains the item on the destination location
		var exists bool
		query := `SELECT EXISTS(SELECT 1 FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND location_id = $4 AND warehouse_id = $5 AND (expiration_date = $6 OR $6 IS NULL)) AS "exists"`
		err = tx.QueryRow(ctx, query, item.ItemID, item.BatchNumber, item.SerialNumber, destinationLocation, warehouseID, item.ExpirationDate).Scan(&exists)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to check if item exists on destination location")
		}

		if !exists {
			// Sku doesn't exist on the location, insert a new row
			err := c.App.Operations.InsertItemInLocation(tx, ctx, *destinationLocation, warehouseID, item.Quantity, item.AvailableQty, inventory_item, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
			if err != nil {
				tx.Rollback(ctx)
				return false, err
			}
		} else {
			// Item exists on destination location

			// Increment the quantity on location since a item is getting added to a container on that location
			query := `UPDATE location_wise_inventory SET quantity = quantity + $1, available_qty = available_qty + $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)`
			commandTag, err := tx.Exec(ctx, query, item.Quantity, item.ItemID, item.BatchNumber, item.SerialNumber, destinationLocation, warehouseID, item.ExpirationDate)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "error incrementing item quantity on destination location")
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return false, errors.New("item quantity wasn't incremented on the destination location")
			}
		}

		// Get the existing quantity of the item on source location
		var existing_qty int
		query = `SELECT quantity FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND location_id = $4 AND warehouse_id = $5 AND (expiration_date = $6 OR $6 IS NULL)`
		err = tx.QueryRow(ctx, query, item.ItemID, item.BatchNumber, item.SerialNumber, sourceLocation, warehouseID, item.ExpirationDate).Scan(&existing_qty)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "item does not exist on source location")
		}

		// Quantity of the item remaining on source location after it's moved
		remainingQty := existing_qty - item.Quantity

		if remainingQty < 0 {
			// Something is wrong, remaining qty is negative
			tx.Rollback(ctx)
			return false, errors.New("something is wrong - remaining quantity is negative")

		} else if remainingQty == 0 {
			// If source location is going to get empty after the item is moved, only one row should exist with item_id null and quantity = 0
			// If source location contains other items, then delete the row matched by item_id and location_id

			// remove row or set null in case of last row from location
			err := c.App.Operations.RemoveItemFromLocation(tx, ctx, *item.ItemID, *sourceLocation, warehouseID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
			if err != nil {
				tx.Rollback(ctx)
				return false, err
			}

		} else {
			// Source location contains abundant quantity

			// Decrement the quantity from source location
			query := `UPDATE location_wise_inventory SET quantity = quantity - $1, available_qty = available_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)`
			commandTag, err := tx.Exec(ctx, query, item.Quantity, item.ItemID, item.BatchNumber, item.SerialNumber, sourceLocation, warehouseID, item.ExpirationDate)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "unable to decrement the quantity from source location")
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return false, errors.New("quantity wasn't decremented on source location")
			}
		}

		pickable_qty, available_qty := 0, 0
		// Update inventory and tracked inventory for pickable and available quantity
		if !sourceArea.IsAreaPickable && destArea.IsAreaPickable {
			pickable_qty = item.Quantity // increment in inventory and tracked inventory
		} else if sourceArea.IsAreaPickable && !destArea.IsAreaPickable {
			pickable_qty = 0 - item.Quantity // decrement in inventory and tracked inventory
		}
		// Update inventory for available quantity
		if sourceArea.IsAreaHold && !destArea.IsAreaHold {
			available_qty = item.Quantity // increment in inventory and tracked inventory
		} else if !sourceArea.IsAreaHold && destArea.IsAreaHold {
			available_qty = 0 - item.Quantity // decrement in inventory and tracked inventory
		}
		query = `UPDATE inventory SET available_qty = available_qty + $1, pickable_qty = pickable_qty + $2 WHERE item_id = $3`
		_, err = tx.Exec(ctx, query, available_qty, pickable_qty, item.ItemID)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to increment pickable quantity in inventory")
		}

		if item.BatchNumber != nil || item.SerialNumber != nil || item.ExpirationDate != nil {
			// any tracked parameter exists
			query := `UPDATE tracked_inventory SET available_qty = available_qty + $1, pickable_qty = pickable_qty + $2 WHERE item_id = $3 AND (batch_number = $4 OR $4 IS NULL) AND (serial_number = $5 OR $5 IS NULL) AND (expiration_date = $6 OR $6 IS NULL) AND warehouse_id = $7`
			_, err := tx.Exec(ctx, query, available_qty, pickable_qty, item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate, warehouseID)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "unable to decrement pickable quantity in tracked inventory")
			}
		}
	}

	// Add transaction for each item
	for _, item := range containerItems {
		// Get client id from item
		var itemClientID, itemClientName string
		query = `SELECT client_id, client_name FROM inventory WHERE item_id = $1`
		err = tx.QueryRow(ctx, query, item.ItemID).Scan(&itemClientID, &itemClientName)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to get client id from item")
		}

		// Subtract quantity and save the source container and location
		sourceTransactionData := &model.TransactionData{
			ClientID:       itemClientID,
			WarehouseID:    warehouseID,
			RequestID:      sessionDetails.RequestID,
			ItemID:         *item.ItemID,
			SKU:            *item.SKU,
			Action:         "transfer_from_source",
			Change:         int(0 - item.Quantity),
			Username:       sessionDetails.Username,
			UserID:         sessionDetails.UserID,
			ChangedAt:      time.Now().UTC(),
			BatchNumber:    item.BatchNumber,
			SerialNumber:   item.SerialNumber,
			ExpirationDate: item.ExpirationDate,
			Source:         "self",
			Document:       "Transfer",
			ClientName:     itemClientName,
			LocationID:     *sourceLocation,
			LocationCode:   sourceLocationCode,
			ContainerID:    containerID,
			ContainerCode:  &item.Code,
			ContainerType:  item.ContainerType,
		}
		err = c.App.Operations.CreateTransaction(tx, sourceTransactionData)
		if err != nil {
			tx.Rollback(ctx)
			return false, err
		}

		// Increment quantity and save the destination container and location
		// Subtract quantity and save the source container and location
		destTransactionData := &model.TransactionData{
			ClientID:       itemClientID,
			WarehouseID:    warehouseID,
			RequestID:      sessionDetails.RequestID,
			ItemID:         *item.ItemID,
			SKU:            *item.SKU,
			Action:         "transfer_to_destination",
			Change:         item.Quantity,
			Username:       sessionDetails.Username,
			UserID:         sessionDetails.UserID,
			ChangedAt:      time.Now().UTC(),
			BatchNumber:    item.BatchNumber,
			SerialNumber:   item.SerialNumber,
			ExpirationDate: item.ExpirationDate,
			Source:         "self",
			Document:       "Transfer",
			ClientName:     itemClientName,
			LocationID:     *destinationLocation,
			LocationCode:   destLocationCode,
			ContainerID:    containerID,
			ContainerCode:  &item.Code,
			ContainerType:  item.ContainerType,
		}
		err = c.App.Operations.CreateTransaction(tx, destTransactionData)
		if err != nil {
			tx.Rollback(ctx)
			return false, err
		}

		go c.App.Operations.UpdateShopifyInventory(*item.ItemID)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "failed to commit transaction, rolling back")
	}

	return true, nil
}

func (c *ContainerImpl) MoveItemsBtwnContainers(warehouseID string, sourceContainer, destinationContainer *uuid.UUID, explicitDestinationLocation *string, items []schema.ContainerItem, sessionDetails *schema.SessionDetails) (bool, error) {
	if uuid.Equal(*sourceContainer, *destinationContainer) {
		return false, errors.New("Destination cannot be same as source")
	}

	var itemList []string
	for _, item := range items {
		itemList = append(itemList, item.ItemID)
	}

	// Create transaction
	ctx := context.Background()
	tx, err := c.DB.Begin(ctx)
	if err != nil {
		return false, errors.Wrap(err, "unable to begin transaction")
	}
	defer tx.Rollback(ctx)

	// Get client id and container type from source container
	var sourceContainerClient, sourceContainerType *string
	var receivedAt *time.Time
	query := `SELECT c.client_id, c.received_at, ct.name FROM container_wise_inventory c INNER JOIN container_type ct ON ct.id = c.container_type_id WHERE c.container_id = $1 AND c.warehouse_id = $2 LIMIT 1`
	err = tx.QueryRow(ctx, query, sourceContainer, warehouseID).Scan(&sourceContainerClient, &receivedAt, &sourceContainerType)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "failed to query for source container details")
	}

	// Get client id and container type from destination container
	var destContainerClient, destContainerType, destContainerCode *string

	query = `SELECT c.client_id, c.code, ct.name FROM container_wise_inventory c INNER JOIN container_type ct ON ct.id = c.container_type_id WHERE c.container_id = $1 AND c.warehouse_id = $2`
	err = tx.QueryRow(ctx, query, destinationContainer, warehouseID).Scan(&destContainerClient, &destContainerCode, &destContainerType)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "failed to query for dest container details")
	}

	// If both source container and dest container are loose then mixed client items are allowed. Else not allowed
	if !(*sourceContainerType == "loose" && *destContainerType == "loose") {
		// Check if all items belong to the same client
		var clientCount int
		query = `SELECT COUNT(DISTINCT client_id) FROM inventory WHERE item_id = ANY($1)`
		err = tx.QueryRow(ctx, query, itemList).Scan(&clientCount)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to get count of rows containing client id")
		}
		if clientCount > 1 {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "All items should belong to the same client")
		}

		// Get client id from any item
		var itemClientID string
		query = `SELECT client_id FROM inventory WHERE item_id = ANY($1) LIMIT 1`
		err = tx.QueryRow(ctx, query, itemList).Scan(&itemClientID)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to get client id")
		}

		if *destContainerType != "loose" {
			// Not loose
			if destContainerClient != nil {
				// Not empty
				if *destContainerClient != itemClientID {
					// Item and dest container clients should match
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "The items to be moved should have the same client as destination container/location")
				}
			}
		}
	}

	// Get source container location and code
	var source_container_location, source_container_code string
	query = `SELECT location_id, code FROM container_wise_inventory WHERE container_id = $1 AND warehouse_id = $2 LIMIT 1`
	err = tx.QueryRow(ctx, query, sourceContainer, warehouseID).Scan(&source_container_location, &source_container_code)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "failed to query for source container details")
	}

	// Get source container location code
	var source_container_location_code string
	query = `SELECT code FROM location_wise_inventory WHERE location_id = $1 AND warehouse_id = $2`
	err = tx.QueryRow(ctx, query, source_container_location, warehouseID).Scan(&source_container_location_code)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "failed to query for source container location details")
	}

	// Get destination container location and code
	var destination_container_location *string
	query = `SELECT location_id FROM container_wise_inventory WHERE container_id = $1 AND warehouse_id = $2 LIMIT 1`
	err = tx.QueryRow(ctx, query, destinationContainer, warehouseID).Scan(&destination_container_location)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "failed to query for destination container location")
	}
	if destination_container_location == nil {
		if explicitDestinationLocation == nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "Explicit destination location is required")
		}
		destination_container_location = explicitDestinationLocation
	}

	// Get destination container location code
	var destination_container_location_code string
	query = `SELECT code FROM location_wise_inventory WHERE location_id = $1 AND warehouse_id = $2`
	err = tx.QueryRow(ctx, query, destination_container_location, warehouseID).Scan(&destination_container_location_code)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "failed to query for destination container location details")
	}

	// Check if either locations ar on hold
	var source_on_hold bool
	var sourceAreaID string
	query = `SELECT on_hold, area_id FROM location_wise_inventory WHERE location_id = $1 LIMIT 1`
	err = tx.QueryRow(ctx, query, source_container_location).Scan(&source_on_hold, &sourceAreaID)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to set details of source location")
	}
	if source_on_hold {
		tx.Rollback(ctx)
		return false, errors.New("source location is on hold")
	}

	// Get dest location
	var dest_on_hold bool
	var destAreaID string
	query = `SELECT on_hold, area_id FROM location_wise_inventory WHERE location_id = $1 LIMIT 1`
	err = tx.QueryRow(ctx, query, destination_container_location).Scan(&dest_on_hold, &destAreaID)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to set details of destination location")
	}
	if dest_on_hold {
		tx.Rollback(ctx)
		return false, errors.New("destination location is on hold")
	}

	item_qty_map := make(map[string]int)

	if source_container_location != *destination_container_location {
		// move items from source location to destination location

		var getIsPickableRequest core_proto.CheckAreaTypeRequest
		getIsPickableRequest.AreaId = sourceAreaID
		getIsPickableRequest.Property = "picking"
		sourceArea, err := c.App.GrpcClient.Core.Client.CheckAreaType(ctx, &getIsPickableRequest)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to get property from core areas")
		}

		getIsPickableRequest.AreaId = destAreaID
		destArea, err := c.App.GrpcClient.Core.Client.CheckAreaType(ctx, &getIsPickableRequest)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to get property from core areas")
		}

		for _, item := range items {

			var existing_quantity int
			query := `SELECT quantity FROM container_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL) AND container_id = $5`
			err := tx.QueryRow(ctx, query, item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate, sourceContainer).Scan(&existing_quantity)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "failed to get quantity of item from source container")
			}

			if item.Quantity > existing_quantity {
				fmt.Println(item.Quantity)
				fmt.Println(existing_quantity)
				fmt.Println()
				tx.Rollback(ctx)
				return false, errors.New("Quantity of item to be moved cannot be greater than existing quantity on the source container")
			}
			item_qty_map[item.ItemID] = existing_quantity

			// Move item to the destination location

			// Get item details from inventory
			inventory_item, err := c.App.Operations.GetItemFromInventory(item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "unable to get the item details from inventory "+item.ItemID)
			}

			// Check if the item exists on the destination location
			var exists bool
			query = `SELECT EXISTS(SELECT 1 FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND location_id = $4 AND warehouse_id = $5 AND (expiration_date = $6 OR $6 IS NULL)) AS "exists"`
			err = tx.QueryRow(ctx, query, item.ItemID, item.BatchNumber, item.SerialNumber, destination_container_location, warehouseID, item.ExpirationDate).Scan(&exists)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "unable to check if item exists on destination location")
			}
			if exists {
				// Increment the quantity on location since a item is getting added to a container on that location
				query := `UPDATE location_wise_inventory SET quantity = quantity + $1, available_qty = available_qty + $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)`
				commandTag, err := tx.Exec(ctx, query, item.Quantity, item.ItemID, item.BatchNumber, item.SerialNumber, destination_container_location, warehouseID, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "error incrementing item quantity on destination location")
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return false, errors.New("item quantity wasn't incremented on the destination location")
				}
			} else {
				// Sku doesn't exist on the location, insert a new row
				err := c.App.Operations.InsertItemInLocation(tx, ctx, *destination_container_location, warehouseID, item.Quantity, item.Quantity, inventory_item, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, err
				}
			}

			// Remove or decrement item from the source container

			remaining_qty_on_container := existing_quantity - item.Quantity
			if remaining_qty_on_container == 0 {
				// remove item from container
				err := c.App.Operations.RemoveItemFromContainer(tx, ctx, *sourceContainer, item.ItemID, source_container_location, warehouseID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, err
				}
			} else {
				// Abundant quantity is present on source container
				// Decrement quantity from source container
				query := `UPDATE container_wise_inventory SET quantity = quantity - $1, available_qty = available_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND container_id = $5 AND location_id = $6 AND warehouse_id = $7 AND (expiration_date = $8 OR $8 IS NULL)`
				commandTag, err := tx.Exec(ctx, query, item.Quantity, item.ItemID, item.BatchNumber, item.SerialNumber, sourceContainer, source_container_location, warehouseID, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "error decrementing item quantity on source container")
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return false, errors.New("quantity wasn't decremented on the source container")
				}
			}

			// Get existing quantity on the source location
			var qty_on_location int
			query = `SELECT quantity FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND location_id = $4 AND warehouse_id = $5 AND (expiration_date = $6 OR $6 IS NULL)`
			err = tx.QueryRow(ctx, query, item.ItemID, item.BatchNumber, item.SerialNumber, source_container_location, warehouseID, item.ExpirationDate).Scan(&qty_on_location)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "couldn't get quantity of sku on source location")
			}

			// Remaining quantity on the source location after removal of the item in container
			remaining_qty_on_location := qty_on_location - item.Quantity
			if remaining_qty_on_location == 0 {
				// remove row or set null in case of last row from location
				err := c.App.Operations.RemoveItemFromLocation(tx, ctx, item.ItemID, source_container_location, warehouseID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, err
				}
			} else {
				// Decrement item quantity from location quantity
				query := `UPDATE location_wise_inventory SET quantity = quantity - $1, available_qty = available_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)`
				commandTag, err := tx.Exec(ctx, query, item.Quantity, item.ItemID, item.BatchNumber, item.SerialNumber, source_container_location, warehouseID, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "error decrementing item quantity on source location")
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return false, errors.New("quantity wasn't decremented on the source location")
				}
			}

			pickable_qty, available_qty := 0, 0
			// Update inventory and tracked inventory for pickable and available quantity
			if !sourceArea.IsAreaPickable && destArea.IsAreaPickable {
				pickable_qty = item.Quantity // increment in inventory and tracked inventory
			} else if sourceArea.IsAreaPickable && !destArea.IsAreaPickable {
				pickable_qty = 0 - item.Quantity // decrement in inventory and tracked inventory
			}
			// Update inventory for available quantity
			if sourceArea.IsAreaHold && !destArea.IsAreaHold {
				available_qty = item.Quantity // increment in inventory and tracked inventory
			} else if !sourceArea.IsAreaHold && destArea.IsAreaHold {
				available_qty = 0 - item.Quantity // decrement in inventory and tracked inventory
			}
			query = `UPDATE inventory SET available_qty = available_qty + $1, pickable_qty = pickable_qty + $2 WHERE item_id = $3`
			_, err = tx.Exec(ctx, query, available_qty, pickable_qty, item.ItemID)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "unable to increment pickable quantity in inventory")
			}

			if item.BatchNumber != nil || item.SerialNumber != nil || item.ExpirationDate != nil {
				// any tracked parameter exists
				query := `UPDATE tracked_inventory SET available_qty = available_qty + $1, pickable_qty = pickable_qty + $2 WHERE item_id = $3 AND (batch_number = $4 OR $4 IS NULL) AND (serial_number = $5 OR $5 IS NULL) AND (expiration_date = $6 OR $6 IS NULL) AND warehouse_id = $7`
				_, err := tx.Exec(ctx, query, available_qty, pickable_qty, item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate, warehouseID)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "unable to decrement pickable quantity in tracked inventory")
				}
			}
		}
	}

	// move items from source container to destination container

	for _, item := range items {
		var existing_quantity int

		if source_container_location == *destination_container_location {
			query := `SELECT quantity FROM container_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND container_id = $4 AND (expiration_date = $5 OR $5 IS NULL)`
			err := tx.QueryRow(ctx, query, item.ItemID, item.BatchNumber, item.SerialNumber, sourceContainer, item.ExpirationDate).Scan(&existing_quantity)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "failed to get quantity of item from source container")
			}
		} else {
			existing_quantity = item_qty_map[item.ItemID]
		}

		if item.Quantity > existing_quantity {
			return false, errors.New("quantity of item to be moved cannot be greater than existing quantity on the source container")
		}

		// Get item details from inventory
		inventory_item, err := c.App.Operations.GetItemFromInventory(item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to get the item details from inventory "+item.ItemID)
		}

		// Move to destination container

		// Check if the item exists on the destination container
		var exists bool
		query = `SELECT EXISTS(SELECT 1 FROM container_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND container_id = $4 AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)) AS "exists"`
		err = tx.QueryRow(ctx, query, item.ItemID, item.BatchNumber, item.SerialNumber, destinationContainer, destination_container_location, warehouseID, item.ExpirationDate).Scan(&exists)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to check if item exists on destination container")
		}
		if exists {
			// Increment the quantity on destination container since the item is getting added to it
			query := `UPDATE container_wise_inventory SET quantity = quantity + $1, available_qty = available_qty + $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND container_id = $5 AND location_id = $6 AND warehouse_id = $7 AND (expiration_date = $8 OR $8 IS NULL)`
			commandTag, err := tx.Exec(ctx, query, item.Quantity, item.ItemID, item.BatchNumber, item.SerialNumber, destinationContainer, destination_container_location, warehouseID, item.ExpirationDate)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "error incrementing item quantity on destination container")
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return false, errors.New("item quantity wasn't incremented on the destination container")
			}
		} else {
			// Sku doesn't exist on the destination container
			err := c.App.Operations.InsertItemInContainer(tx, ctx, *destinationContainer, *destination_container_location, warehouseID, item.Quantity, item.Quantity, inventory_item, item.BatchNumber, item.SerialNumber, item.ExpirationDate, receivedAt)
			if err != nil {
				tx.Rollback(ctx)
				return false, err
			}
		}

		// Remove or decrement from source container

		// This operation has already happened when source and destination locations were different
		if source_container_location == *destination_container_location {
			remaining_qty_on_container := existing_quantity - item.Quantity
			if remaining_qty_on_container == 0 {
				// remove item from container
				err := c.App.Operations.RemoveItemFromContainer(tx, ctx, *sourceContainer, item.ItemID, source_container_location, warehouseID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, err
				}
			} else {
				// Abundant quantity is present on source container
				// Decrement quantity from source container
				query := `UPDATE container_wise_inventory SET quantity = quantity - $1, available_qty = available_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND container_id = $5 AND location_id = $6 AND warehouse_id = $7 AND (expiration_date = $8 OR $8 IS NULL)`
				commandTag, err := tx.Exec(ctx, query, item.Quantity, item.ItemID, item.BatchNumber, item.SerialNumber, sourceContainer, source_container_location, warehouseID, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "error decrementing item quantity on source container")
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return false, errors.New("quantity wasn't decremented on the source container")
				}
			}
		}
	}

	// Add transaction for each item
	for _, item := range items {
		// Get client id from item
		var itemClientID, itemClientName string
		query = `SELECT client_id, client_name FROM inventory WHERE item_id = $1`
		err = tx.QueryRow(ctx, query, item.ItemID).Scan(&itemClientID, &itemClientName)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to get client id from item")
		}

		// Subtract quantity and save the source container and location
		sourceTransactionData := &model.TransactionData{
			ClientID:       itemClientID,
			WarehouseID:    warehouseID,
			RequestID:      sessionDetails.RequestID,
			ItemID:         item.ItemID,
			SKU:            item.SKU,
			Action:         "transfer_from_source",
			Change:         int(0 - item.Quantity),
			Username:       sessionDetails.Username,
			UserID:         sessionDetails.UserID,
			ChangedAt:      time.Now().UTC(),
			BatchNumber:    item.BatchNumber,
			SerialNumber:   item.SerialNumber,
			ExpirationDate: item.ExpirationDate,
			Source:         "self",
			Document:       "Transfer",
			ClientName:     itemClientName,
			LocationID:     source_container_location,
			LocationCode:   source_container_location_code,
			ContainerID:    sourceContainer,
			ContainerCode:  &source_container_code,
			ContainerType:  *sourceContainerType,
		}
		err = c.App.Operations.CreateTransaction(tx, sourceTransactionData)
		if err != nil {
			tx.Rollback(ctx)
			return false, err
		}

		// Increment quantity and save the destination container and location
		// Subtract quantity and save the source container and location
		destTransactionData := &model.TransactionData{
			ClientID:       itemClientID,
			WarehouseID:    warehouseID,
			RequestID:      sessionDetails.RequestID,
			ItemID:         item.ItemID,
			SKU:            item.SKU,
			Action:         "transfer_to_destination",
			Change:         item.Quantity,
			Username:       sessionDetails.Username,
			UserID:         sessionDetails.UserID,
			ChangedAt:      time.Now().UTC(),
			BatchNumber:    item.BatchNumber,
			SerialNumber:   item.SerialNumber,
			ExpirationDate: item.ExpirationDate,
			Source:         "self",
			Document:       "Transfer",
			ClientName:     itemClientName,
			LocationID:     *destination_container_location,
			LocationCode:   destination_container_location_code,
			ContainerID:    destinationContainer,
			ContainerCode:  destContainerCode,
			ContainerType:  *destContainerType,
		}
		err = c.App.Operations.CreateTransaction(tx, destTransactionData)
		if err != nil {
			tx.Rollback(ctx)
			return false, err
		}

		go c.App.Operations.UpdateShopifyInventory(item.ItemID)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "failed to commit transaction, rolling back")
	}
	return true, nil
}

func (c *ContainerImpl) ItemLocationToLocation(warehouseID string, sourceLocation, destinationLocation *string, items []schema.ContainerItem, sessionDetails *schema.SessionDetails) (bool, error) {
	// Get loose container id
	type_id, err := c.App.Operations.GetContainerTypeID("loose", warehouseID)
	if err != nil {
		return false, err
	}
	// Get loose container from source location
	var source_location_loose_container uuid.UUID
	query := `SELECT container_id FROM container_wise_inventory WHERE location_id = $1 AND container_type_id = $2 AND warehouse_id = $3 LIMIT 1`
	err = c.DB.QueryRow(context.TODO(), query, sourceLocation, type_id, warehouseID).Scan(&source_location_loose_container)
	if err != nil {
		return false, errors.Wrap(err, "failed to get loose container from source location")
	}

	// Get loose container from destination location
	var destn_location_loose_container uuid.UUID
	query = `SELECT container_id FROM container_wise_inventory WHERE location_id = $1 AND container_type_id = $2 AND warehouse_id = $3 LIMIT 1`
	err = c.DB.QueryRow(context.TODO(), query, destinationLocation, type_id, warehouseID).Scan(&destn_location_loose_container)
	if err != nil {
		if err == pgx.ErrNoRows {
			// Create an empty loose container on destination location

			// Previous container code
			prevCodeInt, err := c.App.Container.GetPreviousContainerCodeInt(warehouseID)
			if err != nil {
				return false, err
			}
			code := "LP-" + strconv.Itoa(*prevCodeInt+1)
			container_id := uuid.NewV4()
			destn_location_loose_container = container_id

			query = `INSERT INTO container_wise_inventory (item_id, sku, batch_number, serial_number, expiration_date, scannable, container_id, container_type_id, location_id, warehouse_id, code, quantity, available_qty, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)`
			commandTag, err := c.DB.Exec(context.TODO(), query, nil, nil, nil, nil, nil, nil, container_id, type_id, destinationLocation, warehouseID, code, 0, 0, time.Now())
			if err != nil {
				return false, errors.Wrap(err, "unable to insert new loose container")
			}
			if commandTag.RowsAffected() != 1 {
				return false, errors.New("new lose container wasn't inserted")
			}

		} else {
			return false, errors.Wrap(err, "failed to get loose container from destination location")
		}
	}

	_, err = c.MoveItemsBtwnContainers(warehouseID, &source_location_loose_container, &destn_location_loose_container, nil, items, sessionDetails)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (c *ContainerImpl) ItemLocationToContainer(warehouseID string, sourceLocation *string, destinationContainer *uuid.UUID, explicitDestinationLocation *string, items []schema.ContainerItem, sessionDetails *schema.SessionDetails) (bool, error) {
	// Get loose container id
	type_id, err := c.App.Operations.GetContainerTypeID("loose", warehouseID)
	if err != nil {
		return false, err
	}
	// Get loose container from source location
	var source_location_loose_container uuid.UUID
	query := `SELECT container_id FROM container_wise_inventory WHERE location_id = $1 AND container_type_id = $2 AND warehouse_id = $3 LIMIT 1`
	err = c.DB.QueryRow(context.TODO(), query, sourceLocation, type_id, warehouseID).Scan(&source_location_loose_container)
	if err != nil {
		return false, errors.Wrap(err, "failed to get loose container from source location")
	}

	_, err = c.MoveItemsBtwnContainers(warehouseID, &source_location_loose_container, destinationContainer, explicitDestinationLocation, items, sessionDetails)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (c *ContainerImpl) ItemContainerToLocation(warehouseID string, sourceContainer *uuid.UUID, destinationLocation *string, items []schema.ContainerItem, sessionDetails *schema.SessionDetails) (bool, error) {
	// Get loose container id
	type_id, err := c.App.Operations.GetContainerTypeID("loose", warehouseID)
	if err != nil {
		return false, err
	}
	// Get loose container from destination location
	var destn_location_loose_container uuid.UUID
	query := `SELECT container_id FROM container_wise_inventory WHERE location_id = $1 AND container_type_id = $2 AND warehouse_id = $3 LIMIT 1`
	err = c.DB.QueryRow(context.TODO(), query, destinationLocation, type_id, warehouseID).Scan(&destn_location_loose_container)
	if err != nil {
		if err == pgx.ErrNoRows {
			// Create an empty loose container on destination location

			// Previous container code
			prevCodeInt, err := c.App.Container.GetPreviousContainerCodeInt(warehouseID)
			if err != nil {
				return false, err
			}
			code := "LP-" + strconv.Itoa(*prevCodeInt+1)
			container_id := uuid.NewV4()
			destn_location_loose_container = container_id

			query = `INSERT INTO container_wise_inventory (item_id, sku, batch_number, serial_number, expiration_date, scannable, container_id, container_type_id, location_id, warehouse_id, code, quantity, available_qty, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)`
			commandTag, err := c.DB.Exec(context.TODO(), query, nil, nil, nil, nil, nil, nil, container_id, type_id, destinationLocation, warehouseID, code, 0, 0, time.Now())
			if err != nil {
				return false, errors.Wrap(err, "unable to insert new loose container")
			}
			if commandTag.RowsAffected() != 1 {
				return false, errors.New("new lose container wasn't inserted")
			}

		} else {
			return false, errors.Wrap(err, "failed to get loose container from destination location")
		}
	}

	_, err = c.MoveItemsBtwnContainers(warehouseID, sourceContainer, &destn_location_loose_container, nil, items, sessionDetails)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (c *ContainerImpl) MoveItems(data *schema.ValidateMoveItems, whID, operationType string, sessionDetails *schema.SessionDetails) (bool, error) {
	// Call specific operations based on request data

	switch operationType {

	case "location-to-location":
		if data.SourceLocation != nil && data.DestinationLocation != nil && data.Container == nil && data.SourceContainer == nil && data.DestinationContainer == nil && data.ExplicitDestinationLocation == nil {
			// Imaginary container transfer from one location to another
			_, err := c.ItemLocationToLocation(whID, data.SourceLocation, data.DestinationLocation, data.Items, sessionDetails)
			if err != nil {
				return false, err
			}
		} else {
			return false, errors.New("Only source and destination location is required for location-to-location transfer")
		}

	case "location-to-container":
		if data.SourceLocation != nil && data.DestinationContainer != nil && data.DestinationLocation == nil && data.Container == nil && data.SourceContainer == nil {
			// Imaginary container from source location to another real container
			_, err := c.ItemLocationToContainer(whID, data.SourceLocation, data.DestinationContainer, data.ExplicitDestinationLocation, data.Items, sessionDetails)
			if err != nil {
				return false, err
			}
		} else {
			return false, errors.New("Only source location, destination container and/or explicit destination location is required for location-to-container transfer")
		}

	case "container-to-location":
		if data.SourceContainer != nil && data.DestinationLocation != nil && data.DestinationContainer == nil && data.SourceLocation == nil && data.ExplicitDestinationLocation == nil && data.Container == nil {
			// Container to imaginary container on destination location
			_, err := c.ItemContainerToLocation(whID, data.SourceContainer, data.DestinationLocation, data.Items, sessionDetails)
			if err != nil {
				sentry.CaptureException(errors.Wrap(err, "Item transfer: failed to move items from container to location"))
				return false, err
			}
		} else {
			return false, errors.New("Only source container and destination location is required for container-to-location transfer")
		}

	case "container-to-container":
		if data.SourceContainer != nil && data.DestinationContainer != nil && data.SourceLocation == nil && data.DestinationLocation == nil && data.Container == nil {
			// Container to container transfer
			_, err := c.MoveItemsBtwnContainers(whID, data.SourceContainer, data.DestinationContainer, data.ExplicitDestinationLocation, data.Items, sessionDetails)
			if err != nil {
				return false, err
			}
		} else {
			return false, errors.New("Only source container, destination location and/or explicit destination location is required for container-to-container transfer")
		}

	case "container-transfer":
		if data.SourceLocation != nil && data.DestinationLocation != nil && data.Container != nil && data.SourceContainer == nil && data.DestinationContainer == nil && data.ExplicitDestinationLocation == nil {
			// Container transfer from location to location
			_, err := c.MoveContainerBtwnLocations(whID, data.SourceLocation, data.DestinationLocation, data.Container, sessionDetails)
			if err != nil {
				return false, err
			}
		} else {
			return false, errors.New("Only source location, destination location and container is required for container-transfer")
		}

	default:
		return false, errors.New("invalid operation")
	}

	// Update Ageing inventory
	var req reporting_proto.TransferUpdateRequest
	req.OperationType = operationType

	if data.SourceLocation != nil {
		req.SourceLocation = *data.SourceLocation
	}
	if data.DestinationLocation != nil {
		req.DestinationLocation = *data.DestinationLocation

		// Get destination location code
		var code string
		query := `SELECT code FROM location_wise_inventory WHERE location_id = $1`
		err := c.DB.QueryRow(context.TODO(), query, data.DestinationLocation).Scan(&code)
		if err != nil {
			sentry.CaptureException(errors.Wrap(err, "Item transfer: failed to get destination location code"))
		}
		req.DestinationLocationCode = code

	}
	if data.ExplicitDestinationLocation != nil {
		req.ExplicitDestinationLocation = *data.ExplicitDestinationLocation
		// Get destination location code
		var code string
		query := `SELECT code FROM location_wise_inventory WHERE location_id = $1`
		err := c.DB.QueryRow(context.TODO(), query, data.ExplicitDestinationLocation).Scan(&code)
		if err != nil {
			sentry.CaptureException(errors.Wrap(err, "Item transfer: failed to get destination location code"))
		}
		req.DestinationLocationCode = code
	}
	if data.SourceContainer != nil {
		req.SourceContainer = data.SourceContainer.String()
	}
	if data.DestinationContainer != nil {
		req.DestinationContainer = data.DestinationContainer.String()

		// Get destination location code
		var code string
		query := `SELECT code FROM container_wise_inventory WHERE container_id = $1`
		err := c.DB.QueryRow(context.TODO(), query, data.DestinationContainer).Scan(&code)
		if err != nil {
			sentry.CaptureException(errors.Wrap(err, "Item transfer: failed to get destination container code"))
		}
		req.DestinationContainerCode = code
	}
	if data.Container != nil {
		req.Container = data.Container.String()
	}
	if data.Items != nil {
		for _, item := range data.Items {
			appendItem := &reporting_proto.Item{
				SKU:      item.SKU,
				ItemID:   item.ItemID,
				Quantity: int32(item.Quantity),
			}
			if item.BatchNumber != nil {
				appendItem.BatchNumber = *item.BatchNumber
			}
			if item.SerialNumber != nil {
				appendItem.SerialNumber = *item.SerialNumber
			}
			if item.ExpirationDate != nil {
				appendItem.ExpirationDate = item.ExpirationDate.Format("2006-01-02")
			}
			req.Items = append(req.Items, appendItem)
		}
	}
	res, err := c.App.GrpcClient.Reporting.Client.TransferUpdate(context.TODO(), &req)
	if err != nil {
		sentry.CaptureException(errors.Wrap(err, "Item transfer: failed to update transfer details in age inventory"))
	}
	if !res.GetSuccess() {
		sentry.CaptureException(errors.Wrap(err, "Item transfer: failed to update transfer details in age inventory"))
	}

	return true, nil
}

func (c *ContainerImpl) CreateContainerType(data *schema.ValidateCreateContainerType, whID string) (bool, error) {
	var exists bool
	query := `SELECT EXISTS(SELECT 1 FROM container_type WHERE name = $1 AND warehouse_id = $2) AS "exists"`
	err := c.DB.QueryRow(context.TODO(), query, data.Name, whID).Scan(&exists)
	if err != nil {
		return false, errors.Wrap(err, "failed to check the existance of container type")
	}
	if exists {
		return false, errors.New("this container type exists for this warehouse")
	}

	// Create a row if not exists
	query = `INSERT INTO container_type (warehouse_id, name, only_ships_whole) VALUES ($1, $2, $3)`
	commandTag, err := c.DB.Exec(context.TODO(), query, whID, data.Name, data.ShipsWhole)
	if err != nil {
		return false, errors.Wrap(err, "failed to create container ")
	}
	if commandTag.RowsAffected() != 1 {
		return false, errors.Wrap(err, "Container type already exists for this warehouse")
	}
	return true, nil

}

func (c *ContainerImpl) GetContainerTypes(whID string) (*[]model.ContainerType, error) {
	var containerTypes []model.ContainerType

	query := `SELECT * FROM container_type WHERE warehouse_id = $1 AND name != $2`
	rows, err := c.DB.Query(context.TODO(), query, whID, "loose")
	if err != nil {
		return nil, errors.Wrap(err, "failed to get container types")
	}
	for rows.Next() {
		var containerType model.ContainerType
		err := rows.Scan(
			&containerType.ID,
			&containerType.WarehouseID,
			&containerType.Name,
			&containerType.OnlyShipsWhole,
		)
		if err != nil {
			return nil, errors.Wrap(err, "failed to scan container_type rows")
		}
		containerTypes = append(containerTypes, containerType)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read container_type rows")
	}

	return &containerTypes, nil
}

func (c *ContainerImpl) GetItemContainers(data *schema.ValidateGetItemContainers) ([]schema.Container, error) {
	err := c.App.Operations.CheckClientDeleted(data.ClientID)
	if err != nil {
		return nil, err
	}
	query := `SELECT container_id, location_id, code, container_type.name, quantity, available_qty, batch_number, serial_number, expiration_date FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL) AND location_id = $5 ORDER BY code ASC`
	rows, err := c.DB.Query(context.TODO(), query, data.ItemID, data.BatchNumber, data.SerialNumber, data.ExpirationDate, data.LocationID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for item containers")
	}
	defer rows.Close()

	// Container items
	var containers []schema.Container

	for rows.Next() {
		var container schema.Container
		err := rows.Scan(
			&container.ContainerID,
			&container.LocationID,
			&container.Code,
			&container.ContainerType,
			&container.Quantity,
			&container.AvailableQuantity,
			&container.BatchNumber,
			&container.SerialNumber,
			&container.ExpirationDate,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into item containers schema")
		}
		containers = append(containers, container)
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	return containers, nil
}

func (c *ContainerImpl) CheckReceivable(containerID uuid.UUID, locationID, clientID, warehouseID, resource, resourceID string) (map[string]any, error) {
	var containerLocationID, containerLocationCode, containerClientID *string
	// Query for container details
	query := `SELECT location_id, client_id FROM container_wise_inventory WHERE container_id = $1 AND warehouse_id = $2`
	err := c.DB.QueryRow(context.TODO(), query, containerID, warehouseID).Scan(&containerLocationID, &containerClientID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return map[string]any{"success": false, "container": nil}, errors.Wrap(err, "Container doesn't exist")
		}
		return map[string]any{"success": false, "container": nil}, errors.Wrap(err, "Unable to query container_wise_inventory")
	}

	if containerClientID != nil {
		if *containerClientID != clientID {
			return map[string]any{"success": false, "container": nil}, errors.New("This container is used by another client")
		}
	}

	// Get container details
	var container model.Container

	// Query for container details
	query = `SELECT container_id, container_wise_inventory.warehouse_id, client_id, client_name, location_id, code, last_updated_at, created_at, is_shipped, container_type.name FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id WHERE container_id = $1 AND container_wise_inventory.warehouse_id = $2 LIMIT 1`
	err = c.DB.QueryRow(context.TODO(), query, containerID, warehouseID).Scan(
		&container.ContainerID,
		&container.WarehouseID,
		&container.ClientID,
		&container.ClientName,
		&container.LocationID,
		&container.Code,
		&container.LastUpdatedAt,
		&container.CreatedAt,
		&container.IsShipped,
		&container.ContainerTypeName,
	)
	if err == pgx.ErrNoRows {
		return map[string]any{"success": false, "container": nil}, errors.Wrap(err, "container doesn't exist")
	}
	if err != nil {
		return map[string]any{"success": false, "container": nil}, errors.Wrap(err, "unable to read row")
	}

	if containerLocationID == nil {
		// Check if location is mismatched in scanned arns
		inbReq := &inbound_proto.CheckArnScannedContainerRequest{
			ContainerId: containerID.String(),
		}
		if resource == "arn" && resourceID != "" {
			inbReq.ArnId = resourceID
		}
		inbRes, err := c.App.GrpcClient.Inbound.Client.CheckArnScannedContainer(context.TODO(), inbReq)
		if err != nil {
			return map[string]any{"success": false, "container": nil}, err
		}

		// Check if location is mismatched in scanned rmas
		outReq := &outbound_proto.CheckRMAScannedContainerRequest{
			ContainerId: containerID.String(),
		}
		if resource == "rma" && resourceID != "" {
			outReq.RmaId = resourceID
		}
		outRes, err := c.App.GrpcClient.Outbound.Client.CheckRMAScannedContainer(context.TODO(), outReq)
		if err != nil {
			return map[string]any{"success": false, "container": nil}, err
		}
		fmt.Println("inbRes :", inbRes)
		fmt.Println("outRes :", outRes)
		if inbRes.GetSuccess() && outRes.GetSuccess() {
			return map[string]any{"success": true, "container": container}, nil
		}

		// Do not allow arns of different clients
		if inbRes.GetClientId() != clientID && !inbRes.GetSuccess() {
			return map[string]any{"success": false, "container": nil}, errors.New("Items of different clients cannot be received on the same container.")
		}

		// Do not allow rmas of different clients
		if outRes.GetClientId() != clientID && !outRes.GetSuccess() {
			return map[string]any{"success": false, "container": nil}, errors.New("Items of different clients cannot be received on the same container")
		}

		arnScannedContainerID := uuid.FromStringOrNil(inbRes.GetContainerId())
		if uuid.Equal(arnScannedContainerID, containerID) && inbRes.GetLocationId() != locationID {
			return map[string]any{"success": false, "container": nil}, errors.New("There is already a scanned open arn " + inbRes.GetArnCode() + " where the container " + inbRes.GetContainerCode() + " is associated with the location " + inbRes.GetLocationCode())
		}

		rmaScannedContainerID := uuid.FromStringOrNil(outRes.GetContainerId())
		if uuid.Equal(rmaScannedContainerID, containerID) && outRes.GetLocationId() != locationID {
			return map[string]any{"success": false, "container": nil}, errors.New("There is already a scanned open RMA " + outRes.GetRmaCode() + " where the container " + outRes.GetContainerCode() + " is associated with the location " + outRes.GetLocationCode())
		}

		return map[string]any{"success": true, "container": container}, nil
	}

	if *containerLocationID != locationID {
		// Query for location details
		query := `SELECT code FROM location_wise_inventory WHERE location_id = $1`
		err := c.DB.QueryRow(context.TODO(), query, containerLocationID).Scan(&containerLocationCode)
		if err != nil {
			if err == pgx.ErrNoRows {
				return map[string]any{"success": false, "container": nil}, errors.Wrap(err, "Container's location doesn't exist")
			}
			return map[string]any{"success": false, "container": nil}, errors.Wrap(err, "Unable to query location_wise_inventory")
		}
		return map[string]any{"success": false, "container": nil}, errors.New("This container is placed on another location - " + *containerLocationCode)
	}

	// Check if location is mismatched in scanned arns
	inbReq := &inbound_proto.CheckArnScannedContainerRequest{
		ContainerId: containerID.String(),
	}
	if resource == "arn" && resourceID != "" {
		inbReq.ArnId = resourceID
	}
	inbRes, err := c.App.GrpcClient.Inbound.Client.CheckArnScannedContainer(context.TODO(), inbReq)
	if err != nil {
		return map[string]any{"success": false, "container": nil}, err
	}

	// Check if location is mismatched in scanned rmas
	outReq := &outbound_proto.CheckRMAScannedContainerRequest{
		ContainerId: containerID.String(),
	}
	if resource == "rma" && resourceID != "" {
		outReq.RmaId = resourceID
	}
	outRes, err := c.App.GrpcClient.Outbound.Client.CheckRMAScannedContainer(context.TODO(), outReq)
	if err != nil {
		return map[string]any{"success": false, "container": nil}, err
	}
	fmt.Println("inbRes1 :", inbRes)
	fmt.Println("outRes1 :", outRes)

	if inbRes.GetSuccess() && outRes.GetSuccess() {
		return map[string]any{"success": true, "container": container}, nil
	}

	// Do not allow arns of different clients
	if inbRes.GetClientId() != clientID && !inbRes.GetSuccess() {
		return map[string]any{"success": false, "container": nil}, errors.New("Items of different clients cannot be received on the same container.")
	}

	// Do not allow rmas of different clients
	if outRes.GetClientId() != clientID && !outRes.GetSuccess() {
		return map[string]any{"success": false, "container": nil}, errors.New("Items of different clients cannot be received on the same container")
	}

	arnScannedContainerID := uuid.FromStringOrNil(inbRes.GetContainerId())
	if uuid.Equal(arnScannedContainerID, containerID) && inbRes.GetLocationId() != locationID {
		return map[string]any{"success": false, "container": nil}, errors.New("There is already a scanned open arn " + inbRes.GetArnCode() + " where the container " + inbRes.GetContainerCode() + " is associated with the location " + inbRes.GetLocationCode())
	}

	rmaScannedContainerID := uuid.FromStringOrNil(outRes.GetContainerId())
	if uuid.Equal(rmaScannedContainerID, containerID) && outRes.GetLocationId() != locationID {
		return map[string]any{"success": false, "container": nil}, errors.New("There is already a scanned open RMA " + outRes.GetRmaCode() + " where the container " + outRes.GetContainerCode() + " is associated with the location " + outRes.GetLocationCode())
	}

	return map[string]any{"success": true, "container": container}, nil
}

func createSearchQuery(searchQuery string, warehouseID string) (*bytes.Buffer, error) {
	var buf bytes.Buffer

	filter := []any{
		map[string]any{
			"term": map[string]any{
				"warehouse_id": warehouseID,
			},
		},
	}

	should := []any{
		map[string]any{
			"regexp": map[string]any{
				"container_type": ".*" + searchQuery + ".*",
			},
		},
	}

	queries := strings.Split(searchQuery, "-")
	for _, query := range queries {
		should = append(should, map[string]any{
			"regexp": map[string]any{
				"code": ".*" + query + ".*",
			},
		})
	}

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"should":               should,
				"filter":               filter,
				"minimum_should_match": 1,
			},
		},
	}
	if err := json.NewEncoder(&buf).Encode(query); err != nil {
		return nil, errors.Wrap(err, "failed to query elasticsearch")
	}
	return &buf, nil
}

func (c *ContainerImpl) GetReceivableContainers(searchQuery, warehouseID string) ([]model.SearchResponse, error) {
	response := []model.SearchResponse{}
	searchQuery = "%" + searchQuery + "%"

	query := `SELECT DISTINCT c.code, c.container_id, ct.name, c.container_type_id, c.warehouse_id FROM container_wise_inventory c INNER JOIN container_type ct ON c.container_type_id = ct.id  WHERE (c.code ILIKE $1 OR ct.name ILIKE $1) AND c.warehouse_id = $2`
	rows, err := c.DB.Query(context.TODO(), query, searchQuery, warehouseID)
	if err == pgx.ErrNoRows {
		return response, nil
	}
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for container wise Inventory")
	}

	counter := 0

	for rows.Next() {
		var searchResponseSource model.SearchResponseSource
		err := rows.Scan(
			&searchResponseSource.Code,
			&searchResponseSource.ContainerID,
			&searchResponseSource.ContainerType,
			&searchResponseSource.ContainerTypeID,
			&searchResponseSource.WarehouseID,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into search response schema")
		}
		searchResponse := model.SearchResponse{
			ID:     strconv.Itoa(counter),
			Index:  "container",
			Score:  1,
			Source: searchResponseSource,
		}
		response = append(response, searchResponse)
		counter++
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	return response, nil
}

func diffSlice(origin, target []string) []string {
	m := make(map[string]struct{}, len(target))
	for _, v := range target {
		m[v] = struct{}{}
	}

	var result []string
	for _, v := range origin {
		if _, ok := m[v]; !ok {
			result = append(result, v)
		}
	}

	return result
}

func (c *ContainerImpl) GetBrutePickingSuggestion(itemID, whID string) ([]model.ContainerReportData, error) {
	var containers []model.ContainerReportData

	// Query for container details
	query := `SELECT 
					c.client_name,
					c.container_id,
					c.code AS container_code,
					ct.name AS container_type,
					c.ref_no,
					l.code AS location_code,
					c.item_id,
					c.sku,
					c.batch_number,
					c.serial_number,
					c.expiration_date,
					c.quantity,
					c.location_id,
					c.received_at,
					c.first_used_at,
					c.created_at,
			 CASE 
					WHEN c.expiration_date IS NOT NULL THEN 'FEFO'
					ELSE 'FIFO'
			 END AS strategy
		     FROM container_wise_inventory c
			 INNER JOIN (
				SELECT DISTINCT ON (location_id) *
				FROM location_wise_inventory
				WHERE available_qty > 0
				AND item_id = $2
				AND on_hold = false	
				AND (expiration_date IS NULL OR expiration_date > NOW())
				ORDER BY location_id, expiration_date ASC NULLS LAST
			 ) AS l ON l.location_id = c.location_id
			 INNER JOIN container_type ct ON ct.id = c.container_type_id
		 	 WHERE c.quantity > 0
			 AND c.warehouse_id = $1
		 	 AND c.item_id = $2
			 AND (c.expiration_date IS NULL OR c.expiration_date > NOW())
			 ORDER BY 
				CASE WHEN c.expiration_date IS NOT NULL THEN 0 ELSE 1 END,
				c.expiration_date ASC NULLS LAST,
				COALESCE(c.received_at, c.first_used_at, c.created_at) ASC;
			`

	rows, err := c.DB.Query(context.TODO(), query, whID, itemID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to query for suggested expiry location")
	}
	defer rows.Close()

	for rows.Next() {
		var container model.ContainerReportData
		err := rows.Scan(
			&container.ClientName,
			&container.ContainerID,
			&container.ContainerCode,
			&container.ContainerType,
			&container.RefNo,
			&container.LocationCode,
			&container.ItemID,
			&container.Sku,
			&container.BatchNumber,
			&container.SerialNumber,
			&container.ExpirationDate,
			&container.Quantity,
			&container.LocationID,
			&container.ReceivedAt,
			&container.FirstUsedAt,
			&container.CreatedAt,
			&container.Strategy, // now comes directly from SQL
		)
		if err != nil {
			return nil, errors.Wrap(err, "Unable to read resultant rows into container schema")
		}
		containers = append(containers, container)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "Unable to read rows")
	}
	return containers, nil
}

func (c *ContainerImpl) DeleteContainers(file io.Reader, containerField, requestID string, warehouseID primitive.ObjectID, userData *model.UserModel) (bool, error) {
	// Read the file
	lines, err := csv.NewReader(file).ReadAll()
	if err != nil {
		return false, errors.Wrap(err, "Failed to read file!")
	}
	data := make([]string, 0)
	for i, line := range lines {
		if i == 0 {
			continue
		}
		data = append(data, line[0])
	}

	// elementsToProcess := int32(len(data))
	// zero := int32(0)

	// // Create a background task
	// now := time.Now().UTC()
	// bgTask := model.BackgroundTask{
	// 	RequestID:   requestID,
	// 	WarehouseID: &warehouseID,
	// 	Action:      "delete_containers",
	// 	Title:       "Delete Containers background process",
	// 	Status:      "open",
	// 	CreatedAt:   &now,
	// 	ProcessLog: &model.TaskProcessLog{
	// 		ElementType:       "containers",
	// 		ElementsToProcess: &elementsToProcess,
	// 		ProcessedElements: &zero,
	// 	},
	// }
	// res, err := c.DB.Collection(model.BackgroundTaskColl).InsertOne(context.TODO(), bgTask)
	// if err != nil {
	// 	return nil, errors.Wrap(err, "failed to add a background task")
	// }
	// ID := res.InsertedID.(primitive.ObjectID)
	// bgTask.ID = &ID

	chunkSize := 200
	for i := 0; i < len(data); i += chunkSize {
		end := i + chunkSize

		// necessary check to avoid slicing beyond slice capacity
		if end > len(data) {
			end = len(data)
		}
		containersData := data[i:end]

		// Fetch all containers
		var query string
		if containerField == "lp-id" {
			fmt.Println("lp-id")
			query = `SELECT item_id, container_id, container_type_id, warehouse_id, location_id, quantity, batch_number, serial_number, expiration_date, code, ref_no FROM container_wise_inventory WHERE container_id = ANY($1) AND warehouse_id = $2`

		} else if containerField == "lp-code" {
			fmt.Println("lp-code")
			query = `SELECT item_id, container_id, container_type_id, warehouse_id, location_id, quantity, batch_number, serial_number, expiration_date, code, ref_no FROM container_wise_inventory WHERE code = ANY($1) AND warehouse_id = $2`

		} else if containerField == "lp-ref-no" {
			fmt.Println("lp-ref-no")
			query = `SELECT item_id, container_id, container_type_id, warehouse_id, location_id, quantity, batch_number, serial_number, expiration_date, code, ref_no FROM container_wise_inventory WHERE ref_no = ANY($1) AND warehouse_id = $2`

		} else {
			return false, errors.New("Invalid container field")
		}
		rows, err := c.DB.Query(context.TODO(), query, containersData, warehouseID.Hex())
		if err != nil {
			return false, errors.Wrap(err, "Failed to query containers")
		}
		defer rows.Close()
		var containers []model.Container
		for rows.Next() {
			var container model.Container
			if err := rows.Scan(
				&container.ItemID,
				&container.ContainerID,
				&container.ContainerTypeID,
				&container.WarehouseID,
				&container.LocationID,
				&container.Quantity,
				&container.BatchNumber,
				&container.SerialNumber,
				&container.ExpirationDate,
				&container.Code,
				&container.RefNo,
			); err != nil {
				return false, errors.Wrap(err, "Failed to read row")
			}
			containers = append(containers, container)
		}
		if row_err := rows.Err(); row_err != nil {
			return false, errors.Wrap(row_err, "Failed to read rows")
		}
		if len(containers) == 0 {
			return false, errors.New("No containers found for the given data")
		}

		// Encoding into json
		jsonString, err := json.Marshal(containers)
		if err != nil {
			return false, errors.Wrap(err, "Json encoding failed!")
		}

		// Making kafka structure
		str := uuid.NewV1().String()
		// If this is the last iteration, add a flag to the key
		if end == len(data) {
			str = str + "-last"
		}
		m := segKafka.Message{
			Key:   []byte(str),
			Value: jsonString,
		}
		// Producing kafka messages
		c.App.DeleteContainerProducer.Publish(m)
	}

	return true, nil
}

func (c *ContainerImpl) DeleteContainerConsumer(m kafka.Message) {
	fmt.Println("Delete Container Consumer Called")

	var containers []model.Container
	// receiving kafka msgs and decoding into structure
	message := m.(segKafka.Message)
	err := json.Unmarshal(message.Value, &containers)
	if err != nil {
		c.Logger.Err(err).Msg("Delete Container Consumer")
		sentry.CaptureException(err)
	}

	ctx := context.Background()
	// Create transaction
	tx, err := c.DB.Begin(ctx)
	if err != nil {
		return
	}
	defer tx.Rollback(ctx)

	itemQtyMap := make(map[string]int32)
	containerIDs := make([]uuid.UUID, 0)
	// Delete containers
	for _, container := range containers {
		containerIDs = append(containerIDs, *container.ContainerID)

		if container.ItemID != nil {
			// Update item quantity map for inventory update
			if _, ok := itemQtyMap[*container.ItemID]; !ok {
				itemQtyMap[*container.ItemID] = *container.Quantity
			} else {
				itemQtyMap[*container.ItemID] += *container.Quantity
			}

			// update location_wise_inventory table for item
			// Get existing quantity on the source location
			var existing_qty_on_location, existing_available_qty_on_location int
			query := `SELECT quantity, available_qty FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL) AND location_id = $5`
			err = tx.QueryRow(ctx, query, container.ItemID, container.BatchNumber, container.SerialNumber, container.ExpirationDate, container.LocationID).Scan(&existing_qty_on_location, &existing_available_qty_on_location)
			if err != nil {
				e := errors.Wrap(err, "Couldn't get quantity of item "+*container.ItemID+" on location "+*container.LocationID)
				sentry.CaptureException(e)
				tx.Rollback(ctx)
				return
			}
			if existing_available_qty_on_location-int(*container.Quantity) < 0 {
				e := errors.New("Quantity on location might be allocated in pick-path for item " + *container.ItemID)
				sentry.CaptureException(e)
				tx.Rollback(ctx)
				return

			} else if existing_qty_on_location-int(*container.Quantity) == 0 {
				// remove row or set null in case of last row from location
				err := c.App.Operations.RemoveItemFromLocation(tx, ctx, *container.ItemID, *container.LocationID, *container.WarehouseID, container.BatchNumber, container.SerialNumber, container.ExpirationDate)
				if err != nil {
					sentry.CaptureException(err)
					tx.Rollback(ctx)
					return
				}
			} else {
				// Decrement quantity
				query := `UPDATE location_wise_inventory SET quantity = quantity - $1, available_qty = available_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL) AND location_id = $6`
				commandTag, err := tx.Exec(ctx, query, *container.Quantity, container.ItemID, container.BatchNumber, container.SerialNumber, container.ExpirationDate, container.LocationID)
				if err != nil {
					e := errors.Wrap(err, "Error decrementing item "+*container.ItemID+" quantity on location "+*container.LocationID)
					sentry.CaptureException(e)
					tx.Rollback(ctx)
					return
				}
				if commandTag.RowsAffected() != 1 {
					e := errors.New("Quantity wasn't decremented on the location " + *container.LocationID + " for item" + *container.ItemID)
					sentry.CaptureException(e)
					tx.Rollback(ctx)
					return
				}
			}

			// Update tracked_inventory table
			if container.BatchNumber != nil || container.SerialNumber != nil || container.ExpirationDate != nil {
				//fetch tracked inventory
				var tracked_qty, available_qty, pickable_qty int
				query := `SELECT quantity, available_qty, pickable_qty FROM tracked_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL)`
				err := tx.QueryRow(ctx, query, container.ItemID, container.BatchNumber, container.SerialNumber, container.ExpirationDate).Scan(&tracked_qty, &available_qty, &pickable_qty)
				if err != nil {
					e := errors.Wrap(err, "Unable to get tracked inventory for item "+*container.ItemID)
					sentry.CaptureException(e)
					tx.Rollback(ctx)
					return
				}
				if tracked_qty-int(*container.Quantity) < 0 || available_qty-int(*container.Quantity) < 0 {
					e := errors.New("Item Descrepency found in tracked inventory for item " + *container.ItemID)
					sentry.CaptureException(e)
					tx.Rollback(ctx)
					return
				}
				if tracked_qty-int(*container.Quantity) == 0 {
					//delete row
					query := `DELETE FROM tracked_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL)`
					_, err := tx.Exec(ctx, query, container.ItemID, container.BatchNumber, container.SerialNumber, container.ExpirationDate)
					if err != nil {
						e := errors.Wrap(err, "Unable to delete tracked inventory for item "+*container.ItemID)
						sentry.CaptureException(e)
						tx.Rollback(ctx)
						return
					}
				} else {
					// any tracked parameter exists
					query := `UPDATE tracked_inventory SET quantity = quantity - $1, available_qty = available_qty - $1, pickable_qty = pickable_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL)` // [TAP] - Pickable quantity is decremented at picking
					commandTag, err := tx.Exec(ctx, query, container.Quantity, container.ItemID, container.BatchNumber, container.SerialNumber, container.ExpirationDate)
					if err != nil {
						e := errors.Wrap(err, "Unable to update tracked inventory for item "+*container.ItemID)
						sentry.CaptureException(e)
						tx.Rollback(ctx)
						return
					}
					if commandTag.RowsAffected() != 1 {
						e := errors.New("Tracked inventory wasn't updated for item " + *container.ItemID)
						sentry.CaptureException(e)
						tx.Rollback(ctx)
						return
					}
				}
			}
		}
	}

	// Update available_qty in inventory
	for itemID, quantity := range itemQtyMap {
		query := `UPDATE inventory SET quantity = quantity - $1, pickable_qty = pickable_qty - $1, available_qty = available_qty - $1 WHERE item_id = $2`
		commandTag, err := tx.Exec(ctx, query, quantity, itemID)
		if err != nil {
			e := errors.Wrap(err, "Unable to update inventory for item "+itemID)
			sentry.CaptureException(e)
			tx.Rollback(ctx)
			return
		}
		if commandTag.RowsAffected() != 1 {
			e := errors.New("Inventory wasn't updated for item " + itemID)
			sentry.CaptureException(e)
			tx.Rollback(ctx)
			return
		}
	}

	// Delete from container_wise_inventory
	query := `DELETE FROM container_wise_inventory WHERE container_id = ANY($1)`
	if _, err := tx.Exec(ctx, query, containerIDs); err != nil {
		sentry.CaptureException(err)
		tx.Rollback(ctx)
		return
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return
	}
}

func (c *ContainerImpl) DeleteContainer(v *schema.ValidateDeleteContainer, whID string) (bool, error) {

	ctx := context.Background()
	// Create transaction
	tx, err := c.DB.Begin(ctx)
	if err != nil {
		return false, errors.Wrap(err, "Failed to create transaction")
	}
	defer tx.Rollback(ctx)

	// Fetch rows of the container
	var containers []model.Container
	query := `SELECT item_id, container_id, container_type_id, warehouse_id, location_id, sku, quantity, batch_number, serial_number, expiration_date, code, ref_no FROM container_wise_inventory WHERE container_id = $1 AND warehouse_id = $2`
	rows, err := tx.Query(ctx, query, v.ContainerID, whID)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "Failed to query for container")
	}
	defer rows.Close()
	for rows.Next() {
		var c model.Container
		if err := rows.Scan(
			&c.ItemID,
			&c.ContainerID,
			&c.ContainerTypeID,
			&c.WarehouseID,
			&c.LocationID,
			&c.SKU,
			&c.Quantity,
			&c.BatchNumber,
			&c.SerialNumber,
			&c.ExpirationDate,
			&c.Code,
			&c.RefNo,
		); err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "Failed to read row")
		}
		containers = append(containers, c)
	}
	if row_err := rows.Err(); row_err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(row_err, "Failed to read rows")
	}
	if len(containers) == 0 {
		tx.Rollback(ctx)
		return false, errors.New("No container found for the given data")
	}

	itemQtyMap := make(map[string]int32)
	itemSKUMap := make(map[string]string)
	// Delete containers
	for _, container := range containers {
		if container.ItemID != nil {
			// Update item quantity map for inventory update
			if _, ok := itemQtyMap[*container.ItemID]; !ok {
				itemQtyMap[*container.ItemID] = *container.Quantity
			} else {
				itemQtyMap[*container.ItemID] += *container.Quantity
			}

			// Update item sku map for inventory update
			if _, ok := itemSKUMap[*container.ItemID]; !ok {
				itemSKUMap[*container.ItemID] = *container.SKU
			} else {
				itemSKUMap[*container.ItemID] = *container.SKU
			}

			// update location_wise_inventory table for item
			// Get existing quantity on the source location
			var existing_qty_on_location, existing_available_qty_on_location int
			query := `SELECT quantity, available_qty FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL) AND location_id = $5`
			err = tx.QueryRow(ctx, query, container.ItemID, container.BatchNumber, container.SerialNumber, container.ExpirationDate, container.LocationID).Scan(&existing_qty_on_location, &existing_available_qty_on_location)
			if err != nil {
				e := errors.Wrap(err, "Failed to delete Container - Couldn't get quantity of item "+*container.SKU+" on location")
				tx.Rollback(ctx)
				return false, e
			}
			if existing_available_qty_on_location-int(*container.Quantity) < 0 {
				e := errors.New("Failed to delete Container - Quantity on location might be allocated in pick-path for item " + *container.SKU)
				tx.Rollback(ctx)
				return false, e

			} else if existing_qty_on_location-int(*container.Quantity) == 0 {
				// remove row or set null in case of last row from location
				err := c.App.Operations.RemoveItemFromLocation(tx, ctx, *container.SKU, *container.LocationID, *container.WarehouseID, container.BatchNumber, container.SerialNumber, container.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, err
				}
			} else {
				// Decrement quantity
				query := `UPDATE location_wise_inventory SET quantity = quantity - $1, available_qty = available_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL) AND location_id = $6`
				commandTag, err := tx.Exec(ctx, query, *container.Quantity, container.ItemID, container.BatchNumber, container.SerialNumber, container.ExpirationDate, container.LocationID)
				if err != nil {
					e := errors.Wrap(err, "Failed to delete Container - Error decrementing item "+*container.SKU+" quantity on location")
					tx.Rollback(ctx)
					return false, e
				}
				if commandTag.RowsAffected() != 1 {
					e := errors.New("Failed to delete Container - Quantity wasn't decremented on the location " + *container.LocationID + " for item" + *container.SKU)
					tx.Rollback(ctx)
					return false, e
				}
			}

			// Update tracked_inventory table
			if container.BatchNumber != nil || container.SerialNumber != nil || container.ExpirationDate != nil {
				//fetch tracked inventory
				var tracked_qty, available_qty, pickable_qty int
				query := `SELECT quantity, available_qty, pickable_qty FROM tracked_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL)`
				err := tx.QueryRow(ctx, query, container.ItemID, container.BatchNumber, container.SerialNumber, container.ExpirationDate).Scan(&tracked_qty, &available_qty, &pickable_qty)
				if err != nil {
					e := errors.Wrap(err, "Failed to delete Container - Unable to get tracked inventory for item "+*container.SKU)
					tx.Rollback(ctx)
					return false, e
				}
				if tracked_qty-int(*container.Quantity) < 0 || available_qty-int(*container.Quantity) < 0 {
					e := errors.New("Failed to delete Container - Item Descrepency found in tracked inventory for item " + *container.SKU)
					tx.Rollback(ctx)
					return false, e
				}
				if tracked_qty-int(*container.Quantity) == 0 {
					//delete row
					query := `DELETE FROM tracked_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL)`
					_, err := tx.Exec(ctx, query, container.ItemID, container.BatchNumber, container.SerialNumber, container.ExpirationDate)
					if err != nil {
						e := errors.Wrap(err, "Failed to delete Container - Unable to delete tracked inventory for item "+*container.SKU)
						tx.Rollback(ctx)
						return false, e
					}
				} else {
					// any tracked parameter exists
					query := `UPDATE tracked_inventory SET quantity = quantity - $1, available_qty = available_qty - $1, pickable_qty = pickable_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL)` // [TAP] - Pickable quantity is decremented at picking
					commandTag, err := tx.Exec(ctx, query, container.Quantity, container.ItemID, container.BatchNumber, container.SerialNumber, container.ExpirationDate)
					if err != nil {
						e := errors.Wrap(err, "Failed to delete Container - Unable to update tracked inventory for item "+*container.SKU)
						tx.Rollback(ctx)
						return false, e
					}
					if commandTag.RowsAffected() != 1 {
						e := errors.New("Failed to delete Container - Tracked inventory wasn't updated for item " + *container.SKU)
						tx.Rollback(ctx)
						return false, e
					}
				}
			}
		}
	}

	// Update available_qty in inventory
	for itemID, quantity := range itemQtyMap {
		query := `UPDATE inventory SET quantity = quantity - $1, pickable_qty = pickable_qty - $1, available_qty = available_qty - $1 WHERE item_id = $2`
		commandTag, err := tx.Exec(ctx, query, quantity, itemID)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "Failed to delete Container - Failed to update inventory for item "+itemSKUMap[itemID]+". Check if item is allocated in orders")
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "Failed to delete Container - Failed to update inventory for item "+itemSKUMap[itemID])
		}
	}

	// Delete from container_wise_inventory
	query = `DELETE FROM container_wise_inventory WHERE container_id = $1`
	if _, err := tx.Exec(ctx, query, v.ContainerID); err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "Failed to delete container")
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(tx_err, "Failed to commit transaction")
	}

	return true, nil
}

// # Get All Warehouse Containers
func (c *ContainerImpl) GetAllWarehouseContainers(warehouseID, search string) (*model.ContainerResponse, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query string

	var queryParams []any

	var searchPresent bool = false

	// # Wildcard Search Query
	if search != "" {
		// # Search Filter: Search by 'client_name', 'lp_code' (code), 'sku', 'scannable', 'batch_number' and 'serial_number'
		search = "%" + search + "%"
		searchPresent = true
	}

	// # Query Parameters
	queryParams = append(queryParams, warehouseID)

	// # Base Query
	query = `SELECT DISTINCT ON (container_id)
				container_id,
				container_type_id,
				client_id,
				warehouse_id,
				location_id,
				client_name,
				"code" AS lp_code,
				quantity AS total_qty,
				available_qty,
				sku,
				scannable,
				batch_number,
				serial_number,
				expiration_date,
				on_hold
			FROM
				container_wise_inventory
			WHERE
				warehouse_id = $1
			ORDER BY
				container_id DESC`

	// # Search Filter
	if searchPresent {
		query = `SELECT DISTINCT ON (container_id)
					container_id,
					container_type_id,
					client_id,
					warehouse_id,
					location_id,
					client_name,
					"code" AS lp_code,
					quantity AS total_qty,
					available_qty,
					sku,
					scannable,
					batch_number,
					serial_number,
					expiration_date,
					on_hold
				FROM
					container_wise_inventory
				WHERE
					warehouse_id = $1
					AND (
						client_name ILIKE $2
						OR "code" ILIKE $2
						OR sku ILIKE $2
						OR scannable ILIKE $2
						OR batch_number ILIKE $2
						OR serial_number ILIKE $2
					)
				ORDER BY
					container_id DESC`

		queryParams = append(queryParams, search)
	}

	// # Execute Query
	rows, err = c.DB.Query(ctx, query, queryParams...)
	if err != nil {
		err = errors.Wrap(err, "failed to query container wise inventory for warehouse containers")
		return nil, err
	}

	defer rows.Close()

	var warehouseContainers model.ContainerResponse
	var containers []*model.ContainerObject

	for rows.Next() {
		var container model.ContainerObject
		var quanityInfo model.QuantityInfo

		err = rows.Scan(
			&container.ContainerID,
			&container.ContainerTypeID,
			&container.ClientID,
			&container.WarehouseID,
			&container.LocationID,
			&container.ClientName,
			&container.LPCode,
			&quanityInfo.TotalQty,
			&quanityInfo.AvailableQty,
			&container.SKU,
			&container.Scannable,
			&container.BatchNumber,
			&container.SerialNumber,
			&container.ExpirationDate,
			&container.OnHold,
		)
		if err != nil {
			err = errors.Wrap(err, "unable to read resultant rows into container object schema")
			return nil, err
		}

		container.QuantityInfo = &quanityInfo

		containers = append(containers, &container)
	}

	err = rows.Err()
	if err != nil {
		err = errors.Wrap(err, "unable to read rows for warehouse containers")
		return nil, err
	}

	warehouseContainers = model.ContainerResponse{
		Containers: containers,
	}

	// # Return Warehouse Containers
	return &warehouseContainers, nil
}

// # Get Container Details Layout
func (c *ContainerImpl) GetContainerDetailsLayout(containerID, clientID, warehouseID string) (*model.ContainerDetailsLayout, error) {
	ctx := context.TODO()

	var err error

	// # Query
	query := `SELECT
				c.container_id,
				c.container_type_id,
				c.client_id,
				c.warehouse_id,
				l.area_id,
				l.location_id,
				l.area_name,
				c.client_name,
				c."code" AS lp_code,
				t."name" AS lp_type
			FROM
				container_wise_inventory c
				INNER JOIN location_wise_inventory l ON c.location_id = l.location_id
				INNER JOIN container_type t ON c.container_type_id = t."id"
			WHERE
				c.container_id = $1
				AND c.client_id = $2
				AND c.warehouse_id = $3
			LIMIT
				1`

	var containerDetailsLayout model.ContainerDetailsLayout

	// # Execute Query
	err = c.DB.QueryRow(ctx, query, containerID, clientID, warehouseID).Scan(
		&containerDetailsLayout.ContainerID,
		&containerDetailsLayout.ContainerTypeID,
		&containerDetailsLayout.ClientID,
		&containerDetailsLayout.WarehouseID,
		&containerDetailsLayout.AreaID,
		&containerDetailsLayout.LocationID,
		&containerDetailsLayout.AreaName,
		&containerDetailsLayout.ClientName,
		&containerDetailsLayout.LPCode,
		&containerDetailsLayout.LPType,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			err = errors.New("no rows found for container details layout")
			return nil, err
		}
		err = errors.Wrap(err, "unable to get container details layout")
		return nil, err
	}

	if containerDetailsLayout.ContainerID != nil && containerDetailsLayout.LPCode != nil {
		// # Form container 'QR Code' string
		containerID = (*containerDetailsLayout.ContainerID).String()
		lpCode := *containerDetailsLayout.LPCode

		containerQR := fmt.Sprintf("LP/%s/%s", containerID, lpCode)
		containerDetailsLayout.ContainerQR = &containerQR
	}

	if containerDetailsLayout.AreaID != nil {
		areaID := *containerDetailsLayout.AreaID

		req := core_proto.GetAreaByIDRequest{
			AreaId: areaID,
		}

		// # Call 'Get Area By ID' gRPC method to fetch 'area details' from 'core' service
		res, err := c.App.GrpcClient.Core.Client.GetAreaByID(ctx, &req)
		if err != nil {
			err = errors.Wrap(err, "failed to get area by ID")
			return nil, err
		}

		containerDetailsLayout.Properties = res.AreaProperties
	}

	// # Return Container Details Layout
	return &containerDetailsLayout, nil
}
