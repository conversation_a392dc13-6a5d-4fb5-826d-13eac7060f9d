package app

import (
	"context"
	"fmt"
	"inventory/constants"
	"inventory/model"
	"inventory/schema"
	"inventory/server/auth"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/jackc/pgconn"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/pkg/errors"
	"github.com/rs/zerolog"
	uuid "github.com/satori/go.uuid"

	core_proto "proto/core"
	inbound_proto "proto/inbound"
	reporting_proto "proto/reporting"
)

// Inventory defines methods of Inventory service to be implemented
type Inventory interface {
	AggregateARNItemsReport(*inbound_proto.AggregateARNItemsReportRequest) (*inbound_proto.AggregateARNItemsReportResponse, error)
	GetInventory(string, string, string, string, string, string, string, int64, int64) (*model.InventoryResponse, error)
	AuditInventory(*schema.ValidateAuditInventory, string) (bool, error)
	GetItemTrackedInventory(string, string, string, string, string, string, bool) (*model.TrackedObject, error)
	SearchInventory(string, string, string, int) ([]model.InventorySearch, error)
	GetConsolidatedInventory([]auth.ServiceData, int, int, string) (*model.ConsolidatedInventoryResponse, error)
	GetOrgInventory([]auth.ServiceData, string) ([]model.Inventory, error)
	GetAreaDetails(string) (*schema.AreaDetails, error)
	GetAllWarehouseItems(string, string) (*model.ItemResponse, error)
	GetLocationBySearch(string, string, string) ([]model.LocationLayoutSearchResponse, error)

	// # LeanShip #
	UpdateItemQuantity(*schema.ValidateUpdateItemQuantity) (bool, error)
}

// InventoryOpts contains arguments to be accepted for new instance of Inventory service
type InventoryOpts struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// InventoryImpl implements Inventory service
type InventoryImpl struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// InitInventory returns initializes Inventory service
func InitInventory(opts *InventoryOpts) Inventory {
	i := &InventoryImpl{
		App:    opts.App,
		DB:     opts.DB,
		Logger: opts.Logger,
	}
	return i
}

// # Aggregate ARN Items Report
func (i *InventoryImpl) AggregateARNItemsReport(req *inbound_proto.AggregateARNItemsReportRequest) (*inbound_proto.AggregateARNItemsReportResponse, error) {
	ctx := context.TODO()

	var err error

	// # Initiate the 'gRPC' server 'streaming' for the 'aggregate ARN items' report
	stream, err := i.App.GrpcClient.Inbound.Client.AggregateARNItemsReport(ctx, req)
	if err != nil {
		return nil, errors.Wrap(err, "failed to initiate the gRPC server streaming for the aggregate ARN items report")
	}

	var report []*inbound_proto.AggregateARNItemsReportDataResponse
	var totalDocuments, totalPages, currentPage int32

	// # Collect the 'streamed' report 'data' and 'metadata'
	for {
		// # Receive the 'next' stream 'item'
		item, err := stream.Recv()
		if err != nil {
			if err == io.EOF {
				// # The 'stream' has 'finished'
				break
			}
			return nil, errors.Wrap(err, "failed to receive the report data or metadata from the stream")
		}

		// # Handle the 'response' based on its type, whether it's 'data' or 'metadata'
		switch msg := item.Response.(type) {
		case *inbound_proto.AggregateARNItemsReportStreamResponse_Chunk:
			{
				// # Handle the report 'data'
				data := msg.Chunk.Data
				report = append(report, data...)
			}
		case *inbound_proto.AggregateARNItemsReportStreamResponse_Metadata:
			{
				// # Handle the report 'metadata'
				metadata := msg.Metadata

				totalDocuments = metadata.TotalDocuments
				totalPages = metadata.TotalPages
				currentPage = metadata.CurrentPage
			}
		}
	}

	response := inbound_proto.AggregateARNItemsReportResponse{
		TotalDocuments: totalDocuments,
		TotalPages:     totalPages,
		CurrentPage:    currentPage,
		Report:         report,
	}

	// # Return the 'response'
	return &response, nil
}

// # Get Inventory: It fetches all the warehouse 'inventory'
func (i *InventoryImpl) GetInventory(warehouseID, clientID, onHold, sortingValue, sortingOrder, search, searchModifier string, pageNo, pageSize int64) (*model.InventoryResponse, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query, searchQuery string
	var partialSearch, cleanSearch string

	var queryParams []any
	var paramIndex int

	var searchPresent, searchModifierPresent, sortPresent, isExactSearch bool

	if search != "" {
		// # Search Filter: Search by 'item name', 'item SKU', 'item scannable', and 'item description'
		searchPresent = true

		// # Utility to detect the 'exact' search -> if the 'search' query 'string' is 'wrapped' in the 'double' quotes
		isExactSearch = strings.HasPrefix(search, "\"") && strings.HasSuffix(search, "\"")
		cleanSearch = strings.Trim(search, "\"")
		partialSearch = "%" + cleanSearch + "%"
	}

	if searchModifier != "" {
		searchModifierPresent = true
	}

	if sortingValue != "" && sortingOrder != "" {
		// # Sort Filter: Sort by 'item name', 'client name', 'item SKU', 'item scannable', and 'created at date'
		sortPresent = true
	}

	var limit, offset *int64
	var all bool = false

	if pageNo < 1 || pageSize < 1 {
		all = true
		pageNo = 1
		pageSize = 10
	}

	limit = &pageSize

	offset_ := ((pageNo - 1) * (pageSize))
	offset = &offset_

	if all {
		limit = nil
		offset = nil
	}

	var clientId []string
	if clientID != "" {
		clientId = []string{clientID}
	}

	req := inbound_proto.AggregateARNItemsReportRequest{
		WarehouseId: warehouseID,
		Clients:     clientId,
	}

	// # Call 'Get Aggregate ARN Items Report' method
	report, err := i.AggregateARNItemsReport(&req)
	if err != nil {
		err = errors.Wrap(err, "failed to get item wise arn data")
		return nil, err
	}

	itemQtyMap := make(map[string]int32)

	itemReport := report.GetReport()

	for _, item := range itemReport {
		itemID := item.GetItemId()
		itemQty := item.GetQuantity()
		itemQtyMap[itemID] = itemQty
	}

	var orderBy string
	if sortPresent {
		// # Given Sort: Sort by the provided 'sorting value' field in the provided 'sorting order'
		orderBy = fmt.Sprintf("%s %s", sortingValue, sortingOrder)
	} else {
		// # Default Sort: Sort by the 'id' field in the 'descending' order
		orderBy = "id DESC"
	}

	var onHoldValue bool
	if onHold == "true" {
		onHoldValue = true
	} else {
		onHoldValue = false
	}

	// # Base Query For Inventory
	query = `SELECT * FROM inventory WHERE warehouse_id = $1`

	queryParams = append(queryParams, warehouseID)
	paramIndex = 2

	/* # Note:
	1. For the 'partial' search, 'enter' the string 'as-is'
	2. For the 'exact' search, 'enclose' the string in the 'double' quotes
	*/
	if searchPresent {
		if searchModifierPresent {
			// # Modifier Based Search: Search by the provided 'search modifier'
			if isExactSearch {
				// # Exact Search
				searchQuery = fmt.Sprintf(" AND (%s = $%d)", searchModifier, paramIndex)
				search = cleanSearch
			} else {
				// # Partial Search
				searchQuery = fmt.Sprintf(" AND (%s ILIKE $%d)", searchModifier, paramIndex)
				search = partialSearch
			}
		} else {
			// # Default Search: Search by the 'item name' and 'item SKU'
			if isExactSearch {
				// # Exact Search
				searchQuery = fmt.Sprintf(" AND (name = $%d OR sku = $%d)", paramIndex, paramIndex)
				search = cleanSearch
			} else {
				// # Partial Search
				searchQuery = fmt.Sprintf(" AND (name ILIKE $%d OR sku ILIKE $%d)", paramIndex, paramIndex)
				search = partialSearch
			}
		}
		query += searchQuery
		queryParams = append(queryParams, search)
		paramIndex++
	}

	// # Client ID Filter
	if clientID != "" {
		searchQuery = fmt.Sprintf(" AND client_id = $%d", paramIndex)
		query += searchQuery
		queryParams = append(queryParams, clientID)
		paramIndex++
	}

	// # On Hold Filter
	if onHold != "" {
		searchQuery = fmt.Sprintf(" AND on_hold = $%d", paramIndex)
		query += searchQuery
		queryParams = append(queryParams, onHoldValue)
		paramIndex++
	}

	// # Pagination Filter
	searchQuery = fmt.Sprintf(" ORDER BY %s LIMIT $%d OFFSET $%d", orderBy, paramIndex, paramIndex+1)
	query += searchQuery
	queryParams = append(queryParams, limit, offset)

	// # Execute Query
	rows, err = i.DB.Query(ctx, query, queryParams...)
	if err != nil {
		err = errors.Wrap(err, "failed to query for inventory")
		return nil, err
	}

	defer rows.Close()

	var items []*model.Inventory

	for rows.Next() {
		var item model.Inventory

		err = rows.Scan(
			&item.ID,
			&item.ItemID,
			&item.ClientID,
			&item.WarehouseID,
			&item.Quantity,
			&item.AvailableQty,
			&item.PickableQty,
			&item.BaseUnit,
			&item.SKU,
			&item.Scannable,
			&item.CreatedAt,
			&item.UpdatedAt,
			&item.IsDeleted,
			&item.OnHold,
			&item.IsBatchControlled,
			&item.IsTrackedBySerialNo,
			&item.Name,
			&item.Description,
			&item.OnHoldBy,
			&item.IsPerishable,
			&item.TrackedBy,
			&item.Image,
			&item.BackOrders,
			&item.ClientName,
			&item.IsClientDeleted,
			&item.IsKit,
			&item.DynamicColumn,

			// # LeanShip #
			&item.IsLeanShip,
		)
		if err != nil {
			err = errors.Wrap(err, "unable to read resultant rows into inventory schema")
			return nil, err
		}

		itemID := item.ItemID
		itemInboundQty := itemQtyMap[itemID]

		item.InboundQty = itemInboundQty

		items = append(items, &item)
	}

	err = rows.Err()
	if err != nil {
		err = errors.Wrap(err, "unable to read inventory rows")
		return nil, err
	}

	var itemIDs []string
	for _, item := range items {
		itemID := item.ItemID
		itemIDs = append(itemIDs, itemID)
	}

	map_total_lp_count := make(map[string]int32)
	map_lp_count_info := make(map[string][]model.LPCountInfo)

	excludeLPType := "loose"

	query = `SELECT
				c.item_id,
				t."name" AS lp_type,
				COUNT(t."name") AS "count"
			FROM
				container_wise_inventory c
				INNER JOIN container_type t ON c.container_type_id = t."id"
			WHERE
				c.warehouse_id = $1
				AND c.item_id = ANY ($2)
				AND t."name" != $3
			GROUP BY
				c.item_id,
				t."name"`

	rows, err = i.DB.Query(ctx, query, warehouseID, itemIDs, excludeLPType)
	if err != nil {
		err = errors.Wrap(err, "failed to query container wise inventory for lp count")
		return nil, err
	}

	for rows.Next() {
		var container model.LPCount

		err = rows.Scan(
			&container.ItemID,
			&container.LPType,
			&container.Count,
		)
		if err != nil {
			err = errors.Wrap(err, "unable to read resultant rows into container schema")
			return nil, err
		}

		itemID := container.ItemID

		map_total_lp_count[itemID] += container.Count

		lpCountInfo := model.LPCountInfo{
			LPType:  container.LPType,
			LPCount: container.Count,
		}

		map_lp_count_info[itemID] = append(map_lp_count_info[itemID], lpCountInfo)
	}

	err = rows.Err()
	if err != nil {
		err = errors.Wrap(err, "unable to read container wise inventory rows")
		return nil, err
	}

	for i, item := range items {
		itemID := item.ItemID
		items[i].TotalLPCount = map_total_lp_count[itemID]
		items[i].LPCountInfo = map_lp_count_info[itemID]
	}

	var resultCount, totalPages int64

	// # Base Query For Inventory Count
	query = `SELECT COUNT(item_id) FROM inventory WHERE warehouse_id = $1`

	// # Reset Query Params: length becomes '0' but capacity remains the 'same'
	queryParams = queryParams[:0]

	queryParams = append(queryParams, warehouseID)
	paramIndex = 2

	/* # Note:
	1. For the 'partial' search, 'enter' the string 'as-is'
	2. For the 'exact' search, 'enclose' the string in the 'double' quotes
	*/
	if searchPresent {
		if searchModifierPresent {
			// # Modifier Based Search: Search by the provided 'search modifier'
			if isExactSearch {
				// # Exact Search
				searchQuery = fmt.Sprintf(" AND (%s = $%d)", searchModifier, paramIndex)
				search = cleanSearch
			} else {
				// # Partial Search
				searchQuery = fmt.Sprintf(" AND (%s ILIKE $%d)", searchModifier, paramIndex)
				search = partialSearch
			}
		} else {
			// # Default Search: Search by the 'item name' and 'item SKU'
			if isExactSearch {
				// # Exact Search
				searchQuery = fmt.Sprintf(" AND (name = $%d OR sku = $%d)", paramIndex, paramIndex)
				search = cleanSearch
			} else {
				// # Partial Search
				searchQuery = fmt.Sprintf(" AND (name ILIKE $%d OR sku ILIKE $%d)", paramIndex, paramIndex)
				search = partialSearch
			}
		}
		query += searchQuery
		queryParams = append(queryParams, search)
		paramIndex++
	}

	// # Client ID Filter
	if clientID != "" {
		searchQuery = fmt.Sprintf(" AND client_id = $%d", paramIndex)
		query += searchQuery
		queryParams = append(queryParams, clientID)
		paramIndex++
	}

	// # On Hold Filter
	if onHold != "" {
		searchQuery = fmt.Sprintf(" AND on_hold = $%d", paramIndex)
		query += searchQuery
		queryParams = append(queryParams, onHoldValue)
	}

	// # Execute Query
	err = i.DB.QueryRow(ctx, query, queryParams...).Scan(&resultCount)
	if err != nil {
		if err == pgx.ErrNoRows {
			err = errors.Wrap(err, "no rows found for inventory count")
			return nil, err
		}
		err = errors.Wrap(err, "unable to get count of inventory rows")
		return nil, err
	}

	if !all {
		if (resultCount % pageSize) == 0 {
			totalPages = (resultCount / pageSize)
		} else {
			totalPages = (resultCount / pageSize) + 1
		}
	} else {
		totalPages = 1
	}

	inventory := model.InventoryResponse{
		TotalDocuments: resultCount,
		TotalPages:     totalPages,
		CurrentPage:    pageNo,
		Data:           items,
		SearchParams:   constants.InventorySearchParams,
	}

	// # Return the 'warehouse inventory'
	return &inventory, nil
}

// Audit inventory, replace system quantity with physical quantity
func (i *InventoryImpl) AuditInventory(data *schema.ValidateAuditInventory, whID string) (bool, error) {
	ctx := context.Background()

	// Create transaction
	tx, err := i.DB.Begin(ctx)
	if err != nil {
		return false, errors.Wrap(err, "unable to begin transaction")
	}

	defer tx.Rollback(ctx)

	// Check if either locations ar on hold
	var source_on_hold bool
	var area_id, locationCode string
	query := `SELECT on_hold, area_id, code FROM location_wise_inventory WHERE location_id = $1 LIMIT 1`
	err = tx.QueryRow(ctx, query, data.LocationID).Scan(&source_on_hold, &area_id, &locationCode)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to set details of source location")
	}
	if source_on_hold {
		tx.Rollback(ctx)
		return false, errors.New("source location is on hold")
	}

	var getIsPickableRequest core_proto.CheckAreaTypeRequest
	getIsPickableRequest.AreaId = area_id
	getIsPickableRequest.Property = "picking"
	sourceArea, err := i.App.GrpcClient.Core.Client.CheckAreaType(ctx, &getIsPickableRequest)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to get property from core areas")
	}

	var auditAgeDataRequest reporting_proto.AuditInventoryUpdateRequest

	var containerID uuid.UUID
	if data.ContainerID == uuid.Nil {
		// Get loose container from location id
		// Get loose container id
		type_id, err := i.App.Operations.GetContainerTypeID("loose", whID)
		if err != nil {
			tx.Rollback(ctx)
			return false, err
		}
		// Get loose container from location
		query := `SELECT container_id FROM container_wise_inventory WHERE location_id = $1 AND container_type_id = $2 AND warehouse_id = $3 LIMIT 1`
		err = tx.QueryRow(context.TODO(), query, data.LocationID, type_id, whID).Scan(&containerID)
		if err != nil {
			if err == pgx.ErrNoRows {
				// Create an empty loose container on location

				// Previous container code
				prevCodeInt, err := i.App.Container.GetPreviousContainerCodeInt(whID)
				if err != nil {
					tx.Rollback(ctx)
					return false, err
				}
				code := "LP-" + strconv.Itoa(*prevCodeInt+1)
				container_id := uuid.NewV4()
				containerID = container_id

				query = `INSERT INTO container_wise_inventory (item_id, sku, batch_number, serial_number, expiration_date, scannable, container_id, container_type_id, location_id, warehouse_id, code, quantity, available_qty, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)`
				commandTag, err := tx.Exec(context.TODO(), query, nil, nil, nil, nil, nil, nil, container_id, type_id, data.LocationID, whID, code, 0, 0, time.Now())
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "unable to insert new loose container")
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return false, errors.New("new lose container wasn't inserted")
				}

			} else {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "failed to get loose container from location")
			}
		}
	} else {
		containerID = data.ContainerID

		// Get loose container id
		type_id, err := i.App.Operations.GetContainerTypeID("loose", whID)
		if err != nil {
			tx.Rollback(ctx)
			return false, err
		}

		// Check if container is loose
		query := `SELECT container_type_id FROM container_wise_inventory WHERE container_id = $1`
		var containerTypeID *int
		err = tx.QueryRow(ctx, query, containerID).Scan(&containerTypeID)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "failed to get container type id")
		}
		isLoose := false
		if type_id == containerTypeID {
			isLoose = true
		}

		// Update reference no on container if provided, else set it to NULL
		if data.ContainerRefNo != nil && !isLoose {
			var refNo *string
			if len(*data.ContainerRefNo) > 0 {
				refNo = data.ContainerRefNo
			} else {
				refNo = nil
			}

			query := `UPDATE container_wise_inventory SET ref_no = $1 WHERE container_id = $2`
			_, err := tx.Exec(ctx, query, refNo, containerID)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "unable to update reference number on container")
			}
		}
	}
	var backOrderAutoAlloationItems []string
	var itemsToSync []string
	map_items_for_orderAllocation := make(map[string]string)
	// Iterate over items in data
	for _, item := range data.AuditItems {
		// Quantity to be updated in the inventory
		var update_inventory int

		// Check if the location id sent matches with the location on container in case container is already on location
		var containerClientID, containerLocationID *string
		var existingContainerType string
		query := `SELECT c.client_id, c.location_id, ct."name" FROM container_wise_inventory c INNER JOIN container_type ct ON c.container_type_id = ct.id WHERE c.container_id = $1`
		err = tx.QueryRow(ctx, query, containerID).Scan(&containerClientID, &containerLocationID, &existingContainerType)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "failed to get location id from container")
		}
		if containerLocationID != nil && *containerLocationID != data.LocationID {
			tx.Rollback(ctx)
			return false, errors.New("Container is already associated with a different location")
		}

		if containerClientID != nil && existingContainerType != "loose" {
			if *containerClientID != item.ClientID {
				tx.Rollback(ctx)
				return false, errors.New("container belongs to another client")
			}
		}

		// Get item details from inventory
		inventory_item, err := i.App.Operations.GetItemFromInventory(item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to get the item details from inventory "+item.ItemID)
		}

		var existing_qty_on_container int
		query = `SELECT quantity FROM container_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND container_id = $4 AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)`
		err = tx.QueryRow(ctx, query, item.ItemID, item.BatchNumber, item.SerialNumber, containerID, data.LocationID, whID, item.ExpirationDate).Scan(&existing_qty_on_container)
		if err == pgx.ErrNoRows {
			// Item doesn't exist on the container

			// To be updated in the inventory later
			update_inventory = item.Quantity

			// Check if the item exists on the location
			var exists bool
			query := `SELECT EXISTS(SELECT 1 FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND location_id = $4 AND warehouse_id = $5 AND (expiration_date = $6 OR $6 IS NULL)) AS "exists"`
			err = tx.QueryRow(ctx, query, item.ItemID, item.BatchNumber, item.SerialNumber, data.LocationID, whID, item.ExpirationDate).Scan(&exists)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "unable to check if item exists on location")
			}
			if exists {
				// Increment the quantity on the location since a item is getting added to a container on that location
				query := `UPDATE location_wise_inventory SET quantity = quantity + $1, available_qty = available_qty + $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)`
				commandTag, err := tx.Exec(ctx, query, item.Quantity, item.ItemID, item.BatchNumber, item.SerialNumber, data.LocationID, whID, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "error incrementing sku quantity on location")
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return false, errors.New("sku quantity wasn't incremented on the location")
				}
			} else {
				// Item doesn't exist on the location, insert a new row
				err := i.App.Operations.InsertItemInLocation(tx, ctx, data.LocationID, whID, item.Quantity, item.Quantity, inventory_item, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, err
				}
			}

			// Insert into container
			err = i.App.Operations.InsertItemInContainer(tx, ctx, containerID, data.LocationID, whID, item.Quantity, item.Quantity, inventory_item, item.BatchNumber, item.SerialNumber, item.ExpirationDate, nil)
			if err != nil {
				tx.Rollback(ctx)
				return false, err
			}
		} else if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "failed to get item from container")
		} else {
			if item.AddNewItem {
				tx.Rollback(ctx)
				return false, errors.New("Item already exists on the location. Please use audit to update the quantity.")
			}
			if item.Quantity == 0 {
				update_inventory = 0 - existing_qty_on_container

				// Quantity is zero, remove item from the container
				err := i.App.Operations.RemoveItemFromContainer(tx, ctx, containerID, item.ItemID, data.LocationID, whID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, err
				}

				// Decrement the container's existing quantity from the location

				// Get existing quantity on the location
				var qty_on_location int
				query := `SELECT quantity FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND location_id = $4 AND warehouse_id = $5 AND (expiration_date = $6 OR $6 IS NULL)`
				err = tx.QueryRow(ctx, query, item.ItemID, item.BatchNumber, item.SerialNumber, data.LocationID, whID, item.ExpirationDate).Scan(&qty_on_location)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "couldn't get quantity of item on location")
				}

				// Remaining quantity on the location after removal of the item in conatainer
				remaining_qty_on_location := qty_on_location - existing_qty_on_container

				if remaining_qty_on_location == 0 {
					// Remove the item from the location
					err := i.App.Operations.RemoveItemFromLocation(tx, ctx, item.ItemID, data.LocationID, whID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
					if err != nil {
						tx.Rollback(ctx)
						return false, err
					}
				} else {
					// Decrement container's existing quantity from location quantity
					query := `UPDATE location_wise_inventory SET quantity = quantity - $1, available_qty = available_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)`
					commandTag, err := tx.Exec(ctx, query, existing_qty_on_container, item.ItemID, item.BatchNumber, item.SerialNumber, data.LocationID, whID, item.ExpirationDate)
					if err != nil {
						tx.Rollback(ctx)
						return false, errors.Wrap(err, "error decrementing item quantity on location")
					}
					if commandTag.RowsAffected() != 1 {
						tx.Rollback(ctx)
						return false, errors.New("quantity wasn't decremented on the location")
					}
				}

			} else {

				// Update this quantity on location
				qty_to_update := item.Quantity - existing_qty_on_container

				// To be updated in the inventory table later
				update_inventory = qty_to_update

				// update on location
				query := `UPDATE location_wise_inventory SET quantity = quantity + $1, available_qty = available_qty + $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND location_id = $5 AND warehouse_id = $6 AND (expiration_date = $7 OR $7 IS NULL)`
				commandTag, err := tx.Exec(ctx, query, qty_to_update, item.ItemID, item.BatchNumber, item.SerialNumber, data.LocationID, whID, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "error updating item quantity on location")
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return false, errors.New("quantity wasn't updated on the location")
				}

				// replace the quantity in the container
				query = `UPDATE container_wise_inventory SET quantity = $1, available_qty = available_qty + $2 WHERE item_id = $3 AND (batch_number = $4 OR $4 IS NULL) AND (serial_number = $5 OR $5 IS NULL) AND container_id = $6 AND location_id = $7 AND warehouse_id = $8 AND (expiration_date = $9 OR $9 IS NULL)`
				commandTag, err = tx.Exec(ctx, query, item.Quantity, qty_to_update, item.ItemID, item.BatchNumber, item.SerialNumber, containerID, data.LocationID, whID, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "error updating item quantity in container")
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return false, errors.New("quantity wasn't updated in the container")
				}
			}
		}

		var update_pickable, update_available int
		if sourceArea.GetIsAreaPickable() {
			update_pickable = update_inventory
		}
		if !sourceArea.GetIsAreaHold() {
			update_available = update_inventory
		}

		if !item.AddNewItem {
			// Check if any quantity is allocated in orders
			var availableQty, pickableQty int
			query = `SELECT available_qty, pickable_qty FROM inventory where item_id = $1`
			err = tx.QueryRow(ctx, query, item.ItemID).Scan(&availableQty, &pickableQty)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "failed to get quantities from inventory")
			}
			if (availableQty+update_available) < 0 || (pickableQty+update_pickable) < 0 {
				tx.Rollback(ctx)
				return false, errors.New("There are some orders which contain this item. Please deallocate the item from these orders")
			}
		}

		// Update quantity in inventory
		query = `UPDATE inventory SET quantity = quantity + $1, available_qty = available_qty + $2, pickable_qty = pickable_qty + $3, updated_at = $4 WHERE item_id = $5`
		commandTag, err := tx.Exec(ctx, query, update_inventory, update_available, update_pickable, time.Now(), item.ItemID)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to update inventory")
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return false, errors.New("inventory wasn't updated")
		}

		if item.BatchNumber != nil || item.SerialNumber != nil || item.ExpirationDate != nil {
			if !item.AddNewItem {
				// Check if any quantity is allocated in orders
				var availableQty, pickableQty int
				query = `SELECT available_qty, pickable_qty FROM tracked_inventory where item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL)`
				err = tx.QueryRow(ctx, query, item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate).Scan(&availableQty, &pickableQty)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "failed to get quantities from inventory")
				}
				if (availableQty+update_available) < 0 || (pickableQty+update_pickable) < 0 {
					tx.Rollback(ctx)
					return false, errors.New("There are some orders to which this item is allocated. Please deallocate the item from these orders")
				}
			}

			// Check if item exists in tracked inventory
			var exists bool
			query := `SELECT EXISTS(SELECT 1 FROM tracked_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL)) AS "exists"`
			err = tx.QueryRow(ctx, query, item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate).Scan(&exists)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "unable to check if item exists in tracked inventory")
			}

			if exists {
				// any tracked parameter exists
				query := `UPDATE tracked_inventory SET quantity = quantity + $1, available_qty = available_qty + $2, pickable_qty = pickable_qty + $3 WHERE item_id = $4 AND (batch_number = $5 OR $5 IS NULL) AND (serial_number = $6 OR $6 IS NULL) AND (expiration_date = $7 OR $7 IS NULL)`
				commandTag, err := tx.Exec(ctx, query, update_inventory, update_available, update_pickable, item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "unable to update tracked inventory")
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return false, errors.New("tracked inventory wasn't updated")
				}
			} else {
				query := `INSERT INTO tracked_inventory (warehouse_id, client_id, item_id, batch_number, serial_number, expiration_date, quantity, available_qty, pickable_qty) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`
				commandTag, err := tx.Exec(ctx, query, whID, item.ClientID, item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate, update_inventory, update_available, update_pickable)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "Unable to insert new row in tracked inventory!")
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return false, errors.New("New row in tracked inventory wasn't inserted!")
				}
			}

		}

		var containerCode *string
		var containerID *uuid.UUID
		containerType := "loose"
		if data.ContainerID != uuid.Nil {
			v := data.ContainerID
			containerID = &v

			// Get container type from id
			query := `SELECT ct.name, c.code FROM container_wise_inventory c INNER JOIN container_type ct ON ct.id = c.container_type_id WHERE c.container_id = $1`
			err = tx.QueryRow(ctx, query, data.ContainerID).Scan(&containerType, &containerCode)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "unable to get the container type")
			}
		}

		// Add to transaction
		transactionData := &model.TransactionData{
			ClientID:       item.ClientID,
			WarehouseID:    whID,
			RequestID:      data.RequestID,
			ItemID:         item.ItemID,
			SKU:            item.SKU,
			Action:         "audit_inventory",
			Change:         update_inventory,
			Username:       data.Username,
			UserID:         data.UserID,
			ChangedAt:      time.Now().UTC(),
			BatchNumber:    item.BatchNumber,
			SerialNumber:   item.SerialNumber,
			ExpirationDate: item.ExpirationDate,
			Source:         "self",
			Document:       "Audit",
			ClientName:     item.ClientName,
			Remarks:        []model.Remark{{Key: "audit_reason", Value: item.Reason}},
			LocationID:     data.LocationID,
			LocationCode:   locationCode,
			ContainerID:    containerID,
			ContainerCode:  containerCode,
			ContainerType:  containerType,
		}
		err = i.App.Operations.CreateTransaction(tx, transactionData)
		if err != nil {
			tx.Rollback(ctx)
			return false, err
		}

		// Add to ageing audit data
		auditItem := reporting_proto.AuditItem{
			ItemId:     item.ItemID,
			LocationId: data.LocationID,
			Quantity:   int32(update_inventory),
		}
		if data.ContainerID != uuid.Nil {
			auditItem.ContainerId = data.ContainerID.String()
		}
		if item.BatchNumber != nil {
			auditItem.BatchNumber = *item.BatchNumber
		}
		if item.SerialNumber != nil {
			auditItem.SerialNumber = *item.SerialNumber
		}
		if item.ExpirationDate != nil {
			auditItem.ExpirationDate = item.ExpirationDate.Format("2006-01-02")
		}

		auditAgeDataRequest.AuditItems = append(auditAgeDataRequest.AuditItems, &auditItem)

		if update_inventory > 0 {
			//updating order if the quanity is changed
			if _, ok := map_items_for_orderAllocation[item.ItemID]; !ok {
				backOrderAutoAlloationItems = append(backOrderAutoAlloationItems, item.ItemID)
				map_items_for_orderAllocation[item.ItemID] = item.ItemID
			}
		}

		// Update inventory in Shopify
		itemsToSync = append(itemsToSync, item.ItemID)
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "failed to commit transaction, rolling back")
	}

	if len(data.AuditItems) > 0 {
		res, err := i.App.GrpcClient.Reporting.Client.AuditInventoryUpdate(ctx, &auditAgeDataRequest)
		if err != nil {
			sentry.CaptureException(errors.Wrap(err, "Audit inventory: failed to update age data in reporting"))
		}
		if !res.GetSuccess() {
			sentry.CaptureException(errors.Wrap(err, "Audit inventory: failed to update age data in reporting"))
		}

		// Auto allocation of back orders
		if len(backOrderAutoAlloationItems) > 0 {
			go i.App.Operations.BackOrderAutoAlloation(backOrderAutoAlloationItems, nil, model.AutditInventoryTrigger, true)
		}

		go func(items []string) {
			for _, item := range items {
				go i.App.Operations.UpdateShopifyInventory(item)
			}
		}(itemsToSync)
	}

	return true, nil
}

// # Get Item And Validate Client
func (i *InventoryImpl) GetItemAndValidateClient(clientID, itemID string) (*model.Inventory, error) {
	var err error

	// # Get item from inventory
	item, err := i.App.Operations.GetItemFromInventoryByItemId(itemID)
	if err != nil {
		return nil, err
	}

	// # Get 'clientID' from 'item' if not provided
	if clientID == "" {
		// # For 'backward compatibility' with old APIs
		clientID = item.ClientID
	}

	// # Check if 'client' is deleted
	err = i.App.Operations.CheckClientDeleted(clientID)
	if err != nil {
		return nil, err
	}

	// # Return the item
	return item, nil
}

// # Get Batch Inventory
func (i *InventoryImpl) GetBatchInventory(datePresent, searchPresent bool, itemID, search string, pickableQty int, startDate, endDate time.Time) (*model.TrackedObject, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query string

	var paramIndex int
	var queryParams []interface{}

	var trackedInventory model.TrackedObject

	var batchNumbers []*model.BatchObject

	query = `SELECT t.batch_number, t.quantity, i.base_unit AS unit, t.available_qty, t.pickable_qty FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.quantity > 0 AND t.pickable_qty > $2`

	paramIndex = 3
	queryParams = append(queryParams, itemID, pickableQty)

	if datePresent {
		query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
		queryParams = append(queryParams, startDate, endDate)
		paramIndex += 2
	}

	if searchPresent {
		query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
		queryParams = append(queryParams, search)
	}

	query += ` ORDER BY t.batch_number DESC`

	rows, err = i.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query tracked inventory")
	}

	defer rows.Close()

	for rows.Next() {
		var batchObject model.BatchObject

		err = rows.Scan(
			&batchObject.BatchNumber,
			&batchObject.Quantity,
			&batchObject.Unit,
			&batchObject.AvailableQty,
			&batchObject.PickableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
		}

		batchNumbers = append(batchNumbers, &batchObject)
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows")
	}

	trackedInventory.BatchNumbers = batchNumbers

	return &trackedInventory, nil
}

// # Get Serial Inventory
func (i *InventoryImpl) GetSerialInventory(datePresent, searchPresent bool, itemID, search string, pickableQty int, startDate, endDate time.Time) (*model.TrackedObject, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query string

	var paramIndex int
	var queryParams []interface{}

	var trackedInventory model.TrackedObject

	var serialNumbers []*model.SerialObject

	query = `SELECT t.serial_number, t.quantity, i.base_unit AS unit, t.available_qty, t.pickable_qty FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.quantity > 0 AND t.pickable_qty > $2`

	paramIndex = 3
	queryParams = append(queryParams, itemID, pickableQty)

	if datePresent {
		query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
		queryParams = append(queryParams, startDate, endDate)
		paramIndex += 2
	}

	if searchPresent {
		query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
		queryParams = append(queryParams, search)
	}

	query += ` ORDER BY t.serial_number DESC`

	rows, err = i.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query tracked inventory")
	}

	defer rows.Close()

	for rows.Next() {
		var serialObject model.SerialObject

		err = rows.Scan(
			&serialObject.SerialNumber,
			&serialObject.Quantity,
			&serialObject.Unit,
			&serialObject.AvailableQty,
			&serialObject.PickableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
		}

		serialNumbers = append(serialNumbers, &serialObject)
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows")
	}

	trackedInventory.SerialNumbers = serialNumbers

	return &trackedInventory, nil
}

// # Get Perishable Inventory
func (i *InventoryImpl) GetPerishableInventory(datePresent, searchPresent bool, itemID, search string, pickableQty int, startDate, endDate time.Time) (*model.TrackedObject, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query string

	var paramIndex int
	var queryParams []interface{}

	var trackedInventory model.TrackedObject

	var expDates []*model.ExpirationObject

	query = `SELECT t.expiration_date, t.quantity, i.base_unit AS unit, t.available_qty, t.pickable_qty FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.quantity > 0 AND t.pickable_qty > $2`

	paramIndex = 3
	queryParams = append(queryParams, itemID, pickableQty)

	if datePresent {
		query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
		queryParams = append(queryParams, startDate, endDate)
		paramIndex += 2
	}

	if searchPresent {
		query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
		queryParams = append(queryParams, search)
	}

	query += ` ORDER BY t.expiration_date ASC`

	rows, err = i.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query tracked inventory")
	}

	defer rows.Close()

	for rows.Next() {
		var expObject model.ExpirationObject

		err = rows.Scan(
			&expObject.ExpirationDate,
			&expObject.Quantity,
			&expObject.Unit,
			&expObject.AvailableQty,
			&expObject.PickableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
		}

		expDates = append(expDates, &expObject)
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows")
	}

	trackedInventory.ExpirationDates = expDates

	return &trackedInventory, nil
}

// # Get Batch And Serial Inventory
func (i *InventoryImpl) GetBatchAndSerialInventory(datePresent, searchPresent bool, itemID, search string, pickableQty int, startDate, endDate time.Time) (*model.TrackedObject, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query string

	var paramIndex int
	var queryParams []interface{}

	var trackedInventory model.TrackedObject

	var batchNumbers []*model.BatchObject

	query = `SELECT t.batch_number, SUM(t.quantity), i.base_unit AS unit, SUM(t.available_qty), SUM(t.pickable_qty) FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.quantity > 0 AND t.pickable_qty > $2`

	paramIndex = 3
	queryParams = append(queryParams, itemID, pickableQty)

	if datePresent {
		query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
		queryParams = append(queryParams, startDate, endDate)
		paramIndex += 2
	}

	if searchPresent {
		query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
		queryParams = append(queryParams, search)
	}

	query += ` GROUP BY t.batch_number, i.base_unit ORDER BY t.batch_number DESC`

	rows, err = i.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query tracked inventory")
	}

	defer rows.Close()

	for rows.Next() {
		var batchObject model.BatchObject

		err = rows.Scan(
			&batchObject.BatchNumber,
			&batchObject.Quantity,
			&batchObject.Unit,
			&batchObject.AvailableQty,
			&batchObject.PickableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
		}

		batchNumbers = append(batchNumbers, &batchObject)
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows")
	}

	for _, batch := range batchNumbers {
		var serialNumbers []*model.SerialObject

		query = `SELECT t.serial_number, t.quantity, i.base_unit AS unit, t.available_qty, t.pickable_qty, t.batch_number FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.batch_number = $2 AND t.quantity > 0 AND t.pickable_qty > $3`

		paramIndex = 4
		queryParams = queryParams[:0]
		queryParams = append(queryParams, itemID, batch.BatchNumber, pickableQty)

		if datePresent {
			query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
			queryParams = append(queryParams, startDate, endDate)
			paramIndex += 2
		}

		if searchPresent {
			query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
			queryParams = append(queryParams, search)
		}

		query += ` ORDER BY t.serial_number DESC`

		rows, err = i.DB.Query(ctx, query, queryParams...)
		if err != nil {
			return nil, errors.Wrap(err, "failed to query tracked inventory")
		}

		for rows.Next() {
			var serialObject model.SerialObject

			err = rows.Scan(
				&serialObject.SerialNumber,
				&serialObject.Quantity,
				&serialObject.Unit,
				&serialObject.AvailableQty,
				&serialObject.PickableQty,
				&serialObject.BatchNumber,
			)
			if err != nil {
				return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
			}

			serialNumbers = append(serialNumbers, &serialObject)
		}

		err = rows.Err()
		if err != nil {
			return nil, errors.Wrap(err, "unable to read rows")
		}

		batch.SerialNumbers = serialNumbers
		trackedInventory.SerialNumbers = append(trackedInventory.SerialNumbers, serialNumbers...)
	}

	trackedInventory.BatchNumbers = batchNumbers

	return &trackedInventory, nil
}

// # Get Batch And Perishable Inventory
func (i *InventoryImpl) GetBatchAndPerishableInventory(datePresent, searchPresent bool, itemID, search string, pickableQty int, startDate, endDate time.Time) (*model.TrackedObject, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query string

	var paramIndex int
	var queryParams []interface{}

	var trackedInventory model.TrackedObject

	var batchNumbers []*model.BatchObject

	query = `SELECT t.batch_number, SUM(t.quantity), i.base_unit AS unit, SUM(t.available_qty), SUM(t.pickable_qty) FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.quantity > 0 AND t.pickable_qty > $2`

	paramIndex = 3
	queryParams = append(queryParams, itemID, pickableQty)

	if datePresent {
		query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
		queryParams = append(queryParams, startDate, endDate)
		paramIndex += 2
	}

	if searchPresent {
		query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
		queryParams = append(queryParams, search)
	}

	query += ` GROUP BY t.batch_number, i.base_unit ORDER BY t.batch_number DESC`

	rows, err = i.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query tracked inventory for batch")
	}

	defer rows.Close()

	for rows.Next() {
		var batchObject model.BatchObject

		err = rows.Scan(
			&batchObject.BatchNumber,
			&batchObject.Quantity,
			&batchObject.Unit,
			&batchObject.AvailableQty,
			&batchObject.PickableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
		}

		batchNumbers = append(batchNumbers, &batchObject)
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows")
	}

	for _, batch := range batchNumbers {
		var expDates []*model.ExpirationObject

		query = `SELECT t.expiration_date, t.quantity, i.base_unit AS unit, t.available_qty, t.pickable_qty, t.batch_number FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.batch_number = $2 AND t.quantity > 0 AND t.pickable_qty > $3`

		paramIndex = 4
		queryParams = queryParams[:0]
		queryParams = append(queryParams, itemID, batch.BatchNumber, pickableQty)

		if datePresent {
			query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
			queryParams = append(queryParams, startDate, endDate)
			paramIndex += 2
		}

		if searchPresent {
			query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
			queryParams = append(queryParams, search)
		}

		query += ` ORDER BY t.expiration_date ASC`

		rows, err = i.DB.Query(ctx, query, queryParams...)
		if err != nil {
			return nil, errors.Wrap(err, "failed to query tracked inventory for batch expiry")
		}

		for rows.Next() {
			var expObject model.ExpirationObject

			err = rows.Scan(
				&expObject.ExpirationDate,
				&expObject.Quantity,
				&expObject.Unit,
				&expObject.AvailableQty,
				&expObject.PickableQty,
				&expObject.BatchNumber,
			)
			if err != nil {
				return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
			}

			expDates = append(expDates, &expObject)
		}

		err = rows.Err()
		if err != nil {
			return nil, errors.Wrap(err, "unable to read rows")
		}

		batch.ExpirationDates = expDates
	}

	trackedInventory.BatchNumbers = batchNumbers

	var expDates []*model.ExpirationObject

	query = `SELECT t.expiration_date, t.quantity, i.base_unit AS unit, t.available_qty, t.pickable_qty, t.batch_number FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.quantity > 0 AND t.pickable_qty > $2`

	paramIndex = 3
	queryParams = queryParams[:0]
	queryParams = append(queryParams, itemID, pickableQty)

	if datePresent {
		query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
		queryParams = append(queryParams, startDate, endDate)
		paramIndex += 2
	}

	if searchPresent {
		query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
		queryParams = append(queryParams, search)
	}

	query += ` ORDER BY t.expiration_date ASC`

	rows, err = i.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query tracked inventory for expiry")
	}

	for rows.Next() {
		var expObject model.ExpirationObject

		err = rows.Scan(
			&expObject.ExpirationDate,
			&expObject.Quantity,
			&expObject.Unit,
			&expObject.AvailableQty,
			&expObject.PickableQty,
			&expObject.BatchNumber,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
		}

		expDates = append(expDates, &expObject)
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows")
	}

	trackedInventory.ExpirationDates = expDates

	return &trackedInventory, nil
}

// # Get Serial And Perishable Inventory
func (i *InventoryImpl) GetSerialAndPerishableInventory(datePresent, searchPresent bool, itemID, search string, pickableQty int, startDate, endDate time.Time) (*model.TrackedObject, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query string

	var paramIndex int
	var queryParams []interface{}

	var trackedInventory model.TrackedObject

	var serialNumbers []*model.SerialObject

	query = `SELECT t.serial_number, SUM(t.quantity), i.base_unit AS unit, SUM(t.available_qty), SUM(t.pickable_qty) FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.quantity > 0 AND t.pickable_qty > $2`

	paramIndex = 3
	queryParams = append(queryParams, itemID, pickableQty)

	if datePresent {
		query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
		queryParams = append(queryParams, startDate, endDate)
		paramIndex += 2
	}

	if searchPresent {
		query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
		queryParams = append(queryParams, search)
	}

	query += ` GROUP BY t.serial_number ORDER BY t.serial_number DESC`

	rows, err = i.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query tracked inventory")
	}

	defer rows.Close()

	for rows.Next() {
		var serialObject model.SerialObject

		err = rows.Scan(
			&serialObject.SerialNumber,
			&serialObject.Quantity,
			&serialObject.Unit,
			&serialObject.AvailableQty,
			&serialObject.PickableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
		}

		serialNumbers = append(serialNumbers, &serialObject)
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows")
	}

	for _, serial := range serialNumbers {
		var expDates []*model.ExpirationObject

		query = `SELECT t.expiration_date, t.quantity, i.base_unit AS unit, t.available_qty, t.pickable_qty, t.serial_number FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.serial_number = $2 AND t.quantity > 0 AND t.pickable_qty > $3`

		paramIndex = 4
		queryParams = queryParams[:0]
		queryParams = append(queryParams, itemID, serial.SerialNumber, pickableQty)

		if datePresent {
			query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
			queryParams = append(queryParams, startDate, endDate)
			paramIndex += 2
		}

		if searchPresent {
			query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
			queryParams = append(queryParams, search)
		}

		query += ` ORDER BY t.expiration_date ASC`

		rows, err := i.DB.Query(ctx, query, queryParams...)
		if err != nil {
			return nil, errors.Wrap(err, "failed to query tracked inventory")
		}

		for rows.Next() {
			var expObject model.ExpirationObject

			err = rows.Scan(
				&expObject.ExpirationDate,
				&expObject.Quantity,
				&expObject.Unit,
				&expObject.AvailableQty,
				&expObject.PickableQty,
				&expObject.SerialNumber,
			)
			if err != nil {
				return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
			}

			expDates = append(expDates, &expObject)
		}

		err = rows.Err()
		if err != nil {
			return nil, errors.Wrap(err, "unable to read rows")
		}

		serial.ExpirationDates = expDates
	}

	trackedInventory.SerialNumbers = serialNumbers

	var expDates []*model.ExpirationObject

	query = `SELECT t.expiration_date, t.quantity, i.base_unit AS unit, t.available_qty, t.pickable_qty, t.serial_number FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.quantity > 0 AND t.pickable_qty > $2`

	paramIndex = 3
	queryParams = queryParams[:0]
	queryParams = append(queryParams, itemID, pickableQty)

	if datePresent {
		query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
		queryParams = append(queryParams, startDate, endDate)
		paramIndex += 2
	}

	if searchPresent {
		query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
		queryParams = append(queryParams, search)
	}

	query += ` ORDER BY t.expiration_date ASC`

	rows, err = i.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query tracked inventory")
	}

	for rows.Next() {
		var expObject model.ExpirationObject

		err = rows.Scan(
			&expObject.ExpirationDate,
			&expObject.Quantity,
			&expObject.Unit,
			&expObject.AvailableQty,
			&expObject.PickableQty,
			&expObject.SerialNumber,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
		}

		expDates = append(expDates, &expObject)
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows")
	}

	trackedInventory.ExpirationDates = expDates

	return &trackedInventory, nil
}

// # Get Batch, Serial And Perishable Inventory
func (i *InventoryImpl) GetBatchSerialAndPerishableInventory(datePresent, searchPresent bool, itemID, search string, pickableQty int, startDate, endDate time.Time) (*model.TrackedObject, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query string

	var paramIndex int
	var queryParams []interface{}

	var trackedInventory model.TrackedObject

	var batchNumbers []*model.BatchObject

	query = `SELECT t.batch_number, SUM(t.quantity), i.base_unit AS unit, SUM(t.available_qty), SUM(t.pickable_qty) FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.quantity > 0 AND t.pickable_qty > $2`

	paramIndex = 3
	queryParams = append(queryParams, itemID, pickableQty)

	if datePresent {
		query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
		queryParams = append(queryParams, startDate, endDate)
		paramIndex += 2
	}

	if searchPresent {
		query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
		queryParams = append(queryParams, search)
	}

	query += ` GROUP BY t.batch_number, i.base_unit ORDER BY t.batch_number DESC`

	rows, err = i.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query tracked inventory")
	}

	for rows.Next() {
		var batchObject model.BatchObject

		err = rows.Scan(
			&batchObject.BatchNumber,
			&batchObject.Quantity,
			&batchObject.Unit,
			&batchObject.AvailableQty,
			&batchObject.PickableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
		}

		batchNumbers = append(batchNumbers, &batchObject)
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows")
	}

	for _, batch := range batchNumbers {
		var serialNumbers []*model.SerialObject

		query = `SELECT t.serial_number, SUM(t.quantity), i.base_unit AS unit, SUM(t.available_qty), SUM(t.pickable_qty), t.batch_number FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.batch_number = $2 AND t.quantity > 0 AND t.pickable_qty > $3`

		paramIndex = 4
		queryParams = queryParams[:0]
		queryParams = append(queryParams, itemID, batch.BatchNumber, pickableQty)

		if datePresent {
			query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
			queryParams = append(queryParams, startDate, endDate)
			paramIndex += 2
		}

		if searchPresent {
			query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
			queryParams = append(queryParams, search)
		}

		query += ` GROUP BY t.serial_number, t.batch_number, i.base_unit ORDER BY t.serial_number DESC`

		rows, err := i.DB.Query(ctx, query, queryParams...)
		if err != nil {
			return nil, errors.Wrap(err, "failed to query tracked inventory")
		}

		for rows.Next() {
			var serialObject model.SerialObject

			err = rows.Scan(
				&serialObject.SerialNumber,
				&serialObject.Quantity,
				&serialObject.Unit,
				&serialObject.AvailableQty,
				&serialObject.PickableQty,
				&serialObject.BatchNumber,
			)
			if err != nil {
				return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
			}

			serialNumbers = append(serialNumbers, &serialObject)
		}

		err = rows.Err()
		if err != nil {
			return nil, errors.Wrap(err, "unable to read rows")
		}

		for _, serial := range serialNumbers {
			var expDates []*model.ExpirationObject

			query = `SELECT t.expiration_date, t.quantity, i.base_unit AS unit, t.available_qty, t.pickable_qty, t.serial_number, t.batch_number FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.batch_number = $2 AND t.serial_number = $3 AND t.quantity > 0 AND t.pickable_qty > $4`

			paramIndex = 5
			queryParams = queryParams[:0]
			queryParams = append(queryParams, itemID, batch.BatchNumber, serial.SerialNumber, pickableQty)

			if datePresent {
				query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
				queryParams = append(queryParams, startDate, endDate)
				paramIndex += 2
			}

			if searchPresent {
				query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
				queryParams = append(queryParams, search)
			}

			query += ` ORDER BY t.expiration_date ASC`

			rows, err = i.DB.Query(ctx, query, queryParams...)
			if err != nil {
				return nil, errors.Wrap(err, "failed to query tracked inventory")
			}

			for rows.Next() {
				var expObject model.ExpirationObject

				err = rows.Scan(
					&expObject.ExpirationDate,
					&expObject.Quantity,
					&expObject.Unit,
					&expObject.AvailableQty,
					&expObject.PickableQty,
					&expObject.SerialNumber,
					&expObject.BatchNumber,
				)
				if err != nil {
					return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
				}

				expDates = append(expDates, &expObject)
			}

			err = rows.Err()
			if err != nil {
				return nil, errors.Wrap(err, "unable to read rows")
			}

			serial.ExpirationDates = expDates
		}

		batch.SerialNumbers = serialNumbers
		trackedInventory.SerialNumbers = append(trackedInventory.SerialNumbers, serialNumbers...)
	}

	trackedInventory.BatchNumbers = batchNumbers

	var expDates []*model.ExpirationObject

	query = `SELECT t.expiration_date, t.quantity, i.base_unit AS unit, t.available_qty, t.pickable_qty, t.serial_number, t.batch_number FROM tracked_inventory t INNER JOIN inventory i ON t.item_id = i.item_id WHERE t.item_id = $1 AND t.quantity > 0 AND t.pickable_qty > $2`

	paramIndex = 3
	queryParams = queryParams[:0]
	queryParams = append(queryParams, itemID, pickableQty)

	if datePresent {
		query += fmt.Sprintf(` AND t.expiration_date BETWEEN $%d AND $%d`, paramIndex, paramIndex+1)
		queryParams = append(queryParams, startDate, endDate)
		paramIndex += 2
	}

	if searchPresent {
		query += fmt.Sprintf(` AND (t.batch_number ILIKE $%d OR t.serial_number ILIKE $%d)`, paramIndex, paramIndex)
		queryParams = append(queryParams, search)
	}

	query += ` ORDER BY t.expiration_date ASC`

	rows, err = i.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query tracked inventory")
	}

	for rows.Next() {
		var expObject model.ExpirationObject

		err = rows.Scan(
			&expObject.ExpirationDate,
			&expObject.Quantity,
			&expObject.Unit,
			&expObject.AvailableQty,
			&expObject.PickableQty,
			&expObject.SerialNumber,
			&expObject.BatchNumber,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into batch object schema")
		}

		expDates = append(expDates, &expObject)
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows")
	}

	trackedInventory.ExpirationDates = expDates

	return &trackedInventory, nil
}

// # Get Default Inventory
func (i *InventoryImpl) GetDefaultInventory() (*model.TrackedObject, error) {
	var trackedInventory model.TrackedObject
	return &trackedInventory, nil
}

// # Get Item Tracked Inventory
func (i *InventoryImpl) GetItemTrackedInventory(warehouseID, clientID, itemID, search, startDate, endDate string, pickableNotZero bool) (*model.TrackedObject, error) {
	var err error

	item, err := i.GetItemAndValidateClient(clientID, itemID)
	if err != nil {
		return nil, err
	}

	pickableQty := -1
	if pickableNotZero {
		pickableQty = 0
	}

	var sd, ed time.Time

	var searchPresent, datePresent bool = false, false

	// # Expiration Date Filter
	if startDate != "" && endDate != "" {
		sd, err = time.Parse("2006-01-02 15:04:05", startDate)
		if err != nil {
			err = errors.Wrap(err, "Invalid start date!")
			return nil, err
		}

		ed, err = time.Parse("2006-01-02 15:04:05", endDate)
		if err != nil {
			err = errors.Wrap(err, "Invalid end date!")
			return nil, err
		}

		datePresent = true
	}

	// # Wildcard Search Query
	if search != "" {
		// # Search Filter: Search by 'batch_number' (lot#) and 'serial_number' (serial#)
		search = "%" + search + "%"
		searchPresent = true
	}

	// # All '7 Cases' of Tracked Inventory
	isBatchOnly := item.IsBatchControlled && !item.IsTrackedBySerialNo && !item.IsPerishable
	isSerialOnly := !item.IsBatchControlled && item.IsTrackedBySerialNo && !item.IsPerishable
	isPerishableOnly := !item.IsBatchControlled && !item.IsTrackedBySerialNo && item.IsPerishable
	isBatchAndSerial := item.IsBatchControlled && item.IsTrackedBySerialNo && !item.IsPerishable
	isBatchAndPerishable := item.IsBatchControlled && !item.IsTrackedBySerialNo && item.IsPerishable
	isSerialAndPerishable := !item.IsBatchControlled && item.IsTrackedBySerialNo && item.IsPerishable
	isBatchSerialAndPerishable := item.IsBatchControlled && item.IsTrackedBySerialNo && item.IsPerishable

	switch {
	case isBatchOnly:
		return i.GetBatchInventory(datePresent, searchPresent, itemID, search, pickableQty, sd, ed)
	case isSerialOnly:
		return i.GetSerialInventory(datePresent, searchPresent, itemID, search, pickableQty, sd, ed)
	case isPerishableOnly:
		return i.GetPerishableInventory(datePresent, searchPresent, itemID, search, pickableQty, sd, ed)
	case isBatchAndSerial:
		return i.GetBatchAndSerialInventory(datePresent, searchPresent, itemID, search, pickableQty, sd, ed)
	case isBatchAndPerishable:
		return i.GetBatchAndPerishableInventory(datePresent, searchPresent, itemID, search, pickableQty, sd, ed)
	case isSerialAndPerishable:
		return i.GetSerialAndPerishableInventory(datePresent, searchPresent, itemID, search, pickableQty, sd, ed)
	case isBatchSerialAndPerishable:
		return i.GetBatchSerialAndPerishableInventory(datePresent, searchPresent, itemID, search, pickableQty, sd, ed)
	default:
		return i.GetDefaultInventory()
	}
}

func (i *InventoryImpl) SearchInventory(warehouseID, clientID, searchQuery string, pageSize int) ([]model.InventorySearch, error) {
	var err error
	var rows pgx.Rows
	var clientId []string
	if clientID != "" {
		clientId = append(clientId, clientID)
	}

	req := inbound_proto.AggregateARNItemsReportRequest{
		WarehouseId: warehouseID,
		Clients:     clientId,
	}

	// # Call 'Get Aggregate ARN Items Report' method
	report, err := i.AggregateARNItemsReport(&req)
	if err != nil {
		err = errors.Wrap(err, "failed to get item wise arn data")
		return nil, err
	}

	item_map := make(map[string]int32)

	itemReport := report.GetReport()

	for _, item := range itemReport {
		itemID := item.GetItemId()
		itemQty := item.GetQuantity()
		item_map[itemID] = itemQty
	}

	items := []model.InventorySearch{}
	searchQuery = "%" + searchQuery + "%"
	if clientID != "" {
		query := `SELECT item_id, quantity, available_qty, pickable_qty, sku, scannable, on_hold, name, description, client_name, client_id, base_unit, is_kit FROM inventory WHERE (sku ILIKE $1 OR name ILIKE $1 OR scannable ILIKE $1) AND warehouse_id = $2 AND client_id = $3 LIMIT $4`
		rows, err = i.DB.Query(context.TODO(), query, searchQuery, warehouseID, clientID, pageSize)
	} else {
		query := `SELECT item_id, quantity, available_qty, pickable_qty, sku, scannable, on_hold, name, description, client_name, client_id, base_unit, is_kit FROM inventory WHERE (sku ILIKE $1 OR name ILIKE $1 OR scannable ILIKE $1) AND warehouse_id = $2 LIMIT $3`
		rows, err = i.DB.Query(context.TODO(), query, searchQuery, warehouseID, pageSize)
	}
	if err == pgx.ErrNoRows {
		return items, nil
	}
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for inventory")
	}

	for rows.Next() {
		var item model.InventorySearch
		err := rows.Scan(
			&item.ItemID,
			&item.Quantity,
			&item.AvailableQty,
			&item.PickableQty,
			&item.SKU,
			&item.Scannable,
			&item.OnHold,
			&item.Name,
			&item.Description,
			&item.ClientName,
			&item.ClientID,
			&item.BaseUnit,
			&item.IsKit,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into item schema")
		}
		item.InboundQty = item_map[item.ItemID]
		items = append(items, item)
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	return items, nil
}

func (i *InventoryImpl) GetConsolidatedInventory(warehouseData []auth.ServiceData, pageNo, pageSize int, searchQuery string) (*model.ConsolidatedInventoryResponse, error) {
	// Handle pagination
	if pageNo < 1 || pageSize < 1 {
		pageNo = 1
		pageSize = 10
	}
	offset := int64((pageNo - 1) * pageSize)

	// Init response
	consolidatedInventoryList := []model.ConsolidatedInventory{}

	// {warehouse_id: warehouse_name}
	whid_name_map := make(map[string]string)
	var warehouses []string
	for _, warehouse := range warehouseData {
		warehouses = append(warehouses, warehouse.WarehouseID.Hex())
		whid_name_map[warehouse.WarehouseID.Hex()] = warehouse.WarehouseName
	}

	var query string
	var err error
	var rows pgx.Rows

	if len(searchQuery) > 3 {
		searchQuery = "%" + searchQuery + "%"

		// Get consolidated warehouse inventory
		query = `SELECT client_name, sku, base_unit, SUM(quantity) AS quantity, SUM(available_qty) AS available_qty, SUM(pickable_qty) AS pickable_qty, MAX(name) AS name, MAX(description) AS description, MAX(image) as image FROM inventory WHERE warehouse_id = ANY($1) AND (sku ILIKE $2 OR name ILIKE $2 OR client_name ILIKE $2) GROUP BY sku, base_unit, client_name LIMIT $3 OFFSET $4`
		rows, err = i.DB.Query(context.TODO(), query, warehouses, searchQuery, pageSize, offset)
	} else {
		// Get consolidated warehouse inventory
		query = `SELECT client_name, sku, base_unit, SUM(quantity) AS quantity, SUM(available_qty) AS available_qty, SUM(pickable_qty) AS pickable_qty, MAX(name) AS name, MAX(description) AS description, MAX(image) as image FROM inventory WHERE warehouse_id = ANY($1) GROUP BY sku, base_unit, client_name LIMIT $2 OFFSET $3`
		rows, err = i.DB.Query(context.TODO(), query, warehouses, pageSize, offset)
	}

	if err != nil {
		return nil, errors.Wrap(err, "failed to query for inventory")
	}
	defer rows.Close()

	// Inventory items

	for rows.Next() {
		var consolidatedInventory model.ConsolidatedInventory
		err := rows.Scan(
			&consolidatedInventory.ClientName,
			&consolidatedInventory.SKU,
			&consolidatedInventory.BaseUnit,
			&consolidatedInventory.Quantity,
			&consolidatedInventory.AvailableQty,
			&consolidatedInventory.PickableQty,
			&consolidatedInventory.Name,
			&consolidatedInventory.Description,
			&consolidatedInventory.Image,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into consolidated inventory item schema")
		}
		consolidatedInventoryList = append(consolidatedInventoryList, consolidatedInventory)
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	var resultCount, totalPages int
	query = `SELECT COUNT(1) FROM (SELECT COUNT(1) FROM inventory WHERE warehouse_id = ANY($1) GROUP BY sku, base_unit, client_name) AS count`
	err = i.DB.QueryRow(context.TODO(), query, warehouses).Scan(&resultCount)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get count of transaction rows")
	}
	if pageSize > 1 {
		if (resultCount % pageSize) == 0 {
			totalPages = resultCount / pageSize
		} else {
			totalPages = resultCount/pageSize + 1
		}
	} else {
		totalPages = 1
	}

	res := &model.ConsolidatedInventoryResponse{
		CurrentPage:           pageNo,
		TotalDocuments:        resultCount,
		TotalPages:            totalPages,
		ConsolidatedInventory: consolidatedInventoryList,
	}

	return res, nil
}

func (i *InventoryImpl) GetOrgInventory(warehouseData []auth.ServiceData, sku string) ([]model.Inventory, error) {
	var items []model.Inventory
	// {warehouse_id: warehouse_name}
	whid_name_map := make(map[string]string)
	var warehouses []string
	for _, warehouse := range warehouseData {
		warehouses = append(warehouses, warehouse.WarehouseID.Hex())
		whid_name_map[warehouse.WarehouseID.Hex()] = warehouse.WarehouseName
	}

	// Get Individual warehouse inventory
	query := `SELECT * FROM inventory WHERE warehouse_id = ANY($1) AND sku = $2`
	rows, err := i.DB.Query(context.TODO(), query, warehouses, sku)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for inventory")
	}
	defer rows.Close()
	for rows.Next() {
		var item model.Inventory
		err := rows.Scan(
			&item.ID,
			&item.ItemID,
			&item.ClientID,
			&item.WarehouseID,
			&item.Quantity,
			&item.AvailableQty,
			&item.PickableQty,
			&item.BaseUnit,
			&item.SKU,
			&item.Scannable,
			&item.CreatedAt,
			&item.UpdatedAt,
			&item.IsDeleted,
			&item.OnHold,
			&item.IsBatchControlled,
			&item.IsTrackedBySerialNo,
			&item.Name,
			&item.Description,
			&item.OnHoldBy,
			&item.IsPerishable,
			&item.TrackedBy,
			&item.Image,
			&item.BackOrders,
			&item.ClientName,
			&item.IsClientDeleted,
			&item.IsKit,
			&item.DynamicColumn,

			// # LeanShip #
			&item.IsLeanShip,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into item schema")
		}

		item.WarehouseName = whid_name_map[item.WarehouseID]

		items = append(items, item)
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	return items, nil
}

func (i *InventoryImpl) GetAreaDetails(areaID string) (*schema.AreaDetails, error) {
	// Get count of distinct locations on area locations
	var numLocations int
	query := `SELECT COUNT(DISTINCT location_id) FROM location_wise_inventory WHERE area_id = $1`
	err := i.DB.QueryRow(context.TODO(), query, areaID).Scan(&numLocations)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get count of distinct locations")
	}

	// Get count of distinct items on area
	var numItems int
	query = `SELECT COUNT(DISTINCT item_id) FROM location_wise_inventory WHERE area_id = $1`
	err = i.DB.QueryRow(context.TODO(), query, areaID).Scan(&numItems)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get count of distinct items on locations")
	}

	return &schema.AreaDetails{NumLocations: numLocations, NumItems: numItems}, nil
}

// # Get All Warehouse Items
func (i *InventoryImpl) GetAllWarehouseItems(warehouseID, search string) (*model.ItemResponse, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query string

	var queryParams []interface{}

	var searchPresent bool = false

	// # Wildcard Search Query
	if search != "" {
		// # Search Filter: Search by 'item_name' (name), 'client_name', 'sku', 'scannable', and 'base_unit'
		search = "%" + search + "%"
		searchPresent = true
	}

	// # Query Parameters
	queryParams = append(queryParams, warehouseID)

	// # Base Query
	query = `SELECT DISTINCT ON (item_id)
				item_id,
				client_id,
				warehouse_id,
				"name" AS item_name,
				client_name,
				description,
				image,
				quantity AS total_qty,
				available_qty,
				pickable_qty,
				sku,
				scannable,
				base_unit,
				on_hold
			FROM
				inventory
			WHERE
				warehouse_id = $1
			ORDER BY
				item_id DESC`

	// # Search Filter
	if searchPresent {
		query = `SELECT DISTINCT ON (item_id)
					item_id,
					client_id,
					warehouse_id,
					"name" AS item_name,
					client_name,
					description,
					image,
					quantity AS total_qty,
					available_qty,
					pickable_qty,
					sku,
					scannable,
					base_unit,
					on_hold
				FROM
					inventory
				WHERE
					warehouse_id = $1
					AND (
						"name" ILIKE $2
						OR client_name ILIKE $2
						OR sku ILIKE $2
						OR scannable ILIKE $2
						OR base_unit ILIKE $2
					)
				ORDER BY
					item_id DESC`

		queryParams = append(queryParams, search)
	}

	// # Execute Query
	rows, err = i.DB.Query(ctx, query, queryParams...)
	if err != nil {
		err = errors.Wrap(err, "failed to query inventory for warehouse items")
		return nil, err
	}

	defer rows.Close()

	var warehouseItems model.ItemResponse
	var items []*model.ItemObject

	for rows.Next() {
		var item model.ItemObject
		var quanityInfo model.QuantityInfo

		err = rows.Scan(
			&item.ItemID,
			&item.ClientID,
			&item.WarehouseID,
			&item.ItemName,
			&item.ClientName,
			&item.Description,
			&item.Image,
			&quanityInfo.TotalQty,
			&quanityInfo.AvailableQty,
			&quanityInfo.PickableQty,
			&item.SKU,
			&item.Scannable,
			&item.BaseUnit,
			&item.OnHold,
		)
		if err != nil {
			err = errors.Wrap(err, "unable to read resultant rows into item object schema")
			return nil, err
		}

		item.QuantityInfo = &quanityInfo

		items = append(items, &item)
	}

	err = rows.Err()
	if err != nil {
		err = errors.Wrap(err, "unable to read rows for warehouse items")
		return nil, err
	}

	warehouseItems = model.ItemResponse{
		Items: items,
	}

	// # Return Warehouse Items
	return &warehouseItems, nil
}

func (i *InventoryImpl) GetLocationBySearch(warehouseID, search, searchModifier string) ([]model.LocationLayoutSearchResponse, error) {
	var locationSearchResponse []model.LocationLayoutSearchResponse

	//Fetch all container Types for the warehouse
	queryCT := `SELECT id, name FROM container_type WHERE warehouse_id = $1`
	rowsCT, err := i.DB.Query(context.TODO(), queryCT, warehouseID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query container types")
	}
	defer rowsCT.Close()
	containerTypes := make(map[int64]string)
	for rowsCT.Next() {
		var containerTypeID int64
		var name string
		err := rowsCT.Scan(&containerTypeID, &name)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into container type schema")
		}
		containerTypes[containerTypeID] = name
	}

	// fetch all areas of warehouse from core service
	response, err := i.App.GrpcClient.Core.Client.GetAllAreas(context.TODO(), &core_proto.GetAllAreasRequest{WarehouseId: warehouseID})
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get all areas for warehouse")
	}
	areaMap := make(map[string][]string)
	for _, area := range response.Areas {
		areaMap[area.GetAreaId()] = area.GetProperties()
	}

	if searchModifier == "location" {
		// Fetch location details based on search
		var locationID, code, areaID, areaName string
		var onHold, isPrime bool
		query := `SELECT DiSTINCT ON (location_id) location_id, code, area_id, area_name, on_hold, is_prime FROM location_wise_inventory WHERE warehouse_id = $1 AND code = $2`
		err := i.DB.QueryRow(context.TODO(), query, warehouseID, search).Scan(&locationID, &code, &areaID, &areaName, &onHold, &isPrime)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to query location wise inventory")
		}
		locationSearchResponse = append(locationSearchResponse, model.LocationLayoutSearchResponse{
			WarehouseID:  &warehouseID,
			LocationID:   &locationID,
			LocationCode: &code,
			AreaID:       &areaID,
			AreaName:     &areaName,
			OnHold:       &onHold,
			IsPrime:      &isPrime,
			Properties:   areaMap[areaID],
		})

		return locationSearchResponse, nil

	} else if searchModifier == "container" {
		// Fetch container details based on search
		var containerID uuid.UUID
		var containerCode, containerRefNo, locationID, locationCode, areaID, areaName, clientID, clientName *string
		var containerTypeID int64
		var onHold, isPrime bool
		query := `SELECT DISTINCT ON (c.container_id) c.container_id, c.code, c.container_type_id, c.ref_no, c.client_id, c.client_name, c.location_id, l.code, l.area_id, l.area_name, l.on_hold, l.is_prime FROM container_wise_inventory c INNER JOIN location_wise_inventory l ON c.location_id = l.location_id WHERE c.warehouse_id = $1 AND c.code = $2`
		err := i.DB.QueryRow(context.TODO(), query, warehouseID, search).Scan(&containerID, &containerCode, &containerTypeID, &containerRefNo, &clientID, &clientName, &locationID, &locationCode, &areaID, &areaName, &onHold, &isPrime)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to query containers")
		}
		containerType := containerTypes[containerTypeID]
		if containerType == "loose" {
			return nil, nil
		}
		locationSearchResponse = append(locationSearchResponse, model.LocationLayoutSearchResponse{
			WarehouseID:    &warehouseID,
			ClientID:       clientID,
			ClientName:     clientName,
			ContainerID:    &containerID,
			ContainerCode:  containerCode,
			ContainerType:  &containerType,
			ContainerRefNo: containerRefNo,
			LocationID:     locationID,
			LocationCode:   locationCode,
			AreaID:         areaID,
			AreaName:       areaName,
			OnHold:         &onHold,
			IsPrime:        &isPrime,
			Properties:     areaMap[*areaID],
		})

		return locationSearchResponse, nil

	} else if searchModifier == "item" {
		// Fetch container wise inventory based on item search
		query := `SELECT c.warehouse_id, c.container_id, c.container_type_id, c.code AS container_code, c.client_id, c.client_name, c.location_id, l.code AS location_code, l.area_name, l.area_id, l.on_hold, l.is_prime, c.item_id, i.image, i.is_kit, i.sku, i.name, i.scannable, c.quantity, c.available_qty, c.batch_number, c.serial_number, c.expiration_date, i.base_unit, i.on_hold AS item_on_hold 
				 FROM container_wise_inventory c 
				 LEFT JOIN (
  							SELECT DISTINCT ON (location_id) *
  							FROM location_wise_inventory
  							ORDER BY location_id
				 ) l ON c.location_id = l.location_id 
				 LEFT JOIN inventory i ON c.item_id = i.item_id 
				 WHERE c.warehouse_id = $1 AND c.item_id = $2`
		rows, err := i.DB.Query(context.TODO(), query, warehouseID, search)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to query container wise inventory")
		}
		defer rows.Close()
		locationSearchMap := make(map[string]*model.LocationLayoutSearchResponse)
		for rows.Next() {
			var data model.LocationLayoutSearchData
			err := rows.Scan(
				&data.WarehouseID,
				&data.ContainerID,
				&data.ContainerTypeID,
				&data.ContainerCode,
				&data.ClientID,
				&data.ClientName,
				&data.LocationID,
				&data.LocationCode,
				&data.AreaName,
				&data.AreaID,
				&data.OnHold,
				&data.IsPrime,
				&data.ItemID,
				&data.Image,
				&data.IsKit,
				&data.SKU,
				&data.Name,
				&data.Scannable,
				&data.Quantity,
				&data.AvailableQty,
				&data.BatchNumber,
				&data.SerialNumber,
				&data.ExpirationDate,
				&data.BaseUnit,
				&data.ItemOnHold,
			)
			if err != nil {
				return nil, errors.Wrap(err, "unable to read resultant rows into location layout search data schema")
			}
			containerType := containerTypes[*data.ContainerTypeID]
			if location, exists := locationSearchMap[*data.LocationID]; !exists {
				// If location is not already present, create a new entry
				ContainerItems := model.LocationLayoutSearchResponseItems{
					ClientID:       data.ClientID,
					ClientName:     data.ClientName,
					ContainerID:    data.ContainerID,
					ContainerCode:  data.ContainerCode,
					ContainerType:  &containerType,
					ItemID:         data.ItemID,
					Image:          data.Image,
					IsKit:          data.IsKit,
					SKU:            data.SKU,
					Name:           data.Name,
					Scannable:      data.Scannable,
					Quantity:       data.Quantity,
					AvailableQty:   data.AvailableQty,
					BatchNumber:    data.BatchNumber,
					SerialNumber:   data.SerialNumber,
					ExpirationDate: data.ExpirationDate,
					BaseUnit:       data.BaseUnit,
					OnHold:         data.ItemOnHold,
				}
				locationSearchMap[*data.LocationID] = &model.LocationLayoutSearchResponse{
					WarehouseID:  data.WarehouseID,
					LocationID:   data.LocationID,
					LocationCode: data.LocationCode,
					AreaID:       data.AreaID,
					AreaName:     data.AreaName,
					OnHold:       data.OnHold,
					IsPrime:      data.IsPrime,
					Properties:   areaMap[*data.AreaID],
					Items:        []model.LocationLayoutSearchResponseItems{ContainerItems},
				}
			} else {
				// If location already exists, append the item to the existing entry
				ContainerItems := model.LocationLayoutSearchResponseItems{
					ClientID:       data.ClientID,
					ClientName:     data.ClientName,
					ContainerID:    data.ContainerID,
					ContainerCode:  data.ContainerCode,
					ContainerType:  &containerType,
					ItemID:         data.ItemID,
					Image:          data.Image,
					IsKit:          data.IsKit,
					SKU:            data.SKU,
					Name:           data.Name,
					Scannable:      data.Scannable,
					Quantity:       data.Quantity,
					AvailableQty:   data.AvailableQty,
					BatchNumber:    data.BatchNumber,
					SerialNumber:   data.SerialNumber,
					ExpirationDate: data.ExpirationDate,
					BaseUnit:       data.BaseUnit,
					OnHold:         data.ItemOnHold,
				}
				location.Items = append(location.Items, ContainerItems)
			}
		}
		if err := rows.Err(); err != nil {
			return nil, errors.Wrap(err, "unable to read rows for container wise inventory")
		}
		// Convert map to slice
		for _, location := range locationSearchMap {
			locationSearchResponse = append(locationSearchResponse, *location)
		}

		return locationSearchResponse, nil

	} else if searchModifier == "client" {
		// Fetch container wise inventory based on client search
		query := `SELECT c.warehouse_id, c.container_id, c.container_type_id, c.code AS container_code, c.client_id, c.client_name, c.location_id, l.code AS location_code, l.area_name, l.area_id, l.on_hold, l.is_prime, c.item_id, i.image, i.is_kit, i.sku, i.name, i.scannable, c.quantity, c.available_qty, c.batch_number, c.serial_number, c.expiration_date, i.base_unit, i.on_hold AS item_on_hold 
				FROM container_wise_inventory c 
				LEFT JOIN (
  						SELECT DISTINCT ON (location_id) *
  						FROM location_wise_inventory
  						ORDER BY location_id
				) l ON c.location_id = l.location_id 
				LEFT JOIN inventory i ON c.item_id = i.item_id 
				WHERE c.warehouse_id = $1 AND c.client_id = $2`
		rows, err := i.DB.Query(context.TODO(), query, warehouseID, search)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to query container wise inventory")
		}
		defer rows.Close()
		locationSearchMap := make(map[string]*model.LocationLayoutSearchResponse)
		for rows.Next() {
			var data model.LocationLayoutSearchData
			err := rows.Scan(
				&data.WarehouseID,
				&data.ContainerID,
				&data.ContainerTypeID,
				&data.ContainerCode,
				&data.ClientID,
				&data.ClientName,
				&data.LocationID,
				&data.LocationCode,
				&data.AreaName,
				&data.AreaID,
				&data.OnHold,
				&data.IsPrime,
				&data.ItemID,
				&data.Image,
				&data.IsKit,
				&data.SKU,
				&data.Name,
				&data.Scannable,
				&data.Quantity,
				&data.AvailableQty,
				&data.BatchNumber,
				&data.SerialNumber,
				&data.ExpirationDate,
				&data.BaseUnit,
				&data.ItemOnHold,
			)
			if err != nil {
				return nil, errors.Wrap(err, "unable to read resultant rows into location layout search data schema")
			}
			containerType := containerTypes[*data.ContainerTypeID]
			if location, exists := locationSearchMap[*data.LocationID]; !exists {
				// If location is not already present, create a new entry
				ContainerItems := model.LocationLayoutSearchResponseItems{
					ContainerID:    data.ContainerID,
					ContainerCode:  data.ContainerCode,
					ContainerType:  &containerType,
					ItemID:         data.ItemID,
					Image:          data.Image,
					IsKit:          data.IsKit,
					SKU:            data.SKU,
					Name:           data.Name,
					Scannable:      data.Scannable,
					Quantity:       data.Quantity,
					AvailableQty:   data.AvailableQty,
					BatchNumber:    data.BatchNumber,
					SerialNumber:   data.SerialNumber,
					ExpirationDate: data.ExpirationDate,
					BaseUnit:       data.BaseUnit,
					OnHold:         data.ItemOnHold,
				}
				locationSearchMap[*data.LocationID] = &model.LocationLayoutSearchResponse{
					WarehouseID:  data.WarehouseID,
					ClientID:     data.ClientID,
					ClientName:   data.ClientName,
					LocationID:   data.LocationID,
					LocationCode: data.LocationCode,
					AreaID:       data.AreaID,
					AreaName:     data.AreaName,
					OnHold:       data.OnHold,
					IsPrime:      data.IsPrime,
					Properties:   areaMap[*data.AreaID],
					Items:        []model.LocationLayoutSearchResponseItems{ContainerItems},
				}
			} else {
				// If location already exists, append the item to the existing entry
				ContainerItems := model.LocationLayoutSearchResponseItems{
					ContainerID:    data.ContainerID,
					ContainerCode:  data.ContainerCode,
					ContainerType:  &containerType,
					ItemID:         data.ItemID,
					Image:          data.Image,
					IsKit:          data.IsKit,
					SKU:            data.SKU,
					Name:           data.Name,
					Scannable:      data.Scannable,
					Quantity:       data.Quantity,
					AvailableQty:   data.AvailableQty,
					BatchNumber:    data.BatchNumber,
					SerialNumber:   data.SerialNumber,
					ExpirationDate: data.ExpirationDate,
					BaseUnit:       data.BaseUnit,
					OnHold:         data.ItemOnHold,
				}
				location.Items = append(location.Items, ContainerItems)
			}
		}
		if err := rows.Err(); err != nil {
			return nil, errors.Wrap(err, "unable to read rows for container wise inventory")
		}
		// Convert map to slice
		for _, location := range locationSearchMap {
			locationSearchResponse = append(locationSearchResponse, *location)
		}

		return locationSearchResponse, nil
	}

	return locationSearchResponse, nil
}

// # LeanShip #
func (i *InventoryImpl) UpdateItemQuantity(data *schema.ValidateUpdateItemQuantity) (bool, error) {
	// Check if client is a leanship client
	res, err := i.App.GrpcClient.Core.Client.IsLeanshipClient(context.Background(), &core_proto.IsLeanshipClientRequest{ClientId: data.ClientID})
	if err != nil {
		return false, errors.Wrap(err, "unable to check if client is a leanship client")
	}
	if !res.GetIsLeanshipClient() {
		return false, errors.New("unable to update item quantity for non-leanship client")
	}

	ctx := context.Background()

	// Note: The quantity received in the request is only incremented or decremented;
	// If the initial quantity is 10 and the request quantity is 5, then the quantity will be updated to 15
	// If the initial quantity is 10 and the request quantity is -5, then the quantity will be updated to 5
	// If the initial quantity is 0 and the request quantity is 5, then the quantity will be updated to 5

	// Create transaction
	tx, err := i.DB.Begin(ctx)
	if err != nil {
		return false, errors.Wrap(err, "unable to begin transaction")
	}

	defer tx.Rollback(ctx)

	// Fetch location id using warehouse id
	var locationID, locationCode string
	query := `SELECT location_id, code FROM location_wise_inventory WHERE warehouse_id = $1`
	err = tx.QueryRow(ctx, query, data.WarehouseID).Scan(&locationID, &locationCode)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to fetch location id")
	}

	// Fetch container id using warehouse id, if not found create a new loose container
	var containerID uuid.UUID
	var containerCode, containerType string
	query = `SELECT container_id, code, name FROM container_wise_inventory c INNER JOIN container_type ct ON c.container_type_id = ct.id WHERE c.warehouse_id = $1`
	err = tx.QueryRow(ctx, query, data.WarehouseID).Scan(&containerID, &containerCode, &containerType)
	if err != nil {
		if err == pgx.ErrNoRows {
			// Get loose container type id
			type_id, err := i.App.Operations.GetContainerTypeID("loose", data.WarehouseID)
			if err != nil {
				tx.Rollback(ctx)
				return false, err
			}

			// Create an empty loose container on destination location
			code := "LP-0001"
			container_id := uuid.NewV4()
			containerID = container_id
			containerCode = code
			containerType = "loose"

			query = `INSERT INTO container_wise_inventory (item_id, sku, batch_number, serial_number, expiration_date, scannable, container_id, container_type_id, location_id, warehouse_id, code, quantity, available_qty, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)`
			commandTag, err := tx.Exec(ctx, query, nil, nil, nil, nil, nil, nil, container_id, type_id, locationID, data.WarehouseID, code, 0, 0, time.Now())
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "unable to insert new loose container")
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return false, errors.New("new lose container wasn't inserted")
			}

		} else {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "failed to get container details")
		}
	}

	// # Is Frontend Request: It indicates if the 'request' is from the 'frontend' or 'gRPC'
	isFrontendRequest := data.IsFrontendRequest

	// # Update all the '3 quantities' of the 'item' in the 'inventory'
	if isFrontendRequest {
		query = `
				UPDATE
					inventory
				SET
					quantity = $1,
					available_qty = $1,
					pickable_qty = $1
				WHERE
					warehouse_id = $2
					AND item_id = $3
				`
	} else {
		query = `
				UPDATE
					inventory
				SET
					quantity = quantity + $1,
					available_qty = available_qty + $1,
					pickable_qty = pickable_qty + $1
				WHERE
					warehouse_id = $2
					AND item_id = $3
				`
	}

	commandTag, err := tx.Exec(ctx, query, data.Quantity, data.WarehouseID, data.ItemID)
	if err != nil {
		tx.Rollback(ctx)

		// # Handle the 'constraint violation' error for the 'quantity' field
		var pgErr *pgconn.PgError
		ok := errors.As(err, &pgErr)
		if ok {
			if pgErr.Code == "23514" {
				err = errors.New("constraint violation: cannot reduce the quantity of an item below zero")
				return false, err
			}
		}

		err = errors.Wrap(err, "failed to update the inventory")
		return false, err
	}
	if commandTag.RowsAffected() != 1 {
		tx.Rollback(ctx)
		return false, errors.New("inventory wasn't updated")
	}

	// Fetch Inventory item by item id
	inventoryItem, err := i.App.Operations.GetItemFromInventoryByItemId(data.ItemID)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "Failed to get item from inventory")
	}

	// Check if item exists on location
	var itemExistsOnLocation bool
	query = `SELECT EXISTS(SELECT 1 FROM location_wise_inventory WHERE item_id = $1 AND warehouse_id = $2) AS "exists"`
	err = tx.QueryRow(ctx, query, data.ItemID, data.WarehouseID).Scan(&itemExistsOnLocation)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to check if item exists on location")
	}

	if itemExistsOnLocation {
		// # Update all the '2 quantities' of the 'item' in the 'location wise inventory' if the 'item' exists on the 'location'
		if isFrontendRequest {
			query = `
					UPDATE
						location_wise_inventory
					SET
						quantity = $1,
						available_qty = $1
					WHERE
						warehouse_id = $2
						AND item_id = $3
					`
		} else {
			query = `
					UPDATE
						location_wise_inventory
					SET
						quantity = quantity + $1,
						available_qty = available_qty + $1
					WHERE
						warehouse_id = $2
						AND item_id = $3
					`
		}

		commandTag, err := tx.Exec(ctx, query, data.Quantity, data.WarehouseID, data.ItemID)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to update location wise inventory")
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return false, errors.New("location wise inventory wasn't updated")
		}
	} else {
		// Item doesn't exist on the location, insert a new row
		// Insert item in location
		err := i.App.Operations.InsertItemInLocation(tx, ctx, locationID, data.WarehouseID, data.Quantity, data.Quantity, inventoryItem, nil, nil, nil)
		if err != nil {
			tx.Rollback(ctx)
			return false, err
		}
	}

	// Check if item exists on container
	var itemExistsOnContainer bool
	query = `SELECT EXISTS(SELECT 1 FROM container_wise_inventory WHERE item_id = $1 AND warehouse_id = $2) AS "exists"`
	err = tx.QueryRow(ctx, query, data.ItemID, data.WarehouseID).Scan(&itemExistsOnContainer)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to check if item exists on container")
	}

	if itemExistsOnContainer {
		// # Update all the '2 quantities' of the 'item' in the 'container wise inventory' if the 'item' exists on the 'container'
		if isFrontendRequest {
			query = `
					UPDATE
						container_wise_inventory
					SET
						quantity = $1,
						available_qty = $1
					WHERE
						warehouse_id = $2
						AND item_id = $3
					`
		} else {
			query = `
					UPDATE
						container_wise_inventory
					SET
						quantity = quantity + $1,
						available_qty = available_qty + $1
					WHERE
						warehouse_id = $2
						AND item_id = $3
					`
		}

		commandTag, err := tx.Exec(ctx, query, data.Quantity, data.WarehouseID, data.ItemID)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "unable to update container wise inventory")
		}
		if commandTag.RowsAffected() != 1 {
			tx.Rollback(ctx)
			return false, errors.New("container wise inventory wasn't updated")
		}
	} else {
		// Item doesn't exist on the container, insert a new row

		// Insert item in container
		err := i.App.Operations.InsertItemInContainer(tx, ctx, containerID, locationID, data.WarehouseID, data.Quantity, data.Quantity, inventoryItem, nil, nil, nil, nil)
		if err != nil {
			tx.Rollback(ctx)
			return false, err
		}
	}

	// Add to Transaction
	transactionData := &model.TransactionData{
		ClientID:       inventoryItem.ClientID,
		WarehouseID:    data.WarehouseID,
		RequestID:      data.RequestID,
		ItemID:         inventoryItem.ItemID,
		SKU:            inventoryItem.SKU,
		Action:         "audit_inventory",
		Change:         data.Quantity,
		Username:       data.Username,
		UserID:         data.UserID,
		ChangedAt:      time.Now().UTC(),
		BatchNumber:    nil,
		SerialNumber:   nil,
		ExpirationDate: nil,
		Source:         "self",
		Document:       "Audit",
		ClientName:     *inventoryItem.ClientName,
		Remarks:        []model.Remark{{Key: "audit_reason", Value: "Update Item Quantity"}},
		LocationID:     locationID,
		LocationCode:   locationCode,
		ContainerID:    &containerID,
		ContainerCode:  &containerCode,
		ContainerType:  containerType,
	}
	err = i.App.Operations.CreateTransaction(tx, transactionData)
	if err != nil {
		tx.Rollback(ctx)
		return false, err
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(tx_err, "Failed to commit transaction")
	}

	return true, nil
}
