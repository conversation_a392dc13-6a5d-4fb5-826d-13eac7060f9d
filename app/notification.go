package app

import (
	"encoding/json"
	"inventory/model"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/rs/zerolog"
	segKafka "github.com/segmentio/kafka-go"
	errors "github.com/vasupal1996/goerror"
)

// Notification defines methods of Notification service to be implemented
type Notification interface {
	TopicNotificationProducer(*model.Notification) error
	SubscriberNotificationProducer(*model.Notification) error
}

// NotificationOpts contains arguments to be accepted for new instance of Notification service
type NotificationOpts struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// NotificationImpl implements Notification service
type NotificationImpl struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// InitNotification returns initializes Notification service
func InitNotification(opts *NotificationOpts) Notification {
	e := &NotificationImpl{
		App:    opts.App,
		DB:     opts.DB,
		Logger: opts.Logger,
	}
	return e
}

func (n *NotificationImpl) TopicNotificationProducer(notification *model.Notification) error {

	// Encoding into json
	jsonString, err := json.Marshal(notification)
	if err != nil {
		return errors.Wrap(err, "Unable to encode notification structure.", &errors.NoType)
	}

	// Making kafka structure
	str := uuid.New().String()
	m := segKafka.Message{
		Key:   []byte(str),
		Value: jsonString,
	}

	// Producing kafka messages
	n.App.TopicNotificationProducer.Publish(m)

	return nil
}

func (n *NotificationImpl) SubscriberNotificationProducer(notification *model.Notification) error {

	// Encoding into json
	jsonString, err := json.Marshal(notification)
	if err != nil {
		return errors.Wrap(err, "Unable to encode notification structure.", &errors.NoType)
	}

	// Making kafka structure
	str := uuid.New().String()
	m := segKafka.Message{
		Key:   []byte(str),
		Value: jsonString,
	}

	// Producing kafka messages
	n.App.SubscriberNotificationProducer.Publish(m)

	return nil
}
