package app

import (
	"context"
	"fmt"
	"inventory/model"
	"inventory/schema"
	"strconv"
	"time"

	core_proto "proto/core"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/pkg/errors"
	"github.com/rs/zerolog"
)

// Location defines methods of Location service to be implemented
type Location interface {
	GetLocation(string) (*schema.LocationResponse, error)
	// # Get Aggregate Location
	GetAggregateLocation(string, string) (*schema.AggregateLocation, error)
	// # Get Item Locations All Containers
	GetItemLocationsAllContainers(string, string, string, time.Time, time.Time) (*schema.ItemLocations, error)
	GetContainersOnLocation(string, string) ([]schema.Container, error)
	GetItemLocations(*schema.ValidateGetItemLocations, bool, bool) ([]schema.Location, error)
	GetItemLocationsGroup(*schema.ValidateGetItemLocationsGroup, bool, bool) ([]schema.LocationGroup, error)
	HoldLocation(*schema.ValidateHoldLocation) (bool, error)
	SuggestEmptyLocations(*schema.ValidateSuggestLocation) ([]schema.Location, error)
	GetTrackedItemDetails(string, string) ([]schema.TrackedItemDetails, error)
	SearchLocations(string, string, []string) ([]model.SearchResponse, error)
	SuggestLocationsForWorkOrder(*schema.ValidateSuggestLocationForWorkOrder) (interface{}, error)
	DeleteLocations(*schema.ValidateDeleteLocations) ([]string, []string, error)
	GetAllWarehouseLocations(string, string) (*model.LocationResponse, error)
	GetLocationDetailsLayout(string, string, string) (*model.LocationDetailsLayout, error)
}

// LocationOpts contains arguments to be accepted for new instance of Location service
type LocationOpts struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// LocationImpl implements Location service
type LocationImpl struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// InitLocation returns initializes Location service
func InitLocation(opts *LocationOpts) Location {
	i := &LocationImpl{
		App:    opts.App,
		DB:     opts.DB,
		Logger: opts.Logger,
	}
	return i
}

func (l *LocationImpl) GetLocation(location_id string) (*schema.LocationResponse, error) {
	var location model.Location

	query := `SELECT * from location_wise_inventory WHERE location_id = $1 LIMIT 1`
	err := l.DB.QueryRow(context.TODO(), query, location_id).Scan(
		&location.ID,
		&location.ItemID,
		&location.WarehouseID,
		&location.ClientID,
		&location.LocationID,
		&location.Code,
		&location.Quantity,
		&location.AvailableQty,
		&location.AreaName,
		&location.AreaID,
		&location.OnHold,
		&location.OnHoldByID,
		&location.LastUpdatedAt,
		&location.LastUpdatedByID,
		&location.LastUpdatedByName,
		&location.SKU,
		&location.Scannable,
		&location.BatchNumber,
		&location.SerialNumber,
		&location.ExpirationDate,
		&location.CreatedAt,
		&location.ClientName,
		&location.IsPrime,
	)
	if err == pgx.ErrNoRows {
		return nil, errors.Wrap(err, "location doesn't exist")
	}
	if err != nil {
		return nil, errors.Wrap(err, "unable to read row")
	}

	// Query for location items
	query = `SELECT l.client_id, l.client_name, l.item_id, l.quantity, l.available_qty, l.sku, l.scannable, l.batch_number, l.serial_number, l.expiration_date, i.base_unit, i."name", i.description FROM location_wise_inventory l INNER JOIN inventory i ON l.item_id = i.item_id WHERE location_id = $1`
	rows, err := l.DB.Query(context.TODO(), query, location_id)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for locations")
	}
	defer rows.Close()

	// Location items
	var items []schema.Item

	for rows.Next() {
		var item schema.Item
		err := rows.Scan(
			&item.ClientID,
			&item.ClientName,
			&item.ItemID,
			&item.Quantity,
			&item.AvailableQty,
			&item.SKU,
			&item.Scannable,
			&item.BatchNumber,
			&item.SerialNumber,
			&item.ExpirationDate,
			&item.BaseUnit,
			&item.Name,
			&item.Description,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into item schema")
		}
		if item.ItemID != nil {
			items = append(items, item)
		}
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	// Response structure
	locationResponse := &schema.LocationResponse{
		ID:                location.ID,
		WarehouseID:       location.WarehouseID,
		ClientID:          location.ClientID,
		LocationID:        location.LocationID,
		Code:              location.Code,
		AreaName:          location.AreaName,
		AreaID:            location.AreaID,
		OnHold:            location.OnHold,
		OnHoldByID:        location.OnHoldByID,
		LastUpdatedAt:     location.LastUpdatedAt,
		LastUpdatedByID:   location.LastUpdatedByID,
		LastUpdatedByName: location.LastUpdatedByName,
		CreatedAt:         location.CreatedAt,
		Items:             items,
		IsPrime:           location.IsPrime,
	}

	return locationResponse, nil
}

// # Get Aggregate Location
func (l *LocationImpl) GetAggregateLocation(locationID, whID string) (*schema.AggregateLocation, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query string

	// # Step-1: Get Location Details #

	// # Location Details Object
	var location model.Location

	// # Query to get 'location details'
	query = `SELECT
				*
			FROM
				location_wise_inventory
			WHERE
				location_id = $1
			LIMIT
				1`

	// # Execute the 'query' and scan the result into 'location' object
	err = l.DB.QueryRow(ctx, query, locationID).Scan(
		&location.ID,
		&location.ItemID,
		&location.WarehouseID,
		&location.ClientID,
		&location.LocationID,
		&location.Code,
		&location.Quantity,
		&location.AvailableQty,
		&location.AreaName,
		&location.AreaID,
		&location.OnHold,
		&location.OnHoldByID,
		&location.LastUpdatedAt,
		&location.LastUpdatedByID,
		&location.LastUpdatedByName,
		&location.SKU,
		&location.Scannable,
		&location.BatchNumber,
		&location.SerialNumber,
		&location.ExpirationDate,
		&location.CreatedAt,
		&location.ClientName,
		&location.IsPrime,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.Wrap(err, "no rows found for location")
		}
		return nil, errors.Wrap(err, "unable to read row into location object")
	}

	// # Step-2: Get Location Items #

	// # Query to get 'location items' for the 'locationID' provided
	query = `SELECT
				l.client_id,
				l.client_name,
				l.item_id,
				l.quantity,
				l.available_qty,
				l.sku,
				l.scannable,
				l.batch_number,
				l.serial_number,
				l.expiration_date,
				i.base_unit,
				i."name",
				i.image,
				i.description
			FROM
				location_wise_inventory l
				INNER JOIN inventory i ON l.item_id = i.item_id
			WHERE
				location_id = $1`

	// # Execute the 'query' and scan the result into 'rows' object
	rows, err = l.DB.Query(ctx, query, locationID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query location wise inventory for location items")
	}

	defer rows.Close()

	// # Location Items List
	var items []*schema.Item

	for rows.Next() {
		var item schema.Item

		err = rows.Scan(
			&item.ClientID,
			&item.ClientName,
			&item.ItemID,
			&item.Quantity,
			&item.AvailableQty,
			&item.SKU,
			&item.Scannable,
			&item.BatchNumber,
			&item.SerialNumber,
			&item.ExpirationDate,
			&item.BaseUnit,
			&item.Name,
			&item.Image,
			&item.Description,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into item object")
		}

		items = append(items, &item)
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows for location items")
	}

	// # Step-3: Get Location Containers [Container Details + Container Items] #

	// # Query to get 'location containers' for the 'locationID' and 'whID' provided
	query = `SELECT
				c.container_id,
				c.code,
				t."name",
				c.last_updated_at,
				c.created_at,
				c.is_shipped,
				c.item_id,
				c.quantity,
				c.available_qty,
				c.sku,
				c.scannable,
				c.batch_number,
				c.serial_number,
				c.expiration_date,
				i.base_unit,
				i."name",
				i.image,
				i.description,
				i.client_id,
				i.client_name
			FROM
				container_wise_inventory c
				INNER JOIN container_type t ON c.container_type_id = t."id"
				INNER JOIN inventory i ON c.item_id = i.item_id
			WHERE
				c.location_id = $1
				AND c.warehouse_id = $2`

	// # Execute the 'query' and scan the result into 'rows' object
	rows, err = l.DB.Query(ctx, query, locationID, whID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query container wise inventory for location containers")
	}

	// # Location Containers List
	var containers []*schema.AggregateContainer

	// # Map to store 'container' by their 'containerID'
	containerMap := make(map[string]*schema.AggregateContainer)

	var containerID string

	for rows.Next() {
		// # Aggregate Container Object
		var container schema.AggregateContainer

		// # Aggregate Container Item Object
		var containerItem schema.AggregateContainerItem

		// # Container Item Object
		var item schema.Item

		err = rows.Scan(
			&container.ContainerID,
			&container.Code,
			&container.ContainerType,
			&containerItem.LastUpdatedAt,
			&containerItem.CreatedAt,
			&containerItem.IsShipped,
			&item.ItemID,
			&item.Quantity,
			&item.AvailableQty,
			&item.SKU,
			&item.Scannable,
			&item.BatchNumber,
			&item.SerialNumber,
			&item.ExpirationDate,
			&item.BaseUnit,
			&item.Name,
			&item.Image,
			&item.Description,
			&item.ClientID,
			&item.ClientName,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into container object")
		}

		// # Convert the 'containerID' from 'UUID' to 'string'
		containerID = container.ContainerID.String()

		// # Skip 'empty' container IDs
		if containerID == "" {
			continue
		}

		// # Check if 'container' already exists in the 'container map'
		existingContainer, isContainerExists := containerMap[containerID]

		if isContainerExists {
			// # Append the 'container item' into an 'existing container' object 'container details' object 'items' list
			existingContainer.ContainerDetails.Items = append(existingContainer.ContainerDetails.Items, &item)
		} else {
			// # Add the 'container item' into 'containerItem' object 'items' list
			containerItem.Items = []*schema.Item{&item}

			// # Set the 'containerItem' object into 'container' object 'container details' object
			container.ContainerDetails = &containerItem

			// # Append the 'container' object into 'containers' list
			containers = append(containers, &container)

			// # Add the 'container' object into the 'container map'
			containerMap[containerID] = &container
		}
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows for location containers")
	}

	// # Step-4: Aggregate Location Response Object Creation #

	// # Aggregate Location Response Object
	aggregateLocation := schema.AggregateLocation{
		// # Location Details
		ID:                location.ID,
		WarehouseID:       location.WarehouseID,
		ClientID:          location.ClientID,
		LocationID:        location.LocationID,
		Code:              location.Code,
		AreaName:          location.AreaName,
		AreaID:            location.AreaID,
		OnHold:            location.OnHold,
		OnHoldByID:        location.OnHoldByID,
		LastUpdatedAt:     location.LastUpdatedAt,
		LastUpdatedByID:   location.LastUpdatedByID,
		LastUpdatedByName: location.LastUpdatedByName,
		CreatedAt:         location.CreatedAt,
		IsPrime:           location.IsPrime,
		Items:             items,      // # Location Items
		Containers:        containers, // # Location Containers [Container Details + Container Items]
	}

	// # Return Aggregate Location
	return &aggregateLocation, nil
}

// # Get Item Locations All Containers
func (l *LocationImpl) GetItemLocationsAllContainers(itemID, clientID, search string, startDate, endDate time.Time) (*schema.ItemLocations, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query, filterQuery string

	var paramIndex int
	var queryParams []interface{}

	var searchPresent, datePresent bool = false, false

	// # Check for 'startDate' and 'endDate' presence and set the flag
	if !startDate.IsZero() && !endDate.IsZero() {
		datePresent = true
	}

	// # Wildcard Search Query
	if search != "" {
		// # Search Filter: Search by 'lp_code' (LP) and 'batch_number' (lot#)
		search = "%" + search + "%"
		searchPresent = true
	}

	var matchField string

	// # Base Query Parameters (Item ID or Client ID)
	// # Check for 'itemID' or 'clientID' presence
	if itemID != "" {
		matchField = "c.item_id"
		queryParams = append(queryParams, itemID)
	} else if clientID != "" {
		matchField = "c.client_id"
		queryParams = append(queryParams, clientID)
	} else {
		err = errors.New("itemID or clientID is a required field in query params")
		return nil, err
	}

	paramIndex = 2

	// # Expiration Date Filter
	if datePresent {
		filterQuery += fmt.Sprintf(`AND (c.expiration_date BETWEEN $%d AND $%d)`, paramIndex, paramIndex+1)
		queryParams = append(queryParams, startDate, endDate)
		paramIndex += 2
	}

	// # Search Filter
	if searchPresent {
		filterQuery += fmt.Sprintf(` AND (c.code ILIKE $%d OR c.batch_number ILIKE $%d)`, paramIndex, paramIndex)
		queryParams = append(queryParams, search)
	}

	// # Final Query
	query = `SELECT
				l.location_id,
				l.code AS location_code,
				c.container_id,
				t."name" AS container_type,
				i."name" AS item_name,
				i.base_unit AS unit,
				c.code AS lp_code,
				c.batch_number,
				c.serial_number,
				c.expiration_date AS expiry_date,
				SUM(c.quantity) AS total_qty
			FROM
				container_wise_inventory c
				INNER JOIN container_type t ON c.container_type_id = t."id"
				INNER JOIN inventory i ON c.item_id = i.item_id
				INNER JOIN location_wise_inventory l ON c.location_id = l.location_id
			WHERE
				` + matchField + ` = $1
				` + filterQuery + `
			GROUP BY
				l.location_id,
				l.code,
				c.container_id,
				t."name",
				i."name",
				i.base_unit,
				c.code,
				c.batch_number,
				c.serial_number,
				c.expiration_date
			ORDER BY
				l.location_id;
		`

	// # Execute Query
	rows, err = l.DB.Query(ctx, query, queryParams...)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for item locations all containers")
	}

	defer rows.Close()

	// # Item Locations Response Object
	var itemLocations schema.ItemLocations

	locationMap := make(map[string]*schema.ItemLocation)

	var locationID string

	for rows.Next() {
		var location schema.ItemLocation
		var container schema.ItemContainer

		err = rows.Scan(
			&location.LocationID,
			&location.LocationCode,
			&container.ContainerID,
			&container.ContainerType,
			&container.ItemName,
			&container.Unit,
			&container.LPCode,
			&container.BatchNumber,
			&container.SerialNumber,
			&container.Expiry,
			&container.TotalQuantity,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into item location and container schema")
		}

		// # Reset the 'locationID' variable
		locationID = ""

		if location.LocationID != nil {
			locationID = *location.LocationID
		}

		// # Skip 'empty' location IDs
		if locationID == "" {
			continue
		}

		// # Check if 'location' already exists in the 'location map'
		existinglocation, isLocationExists := locationMap[locationID]

		if isLocationExists {
			// # Append the 'container' object into an 'existing location' object 'containers' list
			existinglocation.Containers = append(existinglocation.Containers, &container)
		} else {
			// # Add the 'container' object into 'location' object 'containers' list
			location.Containers = []*schema.ItemContainer{&container}

			// # Append the 'location' object into 'locations' list
			itemLocations.Locations = append(itemLocations.Locations, &location)

			// # Add the 'location' object into the 'location map'
			locationMap[locationID] = &location
		}
	}

	err = rows.Err()
	if err != nil {
		return nil, errors.Wrap(err, "unable to read rows for item locations all containers")
	}

	// # Return Item Locations
	return &itemLocations, nil
}

func (l *LocationImpl) GetContainersOnLocation(locationID, whID string) ([]schema.Container, error) {
	query := `SELECT DISTINCT container_id, location_id, code, container_type.name FROM container_wise_inventory INNER JOIN container_type ON container_wise_inventory.container_type_id = container_type.id WHERE location_id = $1 AND container_wise_inventory.warehouse_id = $2`
	rows, err := l.DB.Query(context.TODO(), query, locationID, whID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for location containers")
	}
	defer rows.Close()

	// Container items
	var containers []schema.Container

	for rows.Next() {
		var container schema.Container
		err := rows.Scan(
			&container.ContainerID,
			&container.LocationID,
			&container.Code,
			&container.ContainerType,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into item containers schema")
		}
		containers = append(containers, container)
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	return containers, nil
}

func (l *LocationImpl) GetItemLocations(data *schema.ValidateGetItemLocations, returnAll, allowHold bool) ([]schema.Location, error) {
	ctx := context.Background()

	var query string
	var rows pgx.Rows
	var err error

	if allowHold {
		if data.CombineItemQuantityOnLocation {
			query = `SELECT location_wise_inventory.location_id, location_wise_inventory.code, location_wise_inventory.area_name, location_wise_inventory.area_id, SUM(location_wise_inventory.quantity) AS quantity, SUM(location_wise_inventory.available_qty) AS available_qty, location_wise_inventory.on_hold, inventory.pickable_qty, inventory.base_unit, inventory.on_hold FROM location_wise_inventory INNER JOIN inventory ON location_wise_inventory.item_id = inventory.item_id WHERE (expiration_date > NOW() OR expiration_date IS NULL) AND location_wise_inventory.available_qty > 0 AND location_wise_inventory.item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL) GROUP BY location_wise_inventory.location_id, location_wise_inventory.code, location_wise_inventory.area_name, location_wise_inventory.area_id, location_wise_inventory.on_hold, inventory.pickable_qty, inventory.base_unit, inventory.on_hold`
			rows, err = l.DB.Query(ctx, query, data.ItemID, data.BatchNumber, data.SerialNumber, data.ExpirationDate)
		} else {
			query = `SELECT location_wise_inventory.location_id, location_wise_inventory.code, location_wise_inventory.area_name, location_wise_inventory.area_id, location_wise_inventory.quantity, location_wise_inventory.available_qty, location_wise_inventory.on_hold, inventory.pickable_qty, inventory.base_unit, inventory.on_hold FROM location_wise_inventory INNER JOIN inventory ON location_wise_inventory.item_id = inventory.item_id WHERE (expiration_date > NOW() OR expiration_date IS NULL) AND location_wise_inventory.available_qty > 0 AND location_wise_inventory.item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL)`
			rows, err = l.DB.Query(ctx, query, data.ItemID, data.BatchNumber, data.SerialNumber, data.ExpirationDate)
		}
	} else {
		if data.CombineItemQuantityOnLocation {
			query = `SELECT location_wise_inventory.location_id, location_wise_inventory.code, location_wise_inventory.area_name, location_wise_inventory.area_id, SUM(location_wise_inventory.quantity) AS quantity, SUM(location_wise_inventory.available_qty) AS available_qty, location_wise_inventory.on_hold, inventory.pickable_qty, inventory.base_unit, inventory.on_hold FROM location_wise_inventory INNER JOIN inventory ON location_wise_inventory.item_id = inventory.item_id WHERE (expiration_date > NOW() OR expiration_date IS NULL) AND location_wise_inventory.available_qty > 0 AND location_wise_inventory.item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL) AND location_wise_inventory.on_hold = $5 GROUP BY location_wise_inventory.location_id, location_wise_inventory.code, location_wise_inventory.area_name, location_wise_inventory.area_id, location_wise_inventory.on_hold, inventory.pickable_qty, inventory.base_unit, inventory.on_hold`
			rows, err = l.DB.Query(ctx, query, data.ItemID, data.BatchNumber, data.SerialNumber, data.ExpirationDate, allowHold)
		} else {
			query = `SELECT location_wise_inventory.location_id, location_wise_inventory.code, location_wise_inventory.area_name, location_wise_inventory.area_id, location_wise_inventory.quantity, location_wise_inventory.available_qty, location_wise_inventory.on_hold, inventory.pickable_qty, inventory.base_unit, inventory.on_hold FROM location_wise_inventory INNER JOIN inventory ON location_wise_inventory.item_id = inventory.item_id WHERE (expiration_date > NOW() OR expiration_date IS NULL) AND location_wise_inventory.available_qty > 0 AND location_wise_inventory.item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL) AND location_wise_inventory.on_hold = $5`
			rows, err = l.DB.Query(ctx, query, data.ItemID, data.BatchNumber, data.SerialNumber, data.ExpirationDate, allowHold)
		}
	}

	if err != nil {
		return nil, errors.Wrap(err, "failed to query for item locations")
	}
	defer rows.Close()

	// Container items
	var locations_list, locations []schema.Location

	for rows.Next() {
		var location schema.Location
		err := rows.Scan(
			&location.LocationID,
			&location.Code,
			&location.AreaName,
			&location.AreaID,
			&location.Quantity,
			&location.AvailableQuantity,
			&location.LocationOnHold,
			&location.PickableQuantity,
			&location.BaseUnit,
			&location.ItemOnHold,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into item location schema")
		}
		locations_list = append(locations_list, location)
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	// check if no locations were found
	if len(locations_list) == 0 {
		return nil, errors.New("No pickable locations found for the given item!")
	}

	// Get Areas and their properties from core
	areaIDs := model.StringSet{}
	for _, loc := range locations_list {
		areaIDs.Add(loc.AreaID)
	}
	coreRes, err := l.App.GrpcClient.Core.Client.GetPropertiesOfAreas(ctx, &core_proto.GetPropertiesOfAreasRequest{AreaIds: areaIDs.ToSlice()})
	if err != nil {
		return nil, errors.Wrap(err, "Failed to fetch area properties from core")
	}

	// Map to store area properties
	areaPropertiesMap := make(map[string]schema.AreaPopertiesBool)

	// Populate the map
	for _, area := range coreRes.GetAreaProperties() {
		areaPropertiesMap[area.AreaId] = schema.AreaPopertiesBool{
			IsPrime:    area.IsPrime,
			IsPickable: area.IsPickable,
			IsHold:     area.IsHold,
		}
	}

	for _, loc := range locations_list {
		if returnAll {
			locations = append(locations, schema.Location{
				LocationID:        loc.LocationID,
				AreaID:            loc.AreaID,
				Code:              loc.Code,
				AreaName:          loc.AreaName,
				Quantity:          loc.Quantity,
				AvailableQuantity: loc.AvailableQuantity,
				PickableQuantity:  loc.PickableQuantity,
				BaseUnit:          loc.BaseUnit,
				LocationOnHold:    loc.LocationOnHold,
				ItemOnHold:        loc.ItemOnHold,
			})
		} else {
			isPickable := areaPropertiesMap[loc.AreaID].IsPickable
			isPrime := areaPropertiesMap[loc.AreaID].IsPrime
			if isPickable && isPrime {
				locations = append([]schema.Location{
					{
						LocationID:        loc.LocationID,
						AreaID:            loc.AreaID,
						Code:              loc.Code,
						AreaName:          loc.AreaName,
						Quantity:          loc.Quantity,
						AvailableQuantity: loc.AvailableQuantity,
						PickableQuantity:  loc.PickableQuantity,
						BaseUnit:          loc.BaseUnit,
						LocationOnHold:    loc.LocationOnHold,
						ItemOnHold:        loc.ItemOnHold,
					},
				}, locations...)
			}
			if isPickable && !isPrime {
				locations = append(locations, schema.Location{
					LocationID:        loc.LocationID,
					AreaID:            loc.AreaID,
					Code:              loc.Code,
					AreaName:          loc.AreaName,
					Quantity:          loc.Quantity,
					AvailableQuantity: loc.AvailableQuantity,
					PickableQuantity:  loc.PickableQuantity,
					BaseUnit:          loc.BaseUnit,
					LocationOnHold:    loc.LocationOnHold,
					ItemOnHold:        loc.ItemOnHold,
				})
			}
		}
	}

	return locations, nil
}

func (l *LocationImpl) GetItemLocationsGroup(data *schema.ValidateGetItemLocationsGroup, returnAll, allowHold bool) ([]schema.LocationGroup, error) {
	ctx := context.Background()

	// Create transaction
	tx, err := l.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to begin transaction")
	}
	defer tx.Rollback(ctx)

	var query string
	var rows pgx.Rows

	if allowHold {
		query = `SELECT item_id, code, location_id, SUM(quantity) AS quantity, SUM(available_qty) AS available_qty, on_hold FROM location_wise_inventory WHERE item_id = $1 GROUP BY item_id, code, location_id, on_hold ORDER BY code ASC`
		rows, err = l.DB.Query(ctx, query, data.ItemID)
	} else {
		query = `SELECT item_id, code, location_id, SUM(quantity) AS quantity, SUM(available_qty) AS available_qty, on_hold FROM location_wise_inventory WHERE item_id = $1 AND on_hold = $2 GROUP BY item_id, code, location_id, on_hold ORDER BY code ASC`
		rows, err = l.DB.Query(ctx, query, data.ItemID, allowHold)
	}
	if err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to query for item locations")
	}
	defer rows.Close()

	// Container items
	var locations_list []schema.LocationGroup

	for rows.Next() {
		var location schema.LocationGroup
		err := rows.Scan(
			&location.ItemID,
			&location.Code,
			&location.LocationID,
			&location.Quantity,
			&location.AvailableQty,
			&location.LocationOnHold,
		)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "unable to read resultant rows into item location schema")
		}
		locations_list = append(locations_list, location)
	}

	if row_err := rows.Err(); row_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back")
	}

	return locations_list, nil
}

func (l *LocationImpl) HoldLocation(v *schema.ValidateHoldLocation) (bool, error) {
	ctx := context.Background()

	// Create transaction
	tx, err := l.DB.Begin(ctx)
	if err != nil {
		return false, errors.Wrap(err, "unable to begin transaction")
	}
	defer tx.Rollback(ctx)
	var isHold bool
	if v.IsHold == nil {
		isHold = false
	} else {
		isHold = *v.IsHold
	}

	var existingOnHold bool
	query := `SELECT on_hold FROM location_wise_inventory WHERE location_id = $1`
	err = l.DB.QueryRow(ctx, query, v.LocationID).Scan(&existingOnHold)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "unable to query location wise inventory")
	}
	if isHold == existingOnHold {
		tx.Rollback(ctx)
		if isHold {
			return false, errors.New("location is already on hold")
		}
		return false, errors.New("location is already in unhold state")
	}

	if isHold {
		query := `UPDATE location_wise_inventory SET on_hold = $1, on_hold_by_id = $2, last_updated_by_id = $2, last_updated_by_name = $3, last_updated_at = $4  WHERE location_id = $5 RETURNING item_id, available_qty, batch_number, serial_number, expiration_date`
		rows, err := tx.Query(ctx, query, isHold, v.UserID, v.Username, time.Now().UTC(), v.LocationID)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "error while putting location on hold")
		}
		defer rows.Close()
		var locationItems []schema.LocationItemObj
		for rows.Next() {
			var locationItem schema.LocationItemObj
			err := rows.Scan(
				&locationItem.ItemID,
				&locationItem.AvailableQty,
				&locationItem.BatchNumber,
				&locationItem.SerialNumber,
				&locationItem.ExpirationDate,
			)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "failed to scan location rows for item")
			}
			locationItems = append(locationItems, locationItem)
		}
		if row_err := rows.Err(); row_err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(row_err, "unable to read location rows")
		}

		for _, item := range locationItems {
			// Update pickable quantity in inventory
			query = `UPDATE inventory SET pickable_qty = pickable_qty - $1 WHERE item_id = $2`
			commandTag, err := tx.Exec(ctx, query, item.AvailableQty, item.ItemID)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "unable to update inventory for pickable quantity")
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return false, errors.New("inventory wasn't updated")
			}

			if item.BatchNumber != nil || item.SerialNumber != nil || item.ExpirationDate != nil {
				// any tracked parameter exists
				query := `UPDATE tracked_inventory SET pickable_qty = pickable_qty - $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL)`
				commandTag, err := tx.Exec(ctx, query, item.AvailableQty, item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "unable to update tracked inventory for pickable quantity")
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return false, errors.New("tracked inventory wasn't updated")
				}
			}
		}
	} else {
		query := `UPDATE location_wise_inventory SET on_hold = $1, on_hold_by_id = $2, last_updated_by_id = $2, last_updated_by_name = $3, last_updated_at = $4  WHERE location_id = $5 RETURNING item_id, available_qty, batch_number, serial_number, expiration_date`
		rows, err := tx.Query(ctx, query, isHold, v.UserID, v.Username, time.Now().UTC(), v.LocationID)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "error while putting location on hold")
		}
		defer rows.Close()
		var locationItems []schema.LocationItemObj
		for rows.Next() {
			var locationItem schema.LocationItemObj
			err := rows.Scan(
				&locationItem.ItemID,
				&locationItem.AvailableQty,
				&locationItem.BatchNumber,
				&locationItem.SerialNumber,
				&locationItem.ExpirationDate,
			)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "failed to scan location rows for item")
			}
			locationItems = append(locationItems, locationItem)
		}
		if row_err := rows.Err(); row_err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(row_err, "unable to read location rows")
		}
		for _, item := range locationItems {
			// Update pickable quantity in inventory
			query = `UPDATE inventory SET pickable_qty = pickable_qty + $1 WHERE item_id = $2`
			commandTag, err := tx.Exec(ctx, query, item.AvailableQty, item.ItemID)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "unable to update inventory")
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return false, errors.New("inventory wasn't updated")
			}

			if item.BatchNumber != nil || item.SerialNumber != nil || item.ExpirationDate != nil {
				// any tracked parameter exists
				query := `UPDATE tracked_inventory SET pickable_qty = pickable_qty + $1 WHERE item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL)`
				commandTag, err := tx.Exec(ctx, query, item.AvailableQty, item.ItemID, item.BatchNumber, item.SerialNumber, item.ExpirationDate)
				if err != nil {
					tx.Rollback(ctx)
					return false, errors.Wrap(err, "unable to update tracked inventory")
				}
				if commandTag.RowsAffected() != 1 {
					tx.Rollback(ctx)
					return false, errors.New("tracked inventory wasn't updated")
				}
			}
		}
	}

	req := core_proto.HoldLocationRequest{
		LocationId: v.LocationID,
		IsHold:     isHold,
	}
	response, err := l.App.GrpcClient.Core.Client.HoldLocation(ctx, &req)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "Failed to hold location on core")
	}
	if !response.Success {
		tx.Rollback(ctx)
		return false, errors.New("Failed to hold location on core")
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "failed to commit transaction, rolling back")
	}

	return true, nil
}

func (l *LocationImpl) SuggestEmptyLocations(data *schema.ValidateSuggestLocation) ([]schema.Location, error) {
	var locationId *string
	if data.ContainerID != nil {
		query := `SELECT location_id FROM container_wise_inventory WHERE container_id = $1 AND warehouse_id = $2`
		err := l.DB.QueryRow(context.TODO(), query, data.ContainerID, data.WarehouseID).Scan(&locationId)
		if err != nil {
			return nil, errors.Wrap(err, "unable to get location id using container id")
		}
	} else {
		query := `SELECT location_id FROM location_wise_inventory WHERE item_id = $1 AND warehouse_id = $2`
		err := l.DB.QueryRow(context.TODO(), query, data.ItemID, data.WarehouseID).Scan(&locationId)
		if err != nil {
			return nil, errors.Wrap(err, "unable to get location id using item id")
		}
	}

	query := `(SELECT location_id, code, area_id, area_name FROM location_wise_inventory WHERE location_id > $1 AND warehouse_id = $2 AND quantity = 0 LIMIT 5) UNION (SELECT location_id, code, area_id, area_name FROM location_wise_inventory WHERE location_id < $1 AND warehouse_id = $2 AND quantity = 0 LIMIT 5) LIMIT 3`
	rows, err := l.DB.Query(context.TODO(), query, locationId, data.WarehouseID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to fetch empty locations.")
	}
	var locations []schema.Location
	for rows.Next() {
		var location schema.Location
		err := rows.Scan(
			&location.LocationID,
			&location.Code,
			&location.AreaID,
			&location.AreaName,
		)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to scan location rows into location variable")
		}
		locations = append(locations, location)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "Unable to read location rows")
	}

	return locations, nil
}

func (l *LocationImpl) GetTrackedItemDetails(locationID, itemID string) ([]schema.TrackedItemDetails, error) {
	query := `SELECT quantity, available_qty, batch_number, serial_number, expiration_date, created_at FROM location_wise_inventory WHERE location_id = $1 AND item_id = $2`
	rows, err := l.DB.Query(context.TODO(), query, locationID, itemID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to fetch locations")
	}
	var trackedItems []schema.TrackedItemDetails
	for rows.Next() {
		var trackedItem schema.TrackedItemDetails
		err := rows.Scan(
			&trackedItem.Quantity,
			&trackedItem.AvailableQty,
			&trackedItem.BatchNumber,
			&trackedItem.SerialNumber,
			&trackedItem.ExpirationDate,
			&trackedItem.CreatedAt,
		)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to scan location rows into location variable")
		}
		trackedItems = append(trackedItems, trackedItem)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "Unable to read location rows")
	}

	return trackedItems, nil
}

func (c *LocationImpl) SearchLocations(searchQuery, warehouseID string, resources []string) ([]model.SearchResponse, error) {
	response := []model.SearchResponse{}
	counter := 0

	searchQuery = "%" + searchQuery + "%"

	if len(resources) == 1 {
		var table string

		if resources[0] == "location" {
			table = "location_wise_inventory"
		} else {
			table = "container_wise_inventory"
		}

		// Query the relevant table for information
		if table == "location_wise_inventory" {

			query := `SELECT DISTINCT area_id, area_name, code, location_id, warehouse_id FROM location_wise_inventory WHERE (code ILIKE $1 OR area_name ILIKE $1) AND warehouse_id = $2`
			rows, err := c.DB.Query(context.TODO(), query, searchQuery, warehouseID)
			if err == pgx.ErrNoRows {
				return response, nil
			}
			if err != nil {
				return nil, errors.Wrap(err, "failed to query for location wise Inventory")
			}

			for rows.Next() {
				var searchResponseSource model.SearchResponseSource
				err := rows.Scan(
					&searchResponseSource.AreaID,
					&searchResponseSource.AreaName,
					&searchResponseSource.Code,
					&searchResponseSource.LocationID,
					&searchResponseSource.WarehouseID,
				)
				if err != nil {
					return nil, errors.Wrap(err, "unable to read resultant rows into search response schema")
				}
				searchResponse := model.SearchResponse{
					ID:     strconv.Itoa(counter),
					Index:  "location",
					Score:  1,
					Source: searchResponseSource,
				}
				response = append(response, searchResponse)
				counter++
			}

			if row_err := rows.Err(); row_err != nil {
				return nil, errors.Wrap(row_err, "unable to read rows")
			}
		} else if table == "container_wise_inventory" {

			query := `SELECT DISTINCT c.code, c.container_id, ct.name, c.container_type_id, c.warehouse_id FROM container_wise_inventory c INNER JOIN container_type ct ON c.container_type_id = ct.id  WHERE (c.code ILIKE $1 OR ct.name ILIKE $1) AND c.warehouse_id = $2`
			rows, err := c.DB.Query(context.TODO(), query, searchQuery, warehouseID)
			if err == pgx.ErrNoRows {
				return response, nil
			}
			if err != nil {
				return nil, errors.Wrap(err, "failed to query for container wise Inventory")
			}

			for rows.Next() {
				var searchResponseSource model.SearchResponseSource
				err := rows.Scan(
					&searchResponseSource.Code,
					&searchResponseSource.ContainerID,
					&searchResponseSource.ContainerType,
					&searchResponseSource.ContainerTypeID,
					&searchResponseSource.WarehouseID,
				)
				if err != nil {
					return nil, errors.Wrap(err, "unable to read resultant rows into search response schema")
				}
				searchResponse := model.SearchResponse{
					ID:     strconv.Itoa(counter),
					Index:  "container",
					Score:  1,
					Source: searchResponseSource,
				}
				response = append(response, searchResponse)
				counter++
			}

			if row_err := rows.Err(); row_err != nil {
				return nil, errors.Wrap(row_err, "unable to read rows")
			}
		}
	} else if len(resources) == 2 {

		query := `SELECT DISTINCT area_id, area_name, code, location_id, warehouse_id FROM location_wise_inventory WHERE (code ILIKE $1 OR area_name ILIKE $1) AND warehouse_id = $2`
		rows, err := c.DB.Query(context.TODO(), query, searchQuery, warehouseID)
		if err == pgx.ErrNoRows {
			return response, nil
		}
		if err != nil {
			return nil, errors.Wrap(err, "failed to query for location wise Inventory")
		}

		for rows.Next() {
			var searchResponseSource model.SearchResponseSource
			err := rows.Scan(
				&searchResponseSource.AreaID,
				&searchResponseSource.AreaName,
				&searchResponseSource.Code,
				&searchResponseSource.LocationID,
				&searchResponseSource.WarehouseID,
			)
			if err != nil {
				return nil, errors.Wrap(err, "unable to read resultant rows into search response schema")
			}
			searchResponse := model.SearchResponse{
				ID:     strconv.Itoa(counter),
				Index:  "location",
				Score:  1,
				Source: searchResponseSource,
			}
			response = append(response, searchResponse)
			counter++
		}

		if row_err := rows.Err(); row_err != nil {
			return nil, errors.Wrap(row_err, "unable to read rows")
		}

		query = `SELECT DISTINCT c.code, c.container_id, ct.name, c.container_type_id, c.warehouse_id FROM container_wise_inventory c INNER JOIN container_type ct ON c.container_type_id = ct.id  WHERE (c.code ILIKE $1 OR ct.name ILIKE $1) AND c.warehouse_id = $2`
		rows, err = c.DB.Query(context.TODO(), query, searchQuery, warehouseID)
		if err == pgx.ErrNoRows {
			return response, nil
		}
		if err != nil {
			return nil, errors.Wrap(err, "failed to query for container wise Inventory")
		}

		for rows.Next() {
			var searchResponseSource model.SearchResponseSource
			err := rows.Scan(
				&searchResponseSource.Code,
				&searchResponseSource.ContainerID,
				&searchResponseSource.ContainerType,
				&searchResponseSource.ContainerTypeID,
				&searchResponseSource.WarehouseID,
			)
			if err != nil {
				return nil, errors.Wrap(err, "unable to read resultant rows into search response schema")
			}
			searchResponse := model.SearchResponse{
				ID:     strconv.Itoa(counter),
				Index:  "container",
				Score:  1,
				Source: searchResponseSource,
			}
			response = append(response, searchResponse)
			counter++
		}

		if row_err := rows.Err(); row_err != nil {
			return nil, errors.Wrap(row_err, "unable to read rows")
		}
	}

	return response, nil
}

func (l *LocationImpl) SuggestLocationsForWorkOrder(data *schema.ValidateSuggestLocationForWorkOrder) (interface{}, error) {
	var locationData []model.LocationData
	var trackedInventory []model.TrackedInventory
	query := `SELECT item_id, batch_number, serial_number, expiration_date, quantity, available_qty FROM tracked_inventory WHERE item_id = $1`
	rows, err := l.DB.Query(context.TODO(), query, data.ItemID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to query tracked inventory")
	}
	defer rows.Close()
	for rows.Next() {
		var tracInv model.TrackedInventory
		err := rows.Scan(
			&tracInv.ItemID,
			&tracInv.BatchNumber,
			&tracInv.SerialNumber,
			&tracInv.ExpirationDate,
			&tracInv.Quantity,
			&tracInv.AvailableQty,
		)
		if err != nil {
			return nil, errors.Wrap(err, "Unable to read resultant rows into tracked inventory schema")
		}
		trackedInventory = append(trackedInventory, tracInv)
	}
	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "Unable to read rows")
	}

	for _, trackedInv := range trackedInventory {
		var locations []model.LocationForWO
		query := `SELECT location_id, code, area_id, area_name, quantity, available_qty, on_hold FROM location_wise_inventory WHERE item_id = $1 AND (batch_number = $2 OR $2 IS NULL) AND (serial_number = $3 OR $3 IS NULL) AND (expiration_date = $4 OR $4 IS NULL)`
		rows, err := l.DB.Query(context.TODO(), query, data.ItemID, trackedInv.BatchNumber, trackedInv.SerialNumber, trackedInv.ExpirationDate)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to query location wise inventory")
		}
		defer rows.Close()
		for rows.Next() {
			var location model.LocationForWO
			err := rows.Scan(
				&location.LocationID,
				&location.Code,
				&location.AreaID,
				&location.AreaName,
				&location.Quantity,
				&location.AvailableQuantity,
				&location.LocationOnHold,
			)
			if err != nil {
				return nil, errors.Wrap(err, "Failed to scan rows from location wise inventory")
			}
			locations = append(locations, location)
		}
		if row_err := rows.Err(); row_err != nil {
			return nil, errors.Wrap(row_err, "Unable to read rows from location wise inventory")
		}
		if len(locations) > 0 {
			itemTracInfo := model.ItemTrackedInfo{
				Batch:      trackedInv.BatchNumber,
				Serial:     trackedInv.SerialNumber,
				Expiration: trackedInv.ExpirationDate,
			}
			locData := model.LocationData{
				ItemID:          data.ItemID,
				ItemTrackedInfo: &itemTracInfo,
				TotalQty:        trackedInv.Quantity,
				AvailableQty:    trackedInv.AvailableQty,
				Locations:       locations,
			}
			locationData = append(locationData, locData)
		}
	}

	return locationData, nil
}

func (l *LocationImpl) DeleteLocations(data *schema.ValidateDeleteLocations) ([]string, []string, error) {
	deletedLocationCodes, undeletableLocationCodes := []string{}, []string{}
	var locationsToDelete []string
	// Create transaction
	tx, err := l.DB.Begin(context.Background())
	if err != nil {
		return nil, nil, errors.Wrap(err, "unable to begin transaction")
	}
	defer tx.Rollback(context.Background())

	// fetch all locations
	query := `SELECT DISTINCT ON (location_id) location_id, item_id, code FROM location_wise_inventory WHERE location_id = ANY($1) ORDER BY location_id`
	rows, err := tx.Query(context.TODO(), query, data.LocationIDs)
	if err != nil {
		tx.Rollback(context.Background())
		return nil, nil, errors.Wrap(err, "failed to query for locations")
	}
	defer rows.Close()

	for rows.Next() {
		var locationData model.DeleteLocationsData
		err := rows.Scan(
			&locationData.LocationID,
			&locationData.ItemID,
			&locationData.Code,
		)
		if err != nil {
			tx.Rollback(context.Background())
			return nil, nil, errors.Wrap(err, "unable to read resultant rows into location schema")
		}

		if locationData.ItemID != nil {
			undeletableLocationCodes = append(undeletableLocationCodes, locationData.Code)
		} else {
			locationsToDelete = append(locationsToDelete, locationData.LocationID)
			deletedLocationCodes = append(deletedLocationCodes, locationData.Code)
		}
	}
	if row_err := rows.Err(); row_err != nil {
		tx.Rollback(context.Background())
		return nil, nil, errors.Wrap(row_err, "unable to read rows")
	}

	if len(locationsToDelete) > 0 {
		// delete locations
		query := `DELETE FROM location_wise_inventory WHERE location_id = ANY($1)`
		res, err := tx.Exec(context.TODO(), query, locationsToDelete)
		if err != nil {
			tx.Rollback(context.Background())
			return nil, nil, errors.Wrap(err, "Failed to delete locations")
		}
		if res.RowsAffected() == 0 {
			tx.Rollback(context.Background())
			return nil, nil, errors.New("No locations deleted")
		}

		// delete location on core service
		req := core_proto.DeleteLocationsRequest{
			LocationIds: locationsToDelete,
		}
		_, err = l.App.GrpcClient.Core.Client.DeleteLocations(context.Background(), &req)
		if err != nil {
			tx.Rollback(context.Background())
			return nil, nil, errors.Wrap(err, "Failed to delete locations on core service.")
		}
	}

	// Commit transaction
	if tx_err := tx.Commit(context.Background()); tx_err != nil {
		tx.Rollback(context.Background())
		return nil, nil, errors.Wrap(err, "failed to commit transaction, rolling back")
	}

	return deletedLocationCodes, undeletableLocationCodes, nil
}

// # Get All Warehouse Locations
func (l *LocationImpl) GetAllWarehouseLocations(warehouseID, search string) (*model.LocationResponse, error) {
	ctx := context.TODO()

	var err error

	var rows pgx.Rows
	var query string

	var queryParams []interface{}

	var searchPresent bool = false

	// # Wildcard Search Query
	if search != "" {
		// # Search Filter: Search by 'area_name', 'client_name', 'location_code' (code), 'sku', 'scannable', 'batch_number', and 'serial_number'
		search = "%" + search + "%"
		searchPresent = true
	}

	// # Query Parameters
	queryParams = append(queryParams, warehouseID)

	// # Base Query
	query = `SELECT DISTINCT ON (location_id)
				location_id,
				client_id,
				warehouse_id,
				area_id,
				area_name,
				client_name,
				"code" AS location_code,
				quantity AS total_qty,
				available_qty,
				sku,
				scannable,
				batch_number,
				serial_number,
				expiration_date,
				on_hold,
				is_prime
			FROM
				location_wise_inventory
			WHERE
				warehouse_id = $1
			ORDER BY
				location_id DESC`

	// # Search Filter
	if searchPresent {
		query = `SELECT DISTINCT ON (location_id)
					location_id,
					client_id,
					warehouse_id,
					area_id,
					area_name,
					client_name,
					"code" AS location_code,
					quantity AS total_qty,
					available_qty,
					sku,
					scannable,
					batch_number,
					serial_number,
					expiration_date,
					on_hold,
					is_prime
				FROM
					location_wise_inventory
				WHERE
					warehouse_id = $1
					AND (
						area_name ILIKE $2
						OR client_name ILIKE $2
						OR "code" ILIKE $2
						OR sku ILIKE $2
						OR scannable ILIKE $2
						OR batch_number ILIKE $2
						OR serial_number ILIKE $2
					)
				ORDER BY
					location_id DESC`

		queryParams = append(queryParams, search)
	}

	// # Execute Query
	rows, err = l.DB.Query(ctx, query, queryParams...)
	if err != nil {
		err = errors.Wrap(err, "failed to query location wise inventory for warehouse locations")
		return nil, err
	}

	defer rows.Close()

	var warehouseLocations model.LocationResponse
	var locations []*model.LocationObject

	for rows.Next() {
		var location model.LocationObject
		var quanityInfo model.QuantityInfo

		err = rows.Scan(
			&location.LocationID,
			&location.ClientID,
			&location.WarehouseID,
			&location.AreaID,
			&location.AreaName,
			&location.ClientName,
			&location.LocationCode,
			&quanityInfo.TotalQty,
			&quanityInfo.AvailableQty,
			&location.SKU,
			&location.Scannable,
			&location.BatchNumber,
			&location.SerialNumber,
			&location.ExpirationDate,
			&location.OnHold,
			&location.IsPrime,
		)
		if err != nil {
			err = errors.Wrap(err, "unable to read resultant rows into location object schema")
			return nil, err
		}

		location.QuantityInfo = &quanityInfo

		locations = append(locations, &location)
	}

	err = rows.Err()
	if err != nil {
		err = errors.Wrap(err, "unable to read rows for warehouse locations")
		return nil, err
	}

	warehouseLocations = model.LocationResponse{
		Locations: locations,
	}

	// # Return Warehouse Locations
	return &warehouseLocations, nil
}

// # Get Location Details Layout
func (l *LocationImpl) GetLocationDetailsLayout(locationID, clientID, warehouseID string) (*model.LocationDetailsLayout, error) {
	ctx := context.TODO()

	var err error

	// # Query
	query := `SELECT
				location_id,
				client_id,
				warehouse_id,
				area_id,
				area_name,
				client_name,
				"code" AS location_code
			FROM
				location_wise_inventory
			WHERE
				location_id = $1
				AND client_id = $2
				AND warehouse_id = $3
			LIMIT
				1`

	var locationDetailsLayout model.LocationDetailsLayout

	// # Execute Query
	err = l.DB.QueryRow(ctx, query, locationID, clientID, warehouseID).Scan(
		&locationDetailsLayout.LocationID,
		&locationDetailsLayout.ClientID,
		&locationDetailsLayout.WarehouseID,
		&locationDetailsLayout.AreaID,
		&locationDetailsLayout.AreaName,
		&locationDetailsLayout.ClientName,
		&locationDetailsLayout.LocationCode,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.Wrap(err, "no rows found for location details layout")
		}
		return nil, errors.Wrap(err, "unable to get location details layout")
	}

	// # Form location 'QR Code' string
	locationID = *locationDetailsLayout.LocationID
	locationCode := *locationDetailsLayout.LocationCode

	locationQR := fmt.Sprintf("l/%s/%s", locationID, locationCode)
	locationDetailsLayout.LocationQR = &locationQR

	areaID := *locationDetailsLayout.AreaID

	req := core_proto.GetAreaByIDRequest{
		AreaId: areaID,
	}

	// # Call 'Get Area By ID' gRPC method to fetch 'area details' from 'core' service
	res, err := l.App.GrpcClient.Core.Client.GetAreaByID(ctx, &req)
	if err != nil {
		err = errors.Wrap(err, "failed to get area by ID")
		return nil, err
	}

	locationDetailsLayout.Properties = res.AreaProperties

	// # Return Location Details Layout
	return &locationDetailsLayout, nil
}
