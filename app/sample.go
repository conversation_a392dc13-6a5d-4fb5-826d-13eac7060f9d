package app

import (
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/rs/zerolog"
)

// Sample defines methods of Sample service to be implemented
type Sample interface {
	Sample(string, string) (bool, error)
}

// SampleOpts contains arguments to be accepted for new instance of Sample service
type SampleOpts struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// SampleImpl implements Sample service
type SampleImpl struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// InitSample returns initializes Sample service
func InitSample(opts *SampleOpts) Sample {
	i := &SampleImpl{
		App:    opts.App,
		DB:     opts.DB,
		Logger: opts.Logger,
	}
	return i
}

func (s *SampleImpl) Sample(warehouseID, clientID string) (bool, error) {

	return false, nil
}
