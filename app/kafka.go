package app

import (
	"context"
	"inventory/server/kafka"
)

func InitConsumer(a *App) {

	ctx := context.TODO()

	a.DeleteContainerConsumer = kafka.NewSegmentioKafkaConsumer(&kafka.SegmentioConsumerOpts{
		Logger: a.<PERSON>gger,
		Config: &a.Config.DeleteContainerConsumerConfig,
	})
	go a.DeleteContainerConsumer.ConsumeAndCommit(ctx, a.Container.DeleteContainerConsumer)

	go a.CronManager.EoMLPSnapShotCron()
}

func InitProducer(a *App) {

	// a.ActivityTrackingProducer = kafka.NewSegmentioProducer(&kafka.SegmentioProducerOpts{
	// 	Logger: a.Logger,
	// 	Config: &a.Config.ActivityTrackingProducerConfig,
	// })

	a.TopicNotificationProducer = kafka.NewSegmentioProducer(&kafka.SegmentioProducerOpts{
		Logger: a.<PERSON>,
		Config: &a.Config.TopicNotificationProducerConfig,
	})

	a.SubscriberNotificationProducer = kafka.NewSegmentioProducer(&kafka.SegmentioProducerOpts{
		Logger: a.Logger,
		Config: &a.Config.SubscriberNotificationProducerConfig,
	})

	a.DeleteContainerProducer = kafka.NewSegmentioProducer(&kafka.SegmentioProducerOpts{
		Logger: a.Logger,
		Config: &a.Config.DeleteContainerProducerConfig,
	})

}
