package app

import (
	"bytes"
	"html/template"
	"inventory/schema"
	"strings"

	"github.com/<PERSON><PERSON><PERSON><PERSON>/go-wkhtmltopdf"
	"github.com/divan/num2words"
	"github.com/pkg/errors"
)

// # Parse Template File Function
func ParseTemplateFile(templateFileName, templateFilePath string, data interface{}) (*bytes.Buffer, error) {
	funcMap := template.FuncMap{
		"inc": func(i int) int {
			return i + 1
		},
		"div": func(i *float32) float32 {
			return *i / 2
		},
		"inWords": func(i float64) string {
			return strings.ToUpper(num2words.Convert(int(i)))
		},
	}
	t, err := template.New(templateFileName).Funcs(funcMap).ParseFiles(templateFilePath)
	if err != nil {
		return nil, err
	}
	buf := new(bytes.Buffer)
	if err = t.Execute(buf, data); err != nil {
		return nil, err
	}

	return buf, nil
}

// # Generate PDF Function
func GeneratePDF(body *bytes.Buffer) (*bytes.Buffer, error) {
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return nil, errors.Wrap(err, "PDF Error: Failed to create pdf generator instance.")
	}
	pdfg.Dpi.Set(300)
	pdfg.PageSize.Set(wkhtmltopdf.PageSizeA4)
	pdfg.AddPage(wkhtmltopdf.NewPageReader(body))

	err = pdfg.Create()
	if err != nil {
		return nil, errors.Wrap(err, "PDF Error: Failed to create pdf dociment.")
	}
	buf := pdfg.Buffer()
	return buf, nil
}

// # Generate QR PDF Function
func GenerateQrPDF(QR *schema.QRCode, file string, path string, labelType string) (*bytes.Buffer, error) {
	// # Create the PDF generator instance
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		err = errors.Wrap(err, "PDF Error: Failed to create pdf generator instance.")
		return nil, err
	}

	// # Calculate the height and width of the PDF page based on the label type
	// # Note: Dimension = Width x Height
	var height, width float64
	if labelType == "minimal_1.25x3.5" {
		// # Note: width = 3.5 and height = 1.25 (By mistake it was swapped)
		// # 1.25x3.5 Inches Minimal Label
		width = 1.85 * 25.4
		height = 0.95 * 25.4

	} else if labelType == "minimal_1.5x4" {
		// # Note: width = 4 and height = 1.5 (By mistake it was swapped)
		// # 1.5x4 Inches Minimal Label
		width = 2 * 25.4
		height = 1 * 25.4
	} else {
		// # 4x6 Inches Minimal Label
		width = 8.3 * 25.4
		height = 12.0 * 25.4
	}

	// # Set the PDF properties (DPI and Page Height & Width)
	pdfg.Dpi.Set(300)
	pdfg.PageHeight.Set(uint(height))
	pdfg.PageWidth.Set(uint(width))

	// # Set the PDF margins to 0
	if labelType == "minimal_1.25x3.5" || labelType == "minimal_1.5x4" {
		pdfg.MarginTop.Set(1) // # Set the top margin to 1 inch
	}
	// # No need of top margin for 4x6 Inches Minimal Label
	// else {
	// 	// pdfg.MarginTop.Set(0) // # Set the top margin to 0 inch
	// }
	pdfg.MarginBottom.Set(0)
	pdfg.MarginLeft.Set(0)
	pdfg.MarginRight.Set(0)

	// # Generate the body content (HTML) for the PDF document
	body, err := ParseTemplateFile(file, path, QR)
	if err != nil {
		err = errors.Wrap(err, "Failed to prepare PDF")
		return nil, err
	}

	// # Create a new page with the body content
	page := wkhtmltopdf.NewPageReader(body)

	// # Set the encoding of the page to UTF-8
	page.Encoding.Set("UTF-8")

	// # Set the zoom level of the page
	if labelType == "minimal_1.25x3.5" {
		// # No need to set zoom level for 1.25x3.5 Inches Minimal Label
		// page.Zoom.Set(0.0)
	} else if labelType == "minimal_1.5x4" {
		page.Zoom.Set(1.0)
	} else {
		// # 4x6 Inches Minimal Label
		page.Zoom.Set(1.0)
	}

	// # Explicitly set the header and footer to empty
	page.FooterRight.Set("")
	page.FooterLeft.Set("")
	page.FooterCenter.Set("")
	page.HeaderRight.Set("")
	page.HeaderLeft.Set("")
	page.HeaderCenter.Set("")
	page.FooterFontSize.Set(0)
	page.HeaderFontSize.Set(0)

	// # Add the page to the PDF document
	pdfg.AddPage(page)

	// # Create the PDF document
	err = pdfg.Create()
	if err != nil {
		err = errors.Wrap(err, "PDF Error: Failed to create pdf document.")
		return nil, err
	}

	// # Get the PDF document as a buffer
	buf := pdfg.Buffer()

	// # Return the PDF document buffer
	return buf, nil
}
