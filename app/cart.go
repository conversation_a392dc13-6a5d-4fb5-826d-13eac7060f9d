package app

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"inventory/model"
	"inventory/schema"
	inventory_proto "proto/inventory"
	outbound_proto "proto/outbound"
	"strconv"
	"strings"
	"time"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/pkg/errors"
	"github.com/rs/zerolog"
	uuid "github.com/satori/go.uuid"
)

// Cart defines methods of Cart service to be implemented
type Cart interface {
	CreateCart(*schema.ValidateCreateCart) ([]string, error)
	GetCarts(string, bool, bool) ([]model.Cart, error)
	GetCart(uuid.UUID) (*model.Cart, error)
	PrepareCart(*schema.ValidatePrepareCart) (bool, error)
	UpdateCartAndToteItems(pgx.Tx, context.Context, int32, *inventory_proto.UpdateLocationForPickUpRequest, *model.Inventory, string, string, *uuid.UUID, *uuid.UUID, *string, *string, *string, *string, *time.Time) error
	GetOrderCartDetails(string, string) (*schema.OrderCartDetailsResponse, error)
	RemoveToteFromCart(*schema.ValidateRemoveToteFromCart) (bool, error)
	PrintCart(*schema.ValidatePrintCart) ([]string, error)
}

// CartOpts contains arguments to be accepted for new instance of Cart service
type CartOpts struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// CartImpl implements Cart service
type CartImpl struct {
	App    *App
	DB     *pgxpool.Pool
	Logger *zerolog.Logger
}

// InitCart returns initializes Cart service
func InitCart(opts *CartOpts) Cart {
	e := &CartImpl{
		App:    opts.App,
		DB:     opts.DB,
		Logger: opts.Logger,
	}
	return e
}

func (c *CartImpl) GetPreviousCartCodeInt(whID string) (*int, error) {
	// Cart-0001
	// Previous cart code
	var prevCode string
	var prevCodeInt int
	query := `SELECT code FROM cart WHERE warehouse_id = $1 ORDER BY NULLIF(regexp_replace(code, '\D', '', 'g'), '')::int DESC LIMIT 1`
	err := c.DB.QueryRow(context.TODO(), query, whID).Scan(&prevCode)
	if err != nil {
		if err == pgx.ErrNoRows {
			prevCodeInt = 0
		} else {
			return nil, errors.Wrap(err, "failed to query for carts")
		}
	} else {
		if len(prevCode) != 0 {
			prevCodeInt, err = strconv.Atoi(strings.Split(prevCode, "-")[1])
			if err != nil {
				return nil, errors.Wrap(err, "failed to convert cart code to int")
			}
		} else {
			prevCodeInt = 0
		}
	}
	return &prevCodeInt, nil
}

// CreateCart creates a new cart
func (c *CartImpl) CreateCart(data *schema.ValidateCreateCart) ([]string, error) {
	// # Create Context
	ctx := context.Background()

	// Create transaction
	tx, err := c.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "unable to begin transaction")
	}
	defer tx.Rollback(ctx)

	// # Previous cart code
	prevCodeInt, err := c.GetPreviousCartCodeInt(data.WarehouseID)
	if err != nil {
		tx.Rollback(ctx)
		return nil, err
	}

	// # QR Struct
	var QR schema.QRCode

	// # QR Data Struct
	Data := []schema.QRData{}

	// # Validate if the label type is one of the allowed values if the file type is pdf
	if (data.FileType == "pdf") && (data.LabelType != "minimal_1.25x3.5") && (data.LabelType != "minimal_1.5x4") && (data.LabelType != "minimal_4x6") {
		err := errors.New("label_type must be one of [minimal_1.25x3.5 minimal_1.5x4 minimal_4x6]")
		tx.Rollback(ctx)
		return nil, err
	}

	// # Create Carts
	now := time.Now().UTC()
	var qrCodes []string
	var rows [][]interface{}
	for i := 1; i <= int(data.NumberOfCarts); i++ {
		code := fmt.Sprintf("Cart-%d", *prevCodeInt+i) // # Cart-1
		cart_id := uuid.NewV4()
		qr := fmt.Sprintf("c/%s/%s", cart_id, code) // # c/cart_id/cart_code
		rows = append(rows, []interface{}{data.WarehouseID, cart_id, code, qr, data.Alias, data.RetainTote, now, data.UserID, data.Username, now, data.Length, data.Width, data.Height, data.DimsUnit, data.WeightCapacity, data.WeightUnit})

		// # CSV - QR Code Strings | PDF - QR Code Images
		if data.FileType == "csv" {
			// # Append to QR codes list
			qrCodes = append(qrCodes, qr)
		} else {
			// # Generate QR code PNG image
			bufBytes, err := c.App.Utils.GenerateQrCode(qr)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "Failed to generate QR code")
			}

			// # Encode image to base64 string
			imgBase64Str := base64.StdEncoding.EncodeToString(bufBytes)

			// # Construct QR
			qr := schema.QRData{
				QRcode: imgBase64Str,
				Code:   code,
			}

			// # Append to QR Data struct
			Data = append(Data, qr)
		}
	}

	// # Bulk insert rows
	copyCount, err1 := tx.CopyFrom(
		ctx,
		// # Table name
		pgx.Identifier{"cart"},
		// # Columns
		[]string{"warehouse_id", "cart_id", "code", "qr", "alias", "retain_tote", "created_at", "created_by_id", "created_by_name", "last_updated_at", "length", "width", "height", "dims_unit", "weight_capacity", "weight_unit"},
		// # Rows to insert
		pgx.CopyFromRows(rows),
	)
	if err1 != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err1, "unable to bulk insert carts")
	}
	if copyCount != int64(len(rows)) {
		tx.Rollback(ctx)
		return nil, errors.New("failed to insert some rows in the database")
	}

	if data.FileType == "pdf" {
		// # Construct Final QR Struct
		QR.WarehouseName = data.WarehouseName
		QR.Data = &Data

		// # Template File Variables
		var file, path string

		// # Check Label Type
		if data.LabelType == "minimal_1.25x3.5" {
			// # Label Type: minimal_1.25x3.5
			// # Template File Name
			file = c.App.Config.PDFConfig.LPLabel1x3MinimalTemplateFileName
			// # Template File Path
			path = c.App.Config.PDFConfig.LPLabel1x3MinimalTemplateFilePath
		} else if data.LabelType == "minimal_1.5x4" {
			// # Label Type: minimal_1.5x4
			// # Template File Name
			file = c.App.Config.PDFConfig.LPLabel1x4MinimalTemplateFileName
			// # Template File Path
			path = c.App.Config.PDFConfig.LPLabel1x4MinimalTemplateFilePath
		} else {
			// # Label Type: minimal_4x6
			// # Template File Name
			file = c.App.Config.PDFConfig.LPLabel4x6MinimalTemplateFileName
			// # Template File Path
			path = c.App.Config.PDFConfig.LPLabel4x6MinimalTemplateFilePath
		}

		// # Generate QR PDF
		buf, err := GenerateQrPDF(&QR, file, path, data.LabelType)
		if err != nil {
			msg := "Failed to generate PDF"
			c.Logger.Err(err).Msg(msg)
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, msg)
		}

		// # Base File Name
		baseName := "wms_cart_label.pdf"

		// # File Name Separator
		sep := "_"

		// # Construct file name for PDF file
		fileName := data.WarehouseName + sep + data.LabelType + sep + baseName

		// # S3 Bucket Name
		bucket := c.App.Config.AWSConfig.TempBucket

		// # Upload PDF File to S3
		fileUrl, err := c.App.SSS.AddQRPDFToS3(fileName, bucket, buf.Bytes())
		if err != nil {
			tx.Rollback(ctx)
			err = errors.Wrap(err, "Failed to upload QR codes PDF to S3!")
			return nil, err
		}

		// Commit transaction
		if tx_err := tx.Commit(ctx); tx_err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "failed to commit transaction for pdf, rolling back")
		}

		// # Return S3 URL of PDF file
		return []string{fileUrl}, nil
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back")
	}

	return qrCodes, nil
}

// GetCarts returns all carts for a warehouse with an option to filter empty carts
func (c *CartImpl) GetCarts(whID string, emptyCarts, readyForNewWaveGeneration bool) ([]model.Cart, error) {
	// Create Context
	ctx := context.Background()
	var query string
	mapCart := make(map[string]bool)

	// Determine the query based on conditions
	switch {
	case emptyCarts:
		query = `
            SELECT id, warehouse_id, cart_id, code, qr, alias, retain_tote, length, width, height, dims_unit, 
                   weight_capacity, weight_unit, created_at, created_by_id, created_by_name, last_updated_at, 
                   0 AS tote_count 
            FROM cart 
            WHERE warehouse_id = $1 
              AND cart_id NOT IN (SELECT cart_id FROM cart_totes) 
              AND cart_id NOT IN (SELECT cart_id FROM cart_items)`
	case readyForNewWaveGeneration:

		// Fetch carts in picking state from outbound service
		resCarts, err := c.App.GrpcClient.Outbound.Client.GetCartsInPicking(ctx, &outbound_proto.GetCartsInPickingRequest{WarehouseId: whID})
		if err != nil {
			return nil, errors.Wrap(err, "Failed to get carts in picking state")
		}
		for _, cart := range resCarts.GetCartIds() {
			mapCart[cart] = true
		}

		query = `
            SELECT c.id, c.warehouse_id, c.cart_id, c.code, c.qr, c.alias, c.retain_tote, c.length, c.width, c.height, 
                   c.dims_unit, c.weight_capacity, c.weight_unit, c.created_at, c.created_by_id, c.created_by_name, 
                   c.last_updated_at, COUNT(ct.tote_id) AS tote_count 
            FROM cart c 
            LEFT JOIN cart_totes ct ON c.cart_id = ct.cart_id 
            WHERE c.warehouse_id = $1 
              AND NOT EXISTS (SELECT 1 FROM cart_items ci WHERE ci.cart_id = c.cart_id) 
              AND NOT EXISTS (SELECT 1 FROM tote_items ti 
                              JOIN cart_totes ct2 ON ti.tote_id = ct2.tote_id 
                              WHERE ct2.cart_id = c.cart_id) 
            GROUP BY c.id, c.cart_id, c.warehouse_id, c.code, c.qr, c.alias, c.retain_tote, c.length, c.width, 
                     c.height, c.dims_unit, c.weight_capacity, c.weight_unit, c.created_at, c.created_by_id, 
                     c.created_by_name, c.last_updated_at 
            HAVING COUNT(ct.tote_id) > 0 
            ORDER BY c.id ASC`
	default:
		query = `
            SELECT c.id, c.warehouse_id, c.cart_id, c.code, c.qr, c.alias, c.retain_tote, c.length, c.width, c.height, 
                   c.dims_unit, c.weight_capacity, c.weight_unit, c.created_at, c.created_by_id, c.created_by_name, 
                   c.last_updated_at, COUNT(ct.tote_id) AS tote_count 
            FROM cart c 
            LEFT JOIN cart_totes ct ON c.cart_id = ct.cart_id 
            WHERE c.warehouse_id = $1 
            GROUP BY c.id, c.cart_id 
            ORDER BY c.id ASC`
	}

	// Execute the query
	rows, err := c.DB.Query(ctx, query, whID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for carts")
	}
	defer rows.Close()

	// Process the result
	var carts []model.Cart
	for rows.Next() {
		var cart model.Cart
		err = rows.Scan(
			&cart.ID,
			&cart.WarehouseID,
			&cart.CartID,
			&cart.Code,
			&cart.QR,
			&cart.Alias,
			&cart.RetainTote,
			&cart.Length,
			&cart.Width,
			&cart.Height,
			&cart.DimsUnit,
			&cart.WeightCapacity,
			&cart.WeightUnit,
			&cart.CreatedAt,
			&cart.CreatedByID,
			&cart.CreatedByName,
			&cart.LastUpdatedAt,
			&cart.ToteCount,
		)
		if err != nil {
			return nil, errors.Wrap(err, "failed to scan cart")
		}

		// Filter carts based on mapCart if applicable
		if len(mapCart) > 0 {
			if _, ok := mapCart[cart.CartID.String()]; ok {
				continue
			}
		}
		carts = append(carts, cart)
	}

	return carts, nil
}

// GetCart returns a cart by cart_id
func (c *CartImpl) GetCart(cartID uuid.UUID) (*model.Cart, error) {
	// # Create Context
	ctx := context.Background()

	cart := &model.Cart{}
	// # Query cart
	query := `SELECT warehouse_id, cart_id, code, qr, alias, retain_tote, length, width, height, dims_unit, weight_capacity, weight_unit, created_at, created_by_id, created_by_name, last_updated_at FROM cart WHERE cart_id = $1`
	row := c.DB.QueryRow(ctx, query, cartID)
	err := row.Scan(
		&cart.WarehouseID,
		&cart.CartID,
		&cart.Code,
		&cart.QR,
		&cart.Alias,
		&cart.RetainTote,
		&cart.Length,
		&cart.Width,
		&cart.Height,
		&cart.DimsUnit,
		&cart.WeightCapacity,
		&cart.WeightUnit,
		&cart.CreatedAt,
		&cart.CreatedByID,
		&cart.CreatedByName,
		&cart.LastUpdatedAt,
	)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.New("Cart not found")
		}
		return nil, errors.Wrap(err, "Failed to scan cart")
	}

	// # Query cart items
	query = `SELECT client_id, client_name, operation_type, item_id, name, sku, scannable, batch_number, serial_number, expiration_date, quantity, picklist_id, picklist_code, order_id, order_code, created_at, created_by_id, created_by_name, last_updated_at FROM cart_items WHERE cart_id = $1`
	rows, err := c.DB.Query(ctx, query, cartID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for cart items")
	}
	defer rows.Close()
	var cartItems []*model.CartItem
	for rows.Next() {
		cartItem := &model.CartItem{}
		err = rows.Scan(
			&cartItem.ClientID,
			&cartItem.ClientName,
			&cartItem.OperationType,
			&cartItem.ItemID,
			&cartItem.Name,
			&cartItem.SKU,
			&cartItem.Scannable,
			&cartItem.BatchNumber,
			&cartItem.SerialNumber,
			&cartItem.ExpirationDate,
			&cartItem.Quantity,
			&cartItem.PicklistID,
			&cartItem.PicklistCode,
			&cartItem.OrderID,
			&cartItem.OrderCode,
			&cartItem.CreatedAt,
			&cartItem.CreatedByID,
			&cartItem.CreatedByName,
			&cartItem.LastUpdatedAt,
		)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to scan cart item")
		}
		cartItems = append(cartItems, cartItem)
	}
	cart.CartItems = cartItems

	// # Query cart totes
	query = `SELECT tote_id FROM cart_totes WHERE cart_id = $1`
	rows, err = c.DB.Query(ctx, query, cartID)
	if err != nil {
		return nil, errors.Wrap(err, "Failed to query for cart totes")
	}
	defer rows.Close()
	var toteIDs []uuid.UUID
	for rows.Next() {
		var toteID uuid.UUID
		err = rows.Scan(&toteID)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to scan cart tote")
		}
		toteIDs = append(toteIDs, toteID)
	}

	// # Query totes and tote items inside totes
	var totes []*model.Tote
	for _, toteID := range toteIDs {
		tote, err := c.App.Tote.GetTote(toteID)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to get tote")
		}
		totes = append(totes, tote)
	}
	cart.Totes = totes

	response, err := c.App.GrpcClient.Outbound.Client.GetWavePicklistInfo(ctx, &outbound_proto.GetWavePicklistInfoRequest{CartId: cartID.String()})
	if err != nil {
		return nil, errors.Wrap(err, "Failed to get picklist info")
	}
	var picklist *model.Picklist
	if len(response.GetPicklist()) > 0 {
		err := json.Unmarshal(response.GetPicklist(), &picklist)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to unmarshal picklist")
		}
		cart.PicklistInfo = picklist
	}

	return cart, nil
}

// PrepareCart prepares a cart by mapping it to totes
func (c *CartImpl) PrepareCart(data *schema.ValidatePrepareCart) (bool, error) {
	ctx := context.Background()

	// Create transaction
	tx, err := c.DB.Begin(ctx)
	if err != nil {
		return false, errors.Wrap(err, "Unable to begin transaction")
	}
	defer tx.Rollback(ctx)

	for _, toteID := range data.ToteIDs {
		// fetch tote code from tote table
		var code string
		query := `SELECT code FROM tote WHERE tote_id = $1`
		err := tx.QueryRow(ctx, query, toteID).Scan(&code)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "Failed to query for tote code")
		}

		// check if tote is empty or not
		var count int
		query = `SELECT COUNT(*) FROM tote_items where tote_id = $1`
		err = tx.QueryRow(ctx, query, toteID).Scan(&count)
		if err != nil {
			if err == pgx.ErrNoRows {
				// Tote is empty
				count = 0
			} else {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "Failed to query for tote items")
			}
		}
		if count > 0 {
			tx.Rollback(ctx)
			return false, errors.New(code + " is not empty")
		}

		// check if tote is already mapped to a cart
		var toteCartID *uuid.UUID
		query = `SELECT COUNT(*), cart_id FROM cart_totes WHERE tote_id = $1 GROUP BY cart_id`
		err = tx.QueryRow(ctx, query, toteID).Scan(&count, &toteCartID)
		if err != nil {
			if err == pgx.ErrNoRows {
				// Tote is not mapped to any cart
				count = 0
			} else {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "failed to query for tote cart mapping")
			}
		}
		if count > 0 {
			response, err := c.App.GrpcClient.Outbound.Client.GetWavePicklistInfo(ctx, &outbound_proto.GetWavePicklistInfoRequest{CartId: toteCartID.String()})
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "Failed to get picklist info")
			}
			if response.Status == "picking" {
				tx.Rollback(ctx)
				return false, errors.New(code + " is already mapped to a cart in picking state")
			}

			// remove tote from cart
			query := `DELETE FROM cart_totes WHERE tote_id = $1`
			_, err = tx.Exec(ctx, query, toteID)
			if err != nil {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "Failed to remove tote from cart")
			}

			resp, err := c.App.GrpcClient.Outbound.Client.AddRemoveToteFormPicklist(ctx, &outbound_proto.AddRemoveToteFormPicklistRequest{CartId: toteCartID.String(), ToteId: toteID.String(), ToteCode: code, Attach: false})
			if err != nil || !resp.GetSuccess() {
				tx.Rollback(ctx)
				return false, errors.Wrap(err, "Failed to remove tote from picklist")
			}
		}

		// map tote to cart
		query = `INSERT INTO cart_totes (cart_id, tote_id) VALUES ($1, $2)`
		_, err = tx.Exec(ctx, query, data.CartID, toteID)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "Failed to map tote to cart")
		}

		resp, err := c.App.GrpcClient.Outbound.Client.AddRemoveToteFormPicklist(ctx, &outbound_proto.AddRemoveToteFormPicklistRequest{CartId: data.CartID.String(), ToteId: toteID.String(), ToteCode: code, Attach: true})
		if err != nil || !resp.GetSuccess() {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "Failed to add tote to picklist")
		}
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "Failed to commit transaction, rolling back")
	}

	return true, nil
}

// UpdateCartAndToteItems updates cart and tote items
func (c *CartImpl) UpdateCartAndToteItems(tx pgx.Tx, ctx context.Context, quantity int32, req *inventory_proto.UpdateLocationForPickUpRequest, item *model.Inventory, orderID, orderCode string, cartID, toteID *uuid.UUID, cartCode, toteCode, batchNo, serialNo *string, expDate *time.Time) error {
	picklistID := req.GetPicklistId()
	now := time.Now().UTC()

	// check if tote id is not nil
	if toteID != nil {
		// check if cart is not mapped to the tote
		var count int
		query := `SELECT COUNT(*) FROM cart_totes WHERE tote_id = $1 AND cart_id = $2`
		err := tx.QueryRow(ctx, query, toteID, cartID).Scan(&count)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "failed to query for tote cart mapping")
		}
		// if not mapped rollback transaction
		if count == 0 {
			tx.Rollback(ctx)
			return errors.New(*toteCode + " is not mapped to " + *cartCode)
		}

		// check if tote is not associated with other order and picklist in tote_items
		query = `SELECT COUNT(*) FROM tote_items WHERE tote_id = $1 AND order_id != $2 AND picklist_id != $3`
		err = tx.QueryRow(ctx, query, toteID, orderID, picklistID).Scan(&count)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "failed to query for tote items")
		}
		// if exists rollback transaction
		if count > 0 {
			tx.Rollback(ctx)
			return errors.New(*toteCode + " is associated with other order or picklist")
		}

		// check if item is already present in the tote with same batch number, serial number, expiration date, order id and picklist id
		query = `SELECT COUNT(*) FROM tote_items WHERE tote_id = $1 AND item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL) AND order_id = $6 AND picklist_id = $7`
		err = tx.QueryRow(ctx, query, toteID, req.GetItemId(), batchNo, serialNo, expDate, orderID, picklistID).Scan(&count)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "failed to query for tote items")
		}
		// if exists update quantity in tote_items else insert new row
		if count > 0 {
			query := `UPDATE tote_items SET quantity = quantity + $1 WHERE tote_id = $2 AND item_id = $3 AND (batch_number = $4 OR $4 IS NULL) AND (serial_number = $5 OR $5 IS NULL) AND (expiration_date = $6 OR $6 IS NULL) AND order_id = $7 AND picklist_id = $8`
			commandTag, err := tx.Exec(ctx, query, quantity, toteID, req.GetItemId(), batchNo, serialNo, expDate, orderID, picklistID)
			if err != nil {
				tx.Rollback(ctx)
				return errors.Wrap(err, "failed to update tote items")
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return errors.New("tote item not updated")
			}

		} else {
			// Insert data into tote_items
			query := `INSERT INTO tote_items (warehouse_id, tote_id, tote_code, client_id, client_name, operation_type, item_id, name, sku, scannable, batch_number, serial_number, expiration_date, quantity, picklist_id, picklist_code, order_id, order_code, created_at, created_by_id, created_by_name, last_updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22)`
			commandTag, err := tx.Exec(ctx, query, req.GetWarehouseId(), toteID, toteCode, req.GetClientId(), req.GetClientName(), "picking", req.GetItemId(), item.Name, req.GetSku(), item.Scannable, batchNo, serialNo, expDate, quantity, picklistID, req.GetPicklistCode(), orderID, orderCode, now, req.GetUserId(), req.GetUsername(), now)
			if err != nil {
				tx.Rollback(ctx)
				return errors.Wrap(err, "failed to insert tote items")
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return errors.New("new row not inserted in tote items")
			}
		}

	} else {
		// check if cart is not associated with other order and picklist in cart_items
		var count int
		query := `SELECT COUNT(*) FROM cart_items WHERE cart_id = $1 AND order_id != $2 AND picklist_id != $3`
		err := tx.QueryRow(ctx, query, cartID, orderID, picklistID).Scan(&count)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "failed to query for cart items")
		}
		if count > 0 {
			tx.Rollback(ctx)
			return errors.New(*cartCode + " is associated with other order or picklist")
		}

		// check if item is already present in the cart with same batch number, serial number, expiration date, order id and picklist id
		query = `SELECT COUNT(*) FROM cart_items WHERE cart_id = $1 AND item_id = $2 AND (batch_number = $3 OR $3 IS NULL) AND (serial_number = $4 OR $4 IS NULL) AND (expiration_date = $5 OR $5 IS NULL) AND order_id = $6 AND picklist_id = $7`
		err = tx.QueryRow(ctx, query, cartID, req.GetItemId(), batchNo, serialNo, expDate, orderID, picklistID).Scan(&count)
		if err != nil {
			tx.Rollback(ctx)
			return errors.Wrap(err, "failed to query for cart items")
		}
		// if exists update quantity in cart_items else insert new row
		if count > 0 {
			query := `UPDATE cart_items SET quantity = quantity + $1 WHERE cart_id = $2 AND item_id = $3 AND (batch_number = $4 OR $4 IS NULL) AND (serial_number = $5 OR $5 IS NULL) AND (expiration_date = $6 OR $6 IS NULL) AND order_id = $7 AND picklist_id = $8`
			commandTag, err := tx.Exec(ctx, query, quantity, cartID, req.GetItemId(), batchNo, serialNo, expDate, orderID, picklistID)
			if err != nil {
				tx.Rollback(ctx)
				return errors.Wrap(err, "failed to update cart items")
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return errors.New("cart item not updated")
			}
		} else {
			// Insert data into cart_items
			query := `INSERT INTO cart_items (warehouse_id, cart_id, cart_code client_id, client_name, operation_type, item_id, name, sku, scannable, batch_number, serial_number, expiration_date, quantity, picklist_id, picklist_code, order_id, order_code, created_at, created_by_id, created_by_name, last_updated_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22)`
			commandTag, err := tx.Exec(ctx, query, req.GetWarehouseId(), cartID, cartCode, req.GetClientId(), req.GetClientName(), "picking", req.GetItemId(), item.Name, req.GetSku(), item.Scannable, batchNo, serialNo, expDate, quantity, picklistID, req.GetPicklistCode(), orderID, orderCode, now, req.GetUserId(), req.GetUsername(), now)
			if err != nil {
				tx.Rollback(ctx)
				return errors.Wrap(err, "failed to insert cart items")
			}
			if commandTag.RowsAffected() != 1 {
				tx.Rollback(ctx)
				return errors.New("new row not inserted in cart items")
			}
		}
	}

	return nil
}

func (c *CartImpl) GetOrderCartDetails(whID, orderID string) (*schema.OrderCartDetailsResponse, error) {
	query := `
		SELECT
			cart.cart_id,
			cart.code,
			tote_items.*
		FROM
			tote_items
			INNER JOIN cart_totes ON tote_items.tote_id = cart_totes.tote_id
			INNER JOIN cart ON cart_totes.cart_id = cart.cart_id
		WHERE
			tote_items.warehouse_id = $1
			AND tote_items.order_id = $2
	`
	rows, err := c.DB.Query(context.TODO(), query, whID, orderID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for tote items")
	}
	defer rows.Close()

	counter := 0
	response := &schema.OrderCartDetailsResponse{
		WarehouseID: &whID,
		OrderID:     &orderID,
	}

	for rows.Next() {
		var row schema.OrderCartDetails
		err := rows.Scan(
			&row.CartID,
			&row.Code,
			&row.ID,
			&row.WarehouseID,
			&row.ToteID,
			&row.ToteCode,
			&row.ClientID,
			&row.ClientName,
			&row.OperationType,
			&row.ItemID,
			&row.Name,
			&row.SKU,
			&row.Scannable,
			&row.BatchNumber,
			&row.SerialNumber,
			&row.ExpirationDate,
			&row.Quantity,
			&row.PicklistID,
			&row.PicklistCode,
			&row.OrderID,
			&row.OrderCode,
			&row.CreatedAt,
			&row.CreatedByID,
			&row.CreatedByName,
			&row.LastUpdatedAt,
		)
		if err != nil {
			return nil, errors.Wrap(err, "unable to read resultant rows into order cart details schema")
		}

		if counter == 0 {
			response.OrderCode = row.OrderCode
			response.Cart = &schema.Cart{
				ID:   *row.CartID,
				Code: *row.Code,
			}
			response.Tote = &schema.Tote{
				ID:   *row.ToteID,
				Code: *row.ToteCode,
			}
		}
		response.Tote.Items = append(response.Tote.Items, &model.ToteItem{
			ID:             row.ID,
			WarehouseID:    row.WarehouseID,
			ToteID:         row.ToteID,
			ToteCode:       row.ToteCode,
			ClientID:       row.ClientID,
			ClientName:     row.ClientName,
			OperationType:  row.OperationType,
			ItemID:         row.ItemID,
			Name:           row.Name,
			SKU:            row.SKU,
			Scannable:      row.Scannable,
			BatchNumber:    row.BatchNumber,
			SerialNumber:   row.SerialNumber,
			ExpirationDate: row.ExpirationDate,
			Quantity:       row.Quantity,
			PicklistID:     row.PicklistID,
			PicklistCode:   row.PicklistCode,
			OrderID:        row.OrderID,
			OrderCode:      row.OrderCode,
			CreatedAt:      row.CreatedAt,
			CreatedByID:    row.CreatedByID,
			CreatedByName:  row.CreatedByName,
			LastUpdatedAt:  row.LastUpdatedAt,
		})

		counter++
	}

	if row_err := rows.Err(); row_err != nil {
		return nil, errors.Wrap(row_err, "unable to read rows")
	}

	return response, nil
}

func (c *CartImpl) RemoveToteFromCart(v *schema.ValidateRemoveToteFromCart) (bool, error) {

	ctx := context.Background()

	// Create transaction
	tx, err := c.DB.Begin(ctx)
	if err != nil {
		return false, errors.Wrap(err, "Unable to begin transaction")
	}
	defer tx.Rollback(ctx)

	// fetch tote code from tote table
	var code string
	query := `SELECT code FROM tote WHERE tote_id = $1`
	err = tx.QueryRow(ctx, query, v.ToteID).Scan(&code)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "Failed to query for tote code")
	}

	// check if tote is empty or not
	var count int
	query = `SELECT COUNT(*) FROM tote_items where tote_id = $1 GROUP BY tote_code`
	err = tx.QueryRow(ctx, query, v.ToteID).Scan(&count)
	if err != nil {
		if err == pgx.ErrNoRows {
			// Tote is empty
			count = 0
		} else {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "Failed to query for tote items")
		}
	}
	if count > 0 {
		tx.Rollback(ctx)
		return false, errors.New(code + " is not empty!")
	}

	// Check if tote is associated to the cart
	query = `SELECT COUNT(*) FROM cart_totes WHERE tote_id = $1 AND cart_id = $2`
	err = tx.QueryRow(ctx, query, v.ToteID, v.CartID).Scan(&count)
	if err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "Failed to query for tote cart mapping")
	}
	if count > 0 {
		// remove tote from cart
		query := `DELETE FROM cart_totes WHERE tote_id = $1 AND cart_id = $2`
		_, err := tx.Exec(ctx, query, v.ToteID, v.CartID)
		if err != nil {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "Failed to remove tote from cart")
		}

		resp, err := c.App.GrpcClient.Outbound.Client.AddRemoveToteFormPicklist(ctx, &outbound_proto.AddRemoveToteFormPicklistRequest{CartId: v.CartID.String(), ToteId: v.ToteID.String(), ToteCode: code, Attach: false})
		if err != nil || !resp.GetSuccess() {
			tx.Rollback(ctx)
			return false, errors.Wrap(err, "Failed to remove tote from picklist")
		}
	} else {
		tx.Rollback(ctx)
		return false, errors.New("Tote is not associated with the cart")
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return false, errors.Wrap(err, "Failed to commit transaction, rolling back")
	}

	return true, nil
}

// Print Cart
func (c *CartImpl) PrintCart(data *schema.ValidatePrintCart) ([]string, error) {

	ctx := context.Background()

	// Create transaction
	tx, err := c.DB.Begin(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "Unable to begin transaction")
	}
	defer tx.Rollback(ctx)

	// # QR Struct
	var QR schema.QRCode

	// # QR Data Struct
	Data := []schema.QRData{}

	var qrCodes []string

	if data.OnlyPrintCart {
		// # Query cart
		query := `SELECT code, qr FROM cart WHERE cart_id = $1`
		row := tx.QueryRow(ctx, query, data.CartID)
		var cartCode, cartQR string
		err := row.Scan(&cartCode, &cartQR)
		if err != nil {
			if err == pgx.ErrNoRows {
				tx.Rollback(ctx)
				return nil, errors.New("Cart not found")
			}
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Failed to scan cart")
		}

		// # CSV - QR Code Strings | PDF - QR Code Images
		if data.FileType == "csv" {
			// # Append to QR codes list
			qrCodes = append(qrCodes, cartQR)
		} else {
			// # Generate QR code PNG image
			bufBytes, err := c.App.Utils.GenerateQrCode(cartQR)
			if err != nil {
				tx.Rollback(ctx)
				return nil, errors.Wrap(err, "Failed to generate QR code for cart")
			}

			// # Encode image to base64 string
			imgBase64Str := base64.StdEncoding.EncodeToString(bufBytes)

			// # Construct QR
			qr := schema.QRData{
				QRcode: imgBase64Str,
				Code:   cartCode,
			}

			// # Append to QR Data struct
			Data = append(Data, qr)
		}
	} else {
		// # Query cart totes
		query := `SELECT tote_id FROM cart_totes WHERE cart_id = $1`
		rows, err := tx.Query(ctx, query, data.CartID)
		if err != nil {
			if err == pgx.ErrNoRows {
				tx.Rollback(ctx)
				return nil, errors.New("Cart is empty")
			}
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Failed to query for cart totes")
		}
		defer rows.Close()
		var toteIDs []uuid.UUID
		for rows.Next() {
			var toteID uuid.UUID
			err = rows.Scan(&toteID)
			if err != nil {
				return nil, errors.Wrap(err, "Failed to scan cart tote")
			}
			toteIDs = append(toteIDs, toteID)
		}
		if len(toteIDs) == 0 {
			tx.Rollback(ctx)
			return nil, errors.New("Cart is empty")
		}

		// Fetch all totes with toteIDs
		query = `SELECT code, qr FROM tote WHERE tote_id = ANY($1)`
		rows, err = tx.Query(ctx, query, toteIDs)
		if err != nil {
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, "Failed to query for totes")
		}
		defer rows.Close()

		for rows.Next() {
			var tote model.Tote
			err = rows.Scan(&tote.Code, &tote.QR)
			if err != nil {
				return nil, errors.Wrap(err, "Failed to scan tote")
			}

			// # CSV - QR Code Strings | PDF - QR Code Images
			if data.FileType == "csv" {
				// # Append to QR codes list
				qrCodes = append(qrCodes, *tote.QR)
			} else {
				// # Generate QR code PNG image
				bufBytes, err := c.App.Utils.GenerateQrCode(*tote.QR)
				if err != nil {
					tx.Rollback(ctx)
					return nil, errors.Wrap(err, "Failed to generate QR code for tote")
				}

				// # Encode image to base64 string
				imgBase64Str := base64.StdEncoding.EncodeToString(bufBytes)

				// # Construct QR
				qr := schema.QRData{
					QRcode: imgBase64Str,
					Code:   *tote.Code,
				}

				// # Append to QR Data struct
				Data = append(Data, qr)
			}
		}
	}

	// # Validate if the label type is one of the allowed values if the file type is pdf
	if (data.FileType == "pdf") && (data.LabelType != "minimal_1.25x3.5") && (data.LabelType != "minimal_1.5x4") && (data.LabelType != "minimal_4x6") {
		err := errors.New("label_type must be one of [minimal_1.25x3.5 minimal_1.5x4 minimal_4x6]")
		tx.Rollback(ctx)
		return nil, err
	}

	if data.FileType == "pdf" {
		// # Construct Final QR Struct
		QR.WarehouseName = data.WarehouseName
		QR.Data = &Data

		// # Template File Variables
		var file, path string

		// # Check Label Type
		if data.LabelType == "minimal_1.25x3.5" {
			// # Label Type: minimal_1.25x3.5
			// # Template File Name
			file = c.App.Config.PDFConfig.LPLabel1x3MinimalTemplateFileName
			// # Template File Path
			path = c.App.Config.PDFConfig.LPLabel1x3MinimalTemplateFilePath
		} else if data.LabelType == "minimal_1.5x4" {
			// # Label Type: minimal_1.5x4
			// # Template File Name
			file = c.App.Config.PDFConfig.LPLabel1x4MinimalTemplateFileName
			// # Template File Path
			path = c.App.Config.PDFConfig.LPLabel1x4MinimalTemplateFilePath
		} else {
			// # Label Type: minimal_4x6
			// # Template File Name
			file = c.App.Config.PDFConfig.LPLabel4x6MinimalTemplateFileName
			// # Template File Path
			path = c.App.Config.PDFConfig.LPLabel4x6MinimalTemplateFilePath
		}

		// # Generate QR PDF
		buf, err := GenerateQrPDF(&QR, file, path, data.LabelType)
		if err != nil {
			msg := "Failed to generate PDF"
			c.Logger.Err(err).Msg(msg)
			tx.Rollback(ctx)
			return nil, errors.Wrap(err, msg)
		}

		// # Base File Name
		baseName := "wms_tote_label.pdf"
		if data.OnlyPrintCart {
			baseName = "wms_cart_label.pdf"
		}

		// # File Name Separator
		sep := "_"

		// # Construct file name for PDF file
		fileName := data.WarehouseName + sep + data.LabelType + sep + baseName

		// # S3 Bucket Name
		bucket := c.App.Config.AWSConfig.TempBucket

		// # Upload PDF File to S3
		fileUrl, err := c.App.SSS.AddQRPDFToS3(fileName, bucket, buf.Bytes())
		if err != nil {
			tx.Rollback(ctx)
			err = errors.Wrap(err, "Failed to upload QR codes PDF to S3!")
			return nil, err
		}

		// # Return S3 URL of PDF file
		return []string{fileUrl}, nil
	}

	// Commit transaction
	if tx_err := tx.Commit(ctx); tx_err != nil {
		tx.Rollback(ctx)
		return nil, errors.Wrap(err, "failed to commit transaction, rolling back")
	}

	return qrCodes, nil
}
