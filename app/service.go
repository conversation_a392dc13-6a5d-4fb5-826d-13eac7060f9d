package app

// InitService this initializes all the busines logic services
func InitService(a *App) {

	a.Utils = InitUtils(&UtilsOpts{
		App: a,
	})

	a.Sample = InitSample(&SampleOpts{
		App:    a,
		DB:     a.PostgresDB.Conn(),
		Logger: a.<PERSON>,
	})

	a.Container = InitContainer(&ContainerOpts{
		App:    a,
		DB:     a.PostgresDB.Conn(),
		Logger: a.<PERSON>,
	})

	a.Location = InitLocation(&LocationOpts{
		App:    a,
		DB:     a.PostgresDB.Conn(),
		Logger: a.<PERSON>gger,
	})

	a.Inventory = InitInventory(&InventoryOpts{
		App:    a,
		DB:     a.PostgresDB.Conn(),
		Logger: a.<PERSON>,
	})

	a.Operations = InitOperations(&OperationsOpts{
		App:    a,
		DB:     a.PostgresDB.Conn(),
		Logger: a.<PERSON>gger,
	})

	a.Transaction = InitTransaction(&TransactionOpts{
		App:    a,
		DB:     a.PostgresDB.Conn(),
		Logger: a.<PERSON><PERSON>,
	})

	a.Notification = InitNotification(&NotificationOpts{
		App:    a,
		DB:     a.PostgresDB.Conn(),
		Logger: a.Logger,
	})

	a.Script = InitScript(&ScriptOpts{
		App:    a,
		DB:     a.PostgresDB.Conn(),
		Logger: a.Logger,
	})

	a.Cart = InitCart(&CartOpts{
		App:    a,
		DB:     a.PostgresDB.Conn(),
		Logger: a.Logger,
	})

	a.Tote = InitTote(&ToteOpts{
		App:    a,
		DB:     a.PostgresDB.Conn(),
		Logger: a.Logger,
	})

	a.CronManager = InitCronManager(&CronManagerOpts{
		App:    a,
		DB:     a.PostgresDB.Conn(),
		Logger: a.Logger,
	})

}
