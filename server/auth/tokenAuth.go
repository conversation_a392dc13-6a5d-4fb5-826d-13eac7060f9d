//go:generate $GOPATH/bin/mockgen -destination=../../mock/mock_tokenAuth.go -package=mock inventory/server/auth TokenAuth,Claim

package auth

import (
	"encoding/json"

	"github.com/dgrijalva/jwt-go"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// HeaderRequestID header name to look for request id for request tracking
const HeaderRequestID = "X-Request-ID"

// TokenAuth defines method for implementing token authentication
type TokenAuth interface {
	SignToken(Claim) (string, error)
	VerifyToken(string) (Claim, string, error)
}

// Claim defines custom token claim type methods.
// Note: this claim is used to automatically parse token into struct when a request has jwt token in header
type Claim interface {
	ToJSON() string
	GetJWTToken() *jwt.Token
	IsGranted(primitive.ObjectID, string) bool
	IsClient(primitive.ObjectID, primitive.ObjectID) bool
}

// JWTToken represents jwt encoded token string for json format
type JWTToken struct {
	Token string `json:"token"`
}

// ToJSON := converting struct to json
func (jwt *JWTToken) ToJSON() string {
	json, _ := json.Marshal(jwt.Token)
	return string(json)
}
