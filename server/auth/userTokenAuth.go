package auth

import (
	"encoding/json"
	"inventory/server/config"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TokenAuthentication contains authentication related attributes and methods
type TokenAuthentication struct {
	Session SessionAuth
	Config  *config.TokenAuthConfig
	User    *UserAuth
}

// NewTokenAuthentication returns new instance of TokenAuthentication
func NewTokenAuthentication(c *config.TokenAuthConfig, s SessionAuth) *TokenAuthentication {
	return &TokenAuthentication{Config: c, Session: s}
}

// UserAuth contains encoded token info and user info
type UserAuth struct {
	UserClaim *UserClaim
	JWTToken  JWTToken
}

// UserClaim contains fields related to User model to be added in JWT Claims
type UserClaim struct {
	ID                  primitive.ObjectID   `json:"_id,omitempty" bson:"_id,omitempty"`
	Gender              string               `json:"gender,omitempty" bson:"gender,omitempty"`
	IsOrganizationOwner bool                 `json:"is_organization_owner,omitempty" bson:"is_organization_owner,omitempty"`
	Session             []UserSessionDetails `json:"session,omitempty" bson:"session,omitempty"`
	FullName            string               `json:"full_name,omitempty" bson:"full_name,omitempty"`
	Email               string               `json:"email,omitempty" bson:"email,omitempty"`
	Username            string               `json:"username,omitempty" bson:"username,omitempty"`
	OrganizationID      *primitive.ObjectID  `json:"organization_id,omitempty" bson:"organization_id,omitempty"`
	EmailVerified       bool                 `json:"email_verified,omitempty" bson:"email_verified,omitempty"`
	CreatedAt           *time.Time           `json:"created_at,omitempty" bson:"created_at,omitempty"`
	CreatedBy           *UserModel           `json:"created_by,omitempty" bson:"created_by,omitempty"`
	UpdatedAt           *time.Time           `json:"updated_at,omitempty" bson:"updated_at,omitempty"`

	ActivePlatformID map[string]primitive.ObjectID                 `json:"active_platform_id,omitempty" bson:"active_platform_id,omitempty"`
	Services         map[string]map[primitive.ObjectID]ServiceData `json:"services,omitempty" bson:"services,omitempty"`

	IsDeactivated      bool       `json:"is_deactivated,omitempty" bson:"is_deactivated,omitempty"`
	DeactivatedAt      *time.Time `json:"deactivated_at,omitempty" bson:"deactivated_at,omitempty"`
	DeactivatedBy      string     `json:"deactivated_by,omitempty" bson:"deactivated_by,omitempty"`
	DeactivationReason string     `json:"deactivation_reason,omitempty" bson:"deactivation_reason,omitempty"`

	SessionID string `json:"session_id,omitempty" bson:"session_id,omitempty"`
	TokenID   string `json:"token_id,omitempty" bson:"token_id,omitempty"`
	Expiry    int64  `json:"exp,omitempty" bson:"exp,omitempty"`
	jwt.StandardClaims
}

type ServiceData struct {
	IsPrimary        bool                 `json:"is_primary,omitempty" bson:"is_primary,omitempty"`
	IsOwner          bool                 `json:"is_owner,omitempty" bson:"is_owner,omitempty"`
	OrganizationID   *primitive.ObjectID  `json:"organization_id,omitempty" bson:"organization_id,omitempty"`
	WarehouseID      *primitive.ObjectID  `json:"warehouse_id,omitempty" bson:"warehouse_id,omitempty"`
	WarehouseName    string               `json:"warehouse_name,omitempty" bson:"warehouse_name,omitempty"`
	AccessProfile    string               `json:"access_profile,omitempty" bson:"access_profile,omitempty"`
	AddedAccess      []string             `json:"added_access,omitempty" bson:"added_access,omitempty"`
	RemovedAccess    []string             `json:"removed_access,omitempty" bson:"removed_access,omitempty"`
	AuthorizedAccess []string             `json:"authorized_access,omitempty" bson:"authorized_access,omitempty"`
	ClientAccess     []primitive.ObjectID `json:"client_access,omitempty" bson:"client_access,omitempty"`
	ClientID         *primitive.ObjectID  `json:"client_id,omitempty" bson:"client_id,omitempty"`
	ClientName       string               `json:"client_name,omitempty" bson:"client_name,omitempty"`
}

// UserOrganization contans reference to Organization
type UserOrganization struct {
	OrganizationID primitive.ObjectID `json:"_id"`
	Role           string             `json:"role" `
	AddedAt        *time.Time         `json:"added_at"`
}

// UserWarehouse contans reference to Team
type UserWarehouse struct {
	WarehouseID primitive.ObjectID `json:"_id,omitempty"`
	Alias       string             `json:"alias,omitempty" `
	Role        string             `json:"role"`
	AddedAt     *time.Time         `json:"added_at"`
}

// ClientData contains client info of user
type ClientData struct {
	ClientID   primitive.ObjectID `json:"client_id,omitempty" bson:"client_id,omitempty"`
	ClientName string             `json:"client_name,omitempty" bson:"client_name,omitempty"`
}

// UserSessionDetails contains details about User Session
type UserSessionDetails struct {
	SessionID string     `json:"session_id,omitempty"`
	UserAgent string     `json:"user_agent,omitempty"`
	Platform  string     `json:"platform,omitempty"`
	Token     string     `json:"token,omitempty"`
	CreatedAt *time.Time `json:"created_at,omitempty"`
}

// CreatedBy contains info about user who is creating something
type UserModel struct {
	UserID   primitive.ObjectID `json:"user_id,omitempty" bson:"user_id,omitempty"`
	Username string             `json:"username,omitempty" bson:"username,omitempty"`
	Name     string             `json:"name,omitempty" bson:"name,omitempty"`
	Email    string             `json:"email,omitempty" bson:"email,omitempty"`
}

// GetJWTToken return jwt.Token with claimInfo from user claim fields
func (uc *UserClaim) GetJWTToken() *jwt.Token {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, uc)
	return token
}

// ToJSON := converting struct to json
func (uc *UserClaim) ToJSON() string {
	json, _ := json.Marshal(uc)
	return string(json)
}

// IsGranted checks user access control permissions
func (uc *UserClaim) IsGranted(Id primitive.ObjectID, str string) bool {
	for key := range uc.Services {
		if _, ok := uc.Services[key][Id]; ok {
			if len(uc.Services[key][Id].AuthorizedAccess) > 0 {
				if str != "" {
					for _, v := range uc.Services[key][Id].AuthorizedAccess {
						if v == str {
							return true
						}
					}
					return false
				}
			}
		}
	}
	return true
}

// IsClient checks user client restriction
func (uc *UserClaim) IsClient(Id, clientId primitive.ObjectID) bool {
	for key := range uc.Services {
		if _, ok := uc.Services[key][Id]; ok {
			if len(uc.Services[key][Id].ClientAccess) > 0 {
				if clientId != primitive.NilObjectID {
					for _, v := range uc.Services[key][Id].ClientAccess {
						if v == clientId {
							return true
						}
					}
					return false
				}
			}
		}
	}
	return true
}

// SignToken sign and encodes jwt.Token as a string
func (t *TokenAuthentication) SignToken(claim Claim) (string, error) {
	userClaim := claim.(*UserClaim)
	if t.Config.JWTExpiresAt != 0 {
		expirationTime := time.Now().Add(time.Duration(t.Config.JWTExpiresAt) * time.Minute)
		userClaim.StandardClaims.ExpiresAt = expirationTime.Unix()
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, userClaim)
	tokenString, _ := token.SignedString([]byte(t.Config.JWTSignKey))
	return tokenString, nil
}

// VerifyToken first verifies the authenticity of the jwt token string and then parse the token string into struct
func (t *TokenAuthentication) VerifyToken(tokenString string) (Claim, string, error) {
	uc := UserClaim{}
	newJwt := new(jwt.Parser)
	_, _, err := newJwt.ParseUnverified(tokenString, &uc)
	if err != nil {
		return nil, "", errors.Wrap(err, "Invalid token, failed to parse token")
	}

	// Get session data from redis using session_id
	var session_data *UserClaim
	session_data_str, err := t.Session.GetToken(uc.SessionID)
	if err != nil {
		return nil, "", errors.Wrap(err, "session not found")
	}

	err = json.Unmarshal([]byte(session_data_str), &session_data)
	if err != nil {
		return nil, "", errors.Wrap(err, "cannot unmarshal session data")
	}

	return session_data, uc.SessionID, nil
}

// GetClaim returns token claim
func (t *TokenAuthentication) GetClaim() Claim {
	return t.User.UserClaim
}

// SetClaim sets token claim
func (t *TokenAuthentication) SetClaim(uc Claim) {
	if uc == nil {
		return
	}
	t.User = &UserAuth{
		UserClaim: uc.(interface{}).(*UserClaim),
	}
}
