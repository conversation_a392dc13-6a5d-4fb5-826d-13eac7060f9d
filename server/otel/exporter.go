package otel

import (
	"context"    // For managing request-scoped values, cancellation signals, and deadlines.
	"crypto/tls" // For configuring TLS options, like certificates.
	"inventory/server/config"
	"time"

	// OpenTelemetry imports for setting up tracing and exporting telemetry data.
	"go.opentelemetry.io/otel"                                        // Core OpenTelemetry APIs for managing tracers.
	"go.opentelemetry.io/otel/attribute"                              // For creating and managing trace attributes.
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp" // HTTP trace exporter for OpenTelemetry Protocol (OTLP).
	"go.opentelemetry.io/otel/propagation"                            // For managing context propagation formats.
	"go.opentelemetry.io/otel/sdk/resource"                           // For defining resources that describe an entity producing telemetry.
	"go.opentelemetry.io/otel/sdk/trace"                              // For configuring tracing, like sampling and processors.
	semconv "go.opentelemetry.io/otel/semconv/v1.24.0"                // Semantic conventions for resource attributes.
)

func SetupTracer(oc *config.OTelConfig) (func(context.Context) error, error) {
	ctx := context.Background()
	return InstallExportPipeline(ctx, oc) // Setup and return the export pipeline for telemetry data.
}

func Resource(oc *config.OTelConfig) *resource.Resource {
	// Defines resource with service name, version, and environment.
	return resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceNameKey.String(oc.Service),
		semconv.ServiceVersionKey.String(oc.ServiceVersion),
		attribute.String("environment", oc.Env),
	)
}

func InstallExportPipeline(ctx context.Context, oc *config.OTelConfig) (func(context.Context) error, error) {
	// Sets up OTLP HTTP exporter with endpoint, headers, and TLS config.
	exporter, err := otlptracehttp.New(ctx,
		otlptracehttp.WithEndpoint(oc.OtlpEndpoint),
		otlptracehttp.WithHeaders(map[string]string{
			"Authorization":   oc.BearerToken,
			"X-AXIOM-DATASET": oc.Dataset,
		}),
		otlptracehttp.WithTLSClientConfig(&tls.Config{}),
	)
	if err != nil {
		return nil, err
	}

	// Configures the tracer provider with the exporter and resource.
	tracerProvider := trace.NewTracerProvider(
		trace.WithBatcher(exporter,
			trace.WithMaxExportBatchSize(1),             // Export after a single span
			trace.WithBatchTimeout(50*time.Millisecond), // Short batch timeout),
		),
		trace.WithSampler(trace.AlwaysSample()), // Sample everything for testing
		trace.WithResource(Resource(oc)),
	)
	otel.SetTracerProvider(tracerProvider)

	// Sets global propagator to W3C Trace Context and Baggage.
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	))

	return tracerProvider.Shutdown, nil // Returns a function to shut down the tracer provider.
}
