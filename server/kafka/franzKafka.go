package kafka

import (
	"context"
	"fmt"
	"inventory/server/config"

	"github.com/rs/zerolog"

	"github.com/twmb/franz-go/pkg/kgo"
	"github.com/twmb/franz-go/pkg/sasl/plain"
)

type KafkaProducer struct {
	Client *kgo.Client
	Logger *zerolog.Logger
}

type KafkaProducerParams struct {
	Logger *zerolog.Logger
	Config *config.ProducerConfig
}

type KafkaConsumer struct {
}

type KafkaConsumerParams struct {
	Logger *zerolog.Logger
	Config *config.ConsumerConfig
}

// Init Producer
func NewKafkaProducer(p *KafkaProducerParams) *KafkaProducer {
	auth := plain.Auth{
		User: p.Config.Username,
		Pass: p.Config.Password,
	}

	opts := []kgo.Opt{
		kgo.SeedBrokers(p.Config.Brokers...),
		kgo.SASL(auth.AsMechanism()),
		kgo.RequiredAcks(kgo.AllISRAcks()),
		kgo.DefaultProduceTopic(p.Config.Topic),
		kgo.WithLogger(&zerologAdapter{l: p.Logger}),
	}

	cl, err := kgo.NewClient(opts...)
	if err != nil {
		fmt.Printf("failed to create client: %v", err)
	}

	return &KafkaProducer{Client: cl, Logger: p.Logger}
}

func (p *KafkaProducer) Produce(record *kgo.Record) {
	ctx := context.Background()
	if err :=  p.Client.ProduceSync(ctx, record).FirstErr(); err != nil {
	}
}

func NewKafkaConsumer() *KafkaConsumer {
	return &KafkaConsumer{}
}

// zerologAdapter wraps zerolog.Logger to satisfy kgo.Logger.
type zerologAdapter struct {
	l *zerolog.Logger
}

func (z *zerologAdapter) Level() kgo.LogLevel {
	return kgo.LogLevelDebug // change if you want less
}

func (z *zerologAdapter) Log(level kgo.LogLevel, msg string, keyvals ...any) {
	// Convert franz-go's keyvals into structured zerolog fields
	ev := z.l.With().Fields(map[string]any{}).Logger()
	switch level {
	case kgo.LogLevelError:
		ev.Error().Fields(kvsToMap(keyvals...)).Msg(msg)
	case kgo.LogLevelWarn:
		ev.Warn().Fields(kvsToMap(keyvals...)).Msg(msg)
	case kgo.LogLevelInfo:
		ev.Info().Fields(kvsToMap(keyvals...)).Msg(msg)
	default:
		ev.Debug().Fields(kvsToMap(keyvals...)).Msg(msg)
	}
}

func kvsToMap(keyvals ...any) map[string]any {
	m := make(map[string]any, len(keyvals)/2)
	for i := 0; i < len(keyvals)-1; i += 2 {
		k, ok := keyvals[i].(string)
		if !ok {
			k = fmt.Sprintf("key%d", i/2)
		}
		m[k] = keyvals[i+1]
	}
	return m
}
