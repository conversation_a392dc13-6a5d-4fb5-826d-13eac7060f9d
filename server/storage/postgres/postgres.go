package postgres

import (
	"context"
	"fmt"
	"inventory/server/config"
	"log"
	"os"

	"github.com/jackc/pgx/v4/pgxpool"
)

type PostgresDB struct {
	pool *pgxpool.Pool
}

func ConnectionURL(c *config.PostgresConfig) string {
	// "postgres://username:password@localhost:5432/database_name"
	url := fmt.Sprintf("postgres://%s:%s@%s:%s/%s", c.User, c.Password, c.Host, c.Port, c.DBName)
	return url
}

func NewPostgresDB(c *config.PostgresConfig) *PostgresDB {

	conf, err := pgxpool.ParseConfig(ConnectionURL(c))
	if err != nil {
		log.Fatalf("cannot load postgres config connection failed: %s", err)
		os.Exit(1)
	}
	conf.MaxConns = 5
	conf.MaxConnIdleTime = 5

	pool, err := pgxpool.ConnectConfig(context.Background(), conf)
	if err != nil {
		log.Fatalf("postgres connection failed: %s", err)
		os.Exit(1)
	}

	return &PostgresDB{
		pool: pool,
	}
}

func (p *PostgresDB) ClosePostgresDB() {
	p.pool.Close()
}

func (p *PostgresDB) Conn() *pgxpool.Pool {
	return p.pool
}
