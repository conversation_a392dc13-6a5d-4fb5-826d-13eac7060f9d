package config

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/spf13/viper"
)

// Config struct stores entire project configurations
type Config struct {
	ServerConfig     ServerConfig     `mapstructure:"server"`
	SessionConfig    SessionConfig    `mapstructure:"session"`
	APIConfig        APIConfig        `mapstructure:"api"`
	APPConfig        APPConfig        `mapstructure:"app"`
	KafkaConfig      KafkaConfig      `mapstructure:"kafka"`
	LoggerConfig     LoggerConfig     `mapstructure:"logger"`
	MongoDBConfig    MongoDBConfig    `mapstructure:"mongodb"`
	PostgresConfig   PostgresConfig   `mapstructure:"postgres"`
	RedisConfig      RedisConfig      `mapstructure:"redis"`
	MiddlewareConfig MiddlewareConfig `mapstructure:"middleware"`
	TokenAuthConfig  TokenAuthConfig  `mapstructure:"token"`
	AWSConfig        AWSConfig        `mapstructure:"aws"`
	CORSConfig       CORSConfig       `mapstructure:"cors"`
	FedexConfig      FedexConfig      `mapstructure:"fedex"`
	PDFConfig        PDFConfig        `mapstructure:"pdf"`
	GrpcAddr         GrpcAddr         `mapstructure:"grpcAddr"`
	SentryConfig     SentryConfig     `mapstructure:"sentry"`
	Notification     Notification     `mapstructure:"notification"`
	ShortLoop        ShortLoop        `mapstructure:"shortloop"`
	OTelConfig       OTelConfig       `mapstructure:"otel"`
}

type OTelConfig struct {
	Service        string `mapstructure:"service"`
	ServiceVersion string `mapstructure:"service_version"`
	OtlpEndpoint   string `mapstructure:"otlp_endpoint"`
	BearerToken    string `mapstructure:"bearer_token"`
	Dataset        string `mapstructure:"dataset"`
	Env            string `mapstructure:"env"`
}

type ShortLoop struct {
	AuthKey string `mapstructure:"authKey"`
}

type Notification struct {
	APIKey string `mapstructure:"apiKey"`
}

// SentryConfig contains sentry related configurations

type SentryConfig struct {
	DNS string `mapstructure:"dns"`
}

type PostgresConfig struct {
	Host     string `mapstructure:"host"`
	Port     string `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"dbname"`
}

// Stores addresses of all service servers
type GrpcAddr struct {
	Entity       string `mapstructure:"entity"`
	Core         string `mapstructure:"core"`
	Outbound     string `mapstructure:"outbound"`
	Inbound      string `mapstructure:"inbound"`
	Integrations string `mapstructure:"integrations"`
	Reporting    string `mapstructure:"reporting"`
}

// ServerConfig has only server specific configuration
type ServerConfig struct {
	ListenAddr     string        `mapstructure:"listenAddr"`
	Port           string        `mapstructure:"port"`
	GrpcPort       string        `mapstructure:"grpcPort"`
	ReadTimeout    time.Duration `mapstructure:"readTimeout"`
	WriteTimeout   time.Duration `mapstructure:"writeTimeout"`
	CloseTimeout   time.Duration `mapstructure:"closeTimeout"`
	Env            string        `mapstructure:"env"`
	GrpcEnv        string        `mapstructure:"grpcEnv"`
	UseMemoryStore bool          `mapstructure:"useMemoryStore"`
}

type SessionConfig struct {
	CookieConfig CookieConfig `mapstructure:"cookie"`
	RedisConfig  RedisConfig
}

type CookieConfig struct {
	Name     string `mapstructure:"name"`
	Path     string `mapstructure:"path"`
	HttpOnly bool   `mapstructure:"httpOnly"`
	Domain   string `mapstructure:"domain"`
	Secure   bool   `mapstructure:"secure"`
}

// FedEx related configurations
type FedexConfig struct {
	Url string `mapstructure:"url"`
}

// APIConfig contains api package related configurations
type APIConfig struct {
	AWSConfig          AWSConfig
	SessionConfig      SessionConfig
	Notification       Notification
	Mode               string `mapstructure:"mode"`
	EnableTestRoute    bool   `mapstructure:"enableTestRoute"`
	EnableMediaRoute   bool   `mapstructure:"enableMediaRoute"`
	EnableStaticRoute  bool   `mapstructure:"enableStaticRoute"`
	MaxRequestDataSize int    `mapstructure:"maxRequestDataSize"`
}

// APPConfig contains api package related configurations
type APPConfig struct {
	MongoDBConfig   MongoDBConfig
	AWSConfig       AWSConfig
	UserConfig      ServiceConfig `mapstructure:"user"`
	Env             string        `mapstructure:"env"`
	TokenAuthConfig TokenAuthConfig
	PDFConfig       PDFConfig
	Notification    Notification
	//INBOUND CONFIGs
	DeleteContainerProducerConfig ProducerConfig `mapstructure:"deleteContainerProducerConfig"`
	DeleteContainerConsumerConfig ListenerConfig `mapstructure:"deleteContainerConsumerConfig"`
	InvoicingProducerConfig       ProducerConfig `mapstructure:"invoicingProducerConfig"`

	//Notification
	TopicNotificationProducerConfig      ProducerConfig `mapstructure:"topicNotificationProducerConfig"`
	SubscriberNotificationProducerConfig ProducerConfig `mapstructure:"subscriberNotificationProducerConfig"`

	// Tracking
	TrackingConsumerConfig ListenerConfig `mapstructure:"trackingConsumerConfig"`

	// Fedex
	FedexConfig FedexConfig
}

// # PDF Config contains path and name to the html template file for pdf generation
type PDFConfig struct {
	TemplateFileName                  string `mapstructure:"templateFileName"`
	TemplateFilePath                  string `mapstructure:"templateFilePath"`
	LPLabel1x3MinimalTemplateFileName string `mapstructure:"lpLabel1x3MinimalTemplateFileName"`
	LPLabel1x3MinimalTemplateFilePath string `mapstructure:"lpLabel1x3MinimalTemplateFilePath"`
	LPLabel1x4MinimalTemplateFileName string `mapstructure:"lpLabel1x4MinimalTemplateFileName"`
	LPLabel1x4MinimalTemplateFilePath string `mapstructure:"lpLabel1x4MinimalTemplateFilePath"`
	LPLabel4x6MinimalTemplateFileName string `mapstructure:"lpLabel4x6MinimalTemplateFileName"`
	LPLabel4x6MinimalTemplateFilePath string `mapstructure:"lpLabel4x6MinimalTemplateFilePath"`
}

// ServiceConfig contains app service related config
type ServiceConfig struct {
	DBName       string `mapstructure:"dbName"`
	EntityDBName string `mapstructure:"entityDBName"`
}

// # AWS Config has authentication related configuration
type AWSConfig struct {
	Region           string `mapstructure:"region"`
	ARNBucket        string `mapstructure:"arnBucket"`
	OrderBucket      string `mapstructure:"orderBucket"`
	ItemMasterBucket string `mapstructure:"itemMasterBucket"`
	ShipmentBucket   string `mapstructure:"shipmentBucket"`
	LPSnapShotBucket string `mapstructure:"lpSnapshotBucket"`
	// # Temp Bucket to store temporary QR codes PDF files
	TempBucket      string `mapstructure:"tempBucket"`
	AccessKeyID     string `mapstructure:"accessKeyId"`
	SecretAccessKey string `mapstructure:"secretAccessKey"`
}

// TokenAuthConfig contains token authentication related configuration
type TokenAuthConfig struct {
	JWTSignKey            string `mapstructure:"jwtSignKey"`
	JWTExpiresAt          int64  `mapstructure:"expiresAt"`
	PasswordHashSecretKey string `mapstructure:"password_hash_secret_key"`
	HmacSecret            string `mapstructure:"hmacSecret"`
	SessionSecretKey      string `mapstructure:"session_secret_key"`
	EmailVerificationKey  string `mapstructure:"email_verification_key"`
	PasswordResetKey      string `mapstructure:"password_reset_key"`
}

// KafkaConfig has kafka cluster specific configuration
type KafkaConfig struct {
	EnableKafka bool     `mapstructure:"enableKafka"`
	BrokerDial  string   `mapstructure:"brokerDial"`
	BrokerURL   string   `mapstructure:"brokerUrl"`
	BrokerPort  string   `mapstructure:"brokerPort"`
	Brokers     []string `mapstructure:"brokers"`
	Username    string   `mapstructure:"username"`
	Password    string   `mapstructure:"password"`
}

// ListenerConfig contains app kafka topic listener related config
type ListenerConfig struct {
	GroupID  string   `mapstructure:"groupId"`
	Brokers  []string `mapstructure:"brokers"`
	Topic    string   `mapstructure:"topic"`
	Username string   `mapstructure:"username"`
	Password string   `mapstructure:"password"`
}

// ConsumerConfig contains app kafka topic listener related config
type ConsumerConfig struct {
	GroupID  string   `mapstructure:"groupId"`
	Brokers  []string `mapstructure:"brokers"`
	Topic    string   `mapstructure:"topic"`
	Username string   `mapstructure:"username"`
	Password string   `mapstructure:"password"`
}

// ProducerConfig contains app kafka topic producer related config
type ProducerConfig struct {
	Brokers  []string `mapstructure:"brokers"`
	Topic    string   `mapstructure:"topic"`
	Async    bool     `mapstructure:"async"`
	Username string   `mapstructure:"username"`
	Password string   `mapstructure:"password"`
}

// LoggerConfig contains different logger configurations
type LoggerConfig struct {
	KafkaLoggerConfig   `mapstructure:"kafkaLog"`
	FileLoggerConfig    `mapstructure:"fileLog"`
	ConsoleLoggerConfig `mapstructure:"consoleLog"`
}

// KafkaLoggerConfig contains kafka logger specific configuration
type KafkaLoggerConfig struct {
	EnableKafkaLogger bool   `mapstructure:"enableKafkaLog"`
	KafkaTopic        string `mapstructure:"kafkaTopic"`
	KafkaPartition    string `mapstructure:"kafkaPartition"`
}

// ConsoleLoggerConfig contains file console logging specific configuration
type ConsoleLoggerConfig struct {
	EnableConsoleLogger bool `mapstructure:"enableConsoleLog"`
}

// FileLoggerConfig contains file logging specific configuration
type FileLoggerConfig struct {
	FileName         string `mapstructure:"fileName"`
	Path             string `mapstructure:"path"`
	EnableFileLogger bool   `mapstructure:"enableFileLog"`
	MaxBackupsFile   int    `mapstructure:"maxBackupFile"`
	MaxSize          int    `mapstructure:"maxFileSize"`
	MaxAge           int    `mapstructure:"maxAge"`
	Compress         bool   `mapstructure:"compress"`
}

// MongoDBConfig contains mongodb related configuration
type MongoDBConfig struct {
	Scheme     string `mapstructure:"scheme"`
	Host       string `mapstructure:"host"`
	DBName     string `mapstructure:"name"`
	Username   string `mapstructure:"username"`
	Password   string `mapstructure:"password"`
	ReplicaSet string `mapstructure:"replicaSet"`
}

// ConnectionURL returns connection string to of mongodb storage
func (d *MongoDBConfig) ConnectionURL() string {
	url := fmt.Sprintf("%s://", d.Scheme)
	if d.Username != "" && d.Password != "" {
		url += fmt.Sprintf("%s:%s@", d.Username, d.Password)
	}
	url += d.Host
	if d.ReplicaSet != "" {
		url += fmt.Sprintf("/?replicaSet=%s", d.ReplicaSet)
	}
	return url
}

// RedisConfig has cache related configuration.
type RedisConfig struct {
	Network  string `mapstructure:"network"`
	Host     string `mapstructure:"host"`
	Port     string `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
}

// ConnectionURL returns connection string to of mongodb storage
func (r *RedisConfig) ConnectionURL() string {
	var url string
	if r.Username != "" {
		url += r.Username
	}
	if r.Password != "" {
		url += fmt.Sprintf(":%s@", r.Password)
	}
	url += r.Host
	if r.Port != "" {
		url += fmt.Sprintf(":%s", r.Port)
	}
	return url
}

// MiddlewareConfig has middlewares related configuration
type MiddlewareConfig struct {
	EnableRequestLog bool `mapstructure:"enableRequestLog"`
}

// GetConfig returns entire project configuration
func GetConfig() *Config {
	return GetConfigFromFile("default")
}

// GetConfigFromFile returns configuration from specific file object
func GetConfigFromFile(fileName string) *Config {
	if fileName == "" {
		fileName = "default"
	}

	// looking for filename `default` inside `src/server` dir with `.toml` extension
	viper.SetConfigName(fileName)
	viper.AddConfigPath("../conf/")
	viper.AddConfigPath("../../conf/")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./conf/")
	viper.SetConfigType("toml")

	err := viper.ReadInConfig()
	if err != nil {
		log.Fatalf("couldn't load config: %s", err)
		os.Exit(1)
	}
	config := &Config{}
	err = viper.Unmarshal(&config)
	if err != nil {
		fmt.Printf("couldn't read config: %s", err)
		os.Exit(1)
	}

	// APP
	config.APPConfig.MongoDBConfig = config.MongoDBConfig
	config.APPConfig.PDFConfig = config.PDFConfig
	config.APPConfig.AWSConfig = config.AWSConfig
	config.APPConfig.TokenAuthConfig = config.TokenAuthConfig
	config.APPConfig.FedexConfig = config.FedexConfig
	config.APPConfig.Notification = config.Notification
	// API
	config.APIConfig.SessionConfig = config.SessionConfig
	config.APIConfig.AWSConfig = config.AWSConfig
	config.APIConfig.Notification = config.Notification
	return config
}

// CORSConfig contains CORS related configurations
type CORSConfig struct {
	AllowedOrigins   []string `mapstructure:"allowed_origins"`
	AllowedMethods   []string `mapstructure:"allowed_methods"`
	AllowCredentials bool     `mapstructure:"allow_credentials"`
	AllowedHeaders   []string `mapstructure:"allowed_headers"`
}
