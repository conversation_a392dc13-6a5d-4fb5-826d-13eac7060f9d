package handler

import (
	"encoding/json"
	"inventory/server/auth"
	"net/http"
	"strconv"

	errors "github.com/vasupal1996/goerror"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
)

// Request represents a request from client
type Request struct {
	HandlerFunc func(*RequestContext, http.ResponseWriter, *http.Request)
	AuthFunc    auth.TokenAuth
	Environment string
	IsLoggedIn  bool
}

// HandleRequest := handles incoming requests from client
func (rh *Request) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	spanCTX, span := otel.GetTracerProvider().Tracer(r.URL.Path).Start(r.Context(), r.URL.Path)
	defer span.End()

	requestCTX := &RequestContext{}
	requestCTX.RequestID = span.SpanContext().SpanID().String()
	requestCTX.Path = r.URL.Path
	requestCTX.SpanCTX = spanCTX

	span.SetAttributes(
		// attribute.String("request_body", string(requestBody)),
		attribute.String("request_content_type", r.Header.Get("Content-type")),
		attribute.String("request_url", r.URL.RequestURI()),
	)

	name := r.Header.Get("route_name")
	platformID := r.Header.Get("platform_id")
	if platformID != "" {
		requestCTX.PlatformID = platformID
		span.SetAttributes(
			attribute.String("platform_id", platformID),
		)
	}
	clientId, _ := primitive.ObjectIDFromHex(r.Header.Get("client_id"))
	if r.Header.Get("client_id") != "" {
		requestCTX.ClientID = clientId
		span.SetAttributes(
			attribute.String("client_id", clientId.Hex()),
		)
	}

	authToken := r.Header.Get("Authorization")
	if authToken != "" && authToken != "open" {
		claim, sessionID, err := rh.AuthFunc.VerifyToken(authToken)
		if err != nil {
			requestCTX.SetErr(errors.Wrap(err, "failed to verify token", &errors.PermissionDenied), http.StatusUnauthorized)
			goto SKIP_REQUEST
		} else {
			requestCTX.SessionID = sessionID
			requestCTX.UserClaim = claim.(*auth.UserClaim)

			span.SetAttributes(
				attribute.String("session_id", sessionID),
			)

			if len(claim.(*auth.UserClaim).ActivePlatformID) > 0 {
				requestCTX.WarehouseID = claim.(*auth.UserClaim).ActivePlatformID[platformID]
				span.SetAttributes(
					attribute.String("warehouse_id", requestCTX.WarehouseID.Hex()),
				)
			}
			if r.Header.Get("client_id") != "" {
				requestCTX.ClientID = primitive.NilObjectID
			}
		}
	}
	if rh.IsLoggedIn {
		if requestCTX.UserClaim == nil {
			requestCTX.SetErr(errors.New("auth token required", &errors.PermissionDenied), http.StatusUnauthorized)
			goto SKIP_REQUEST
		} else {
			if !requestCTX.UserClaim.IsClient(requestCTX.WarehouseID, clientId) {
				requestCTX.SetErr(errors.New("permission denied: operation on this client is not authorized", &errors.PermissionDenied), http.StatusForbidden)
				goto SKIP_REQUEST
			}
			if r.Header.Get("warehosue_id") != "" {
				if !requestCTX.UserClaim.IsGranted(requestCTX.WarehouseID, name) {
					requestCTX.SetErr(errors.New("permission denied: you don't have access to this feature", &errors.PermissionDenied), http.StatusForbidden)
					goto SKIP_REQUEST
				}
			} else {
				if !requestCTX.UserClaim.IsGranted(clientId, name) {
					requestCTX.SetErr(errors.New("permission denied: you don't have access to this feature", &errors.PermissionDenied), http.StatusForbidden)
					goto SKIP_REQUEST
				}
			}

		}
	}

SKIP_REQUEST:

	w.Header().Set(auth.HeaderRequestID, requestCTX.RequestID)
	if requestCTX.Err == nil {
		rh.HandlerFunc(requestCTX, w, r)
	}

	switch t := requestCTX.ResponseType; t {
	case HTMLResp:
		w.Header().Set("Content-Type", "text/html")
		w.WriteHeader(requestCTX.ResponseCode)
		res := requestCTX.Response.GetRaw()
		if _, err := w.Write(res.([]byte)); err != nil {
			requestCTX.SetErr(errors.New("Could not write response", &errors.SomethingWentWrong), http.StatusInternalServerError)
		}
		span.SetAttributes(
			attribute.String("response_content_type", "text/html"),
			attribute.String("response_code", strconv.Itoa(requestCTX.ResponseCode)),
		)
	case JSONResp:
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(requestCTX.ResponseCode)
		if err := json.NewEncoder(w).Encode(requestCTX.Response); err != nil {
			requestCTX.SetErr(errors.New("Could not encode response", &errors.SomethingWentWrong), http.StatusInternalServerError)
		}
		response, _ := json.Marshal(requestCTX.Response)
		span.SetAttributes(
			attribute.String("response_content_type", "application/json"),
			attribute.String("response_code", strconv.Itoa(requestCTX.ResponseCode)),
			attribute.String("response_data", string(response)),
		)
	case ErrorResp:
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(requestCTX.ResponseCode)
		requestCTX.Err.RequestID = &requestCTX.RequestID
		if err := json.NewEncoder(w).Encode(&requestCTX.Err); err != nil {
			requestCTX.SetErr(errors.New("Could not encode response", &errors.SomethingWentWrong), http.StatusInternalServerError)
		}
		errorsStr := ""
		for _, err := range requestCTX.Err.Error {
			errorsStr += err.Error() + "\n"
		}
		span.SetAttributes(
			attribute.String("response_content_type", "application/json"),
			attribute.String("response_code", strconv.Itoa(requestCTX.ResponseCode)),
			attribute.String("error", errorsStr),
		)
	case RedirectResp:
		res, _ := requestCTX.Response.MarshalJSON()
		http.Redirect(w, r, string(res), requestCTX.ResponseCode)
	}

}
