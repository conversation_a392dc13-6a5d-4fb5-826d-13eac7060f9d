<!-- # Template-2: LP Label Minimal 4x6 # -->
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <!-- # Title -->
  <title> WMS Label Minimal 4x6 </title>
  <!-- # Style -->
  <style>
    @font-face {
      font-family: "kelsonBold";
      src: url("/home/<USER>/inventory/template/KelsonSansRU_Bold.otf") format("opentype");
    }

    @font-face {
      font-family: "kelsonLight";
      src: url("/home/<USER>/inventory/template/KelsonSansRU_Light.otf") format("opentype");
    }

    @font-face {
      font-family: "kelsonNormal";
      src: url("/home/<USER>/inventory/template/KelsonSansRU_Normal.otf") format("opentype");
    }

    body {
      /* # adjust all four for proper page fitting */
      margin: 0;
      padding: 0;
      height: 100%;
      width: 100%;
      background: white;
      /* # page break to print each label on new page */
      page-break-before: always;
    }

    table {
      width: 100%;
      height: 100%;
      background-color: white;
      border-collapse: collapse;
    }

    h1 {
      font-family: "kelsonBold";
      font-size: 8em;
      text-align: center;
      margin: 0;
    }

    h3 {
      font-family: "kelsonBold";
      font-size: 3em;
      text-align: center;
      margin: 20px 0;
    }

    p {
      font-family: "kelsonNormal";
      font-size: 3em;
      text-align: center;
      margin: 0;
    }

    .barcode-image {
      height: auto;
      width: 80%;
      display: block;
      margin: auto;
    }

    @media print {
      body {
        /* # adjust all four for proper page fitting */
        margin: 0;
        padding: 0;
        height: 100%;
        width: 100%;
        background: white;
        /* # page break to print each label on new page */
        page-break-before: always;
      }

      table {
        height: 100%;
        width: 100%;
        background-color: white;
        border-collapse: collapse;
      }
    }
  </style>
</head>

<!-- # Body -->

<body>
  <!-- # Loop -->
  {{ range $index, $item := .Data }}
  <table>
    <tr>
      <td>
        <!-- # LP Code -->
        <h1> {{ $item.Code }} </h1>
        <!-- # LP Type -->
        <p style="margin-top: 10px"> {{ $.Type }} </p>
      </td>
    </tr>
    <tr>
      <td>
        <div class="barcode-image-wrapper">
          <!-- # QR Code -->
          <img src="data:image/png;base64,{{ $item.QRcode }}" alt="Barcode" class="barcode-image" />
        </div>
      </td>
    </tr>
    <tr>
      <td>
        <!-- # Warehouse Name -->
        <h3> {{ $.WarehouseName }} </h3>
      </td>
    </tr>
  </table>
  <!-- break pdf page here -->
  <div style="page-break-before: always;"></div>
  <!-- # End -->
  {{ end }}
</body>

</html>