<!-- # Template-3: LP Label Minimal 1.25x3.5 # -->
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <!-- # Title -->
  <title> WMS Label Minimal 1.25x3.5 </title>
  <!-- # Style -->
  <style>
    @font-face {
      font-family: "kelsonBold";
      src: url("/home/<USER>/inventory/template/KelsonSansRU_Bold.otf") format("opentype");
    }

    @font-face {
      font-family: "kelsonLight";
      src: url("/home/<USER>/inventory/template/KelsonSansRU_Light.otf") format("opentype");
    }

    @font-face {
      font-family: "kelsonNormal";
      src: url("/home/<USER>/inventory/template/KelsonSansRU_Normal.otf") format("opentype");
    }

    body {
      /* # adjust all four for proper page fitting */
      margin: 0;
      padding: 0;
      height: 100%;
      width: 100%;
      background: white;
      /* # page break to print each label on new page */
      page-break-before: always;
    }

    table {
      height: 100%;
      width: 100%;
      background-color: white;
      border-collapse: collapse;
    }

    h1 {
      font-family: "kelsonBold";
      font-size: 2.5em;
      text-align: left;
      margin: 8px 0 8px 0;
      padding: 0;
    }

    p {
      padding: 0;
      margin: 0;
      font-family: "kelsonNormal";
      font-size: 16px;
      color: #343434;
    }

    .label-wrapper {
      background-color: white;
      height: 1.25in;
      width: 2.97in;
      grid-template-columns: 1.25in auto;
    }

    .barcode-image {
      height: 1.3in;
      width: 1.3in;
      display: block;
      margin: auto;
    }

    @media print {
      body {
        /* # adjust all four for proper page fitting */
        margin: 0;
        padding: 0;
        height: 100%;
        width: 100%;
        background: white;
        /* # page break to print each label on new page */
        page-break-before: always;
      }

      table {
        height: 100%;
        width: 100%;
        background-color: white;
        border-collapse: collapse;
      }
    }
  </style>
</head>

<!-- # Body -->

<body>
  <!-- # Loop -->
  {{ range $index, $item := .Data }}
  <table class="label-wrapper">
    <tr>
      <td style="width: 1.25in">
        <!-- # QR Code -->
        <img src="data:image/png;base64,{{ $item.QRcode }}" alt="Barcode" class="barcode-image" />
      </td>
      <td>
        <!-- # Warehouse Name -->
        <p> {{ $.WarehouseName }} </p>
        <!-- # LP Code -->
        <h1> {{ $item.Code }} </h1>
        <!-- # LP Type -->
        <p> {{ $.Type }} </p>
      </td>
    </tr>
  </table>
  <!-- # End -->
  {{ end }}
</body>

</html>