#!/bin/bash

# Automatically get the Go project root directory (parent of script directory)
DIR="/home/<USER>/inventory"
SERVICE_NAME="$SERVICENAME"
LOG_FILE="/tmp/go_build.log"
DEPLOY_LOG="/opt/codedeploy-agent/deployment-root/deployment-logs/codedeploy-agent-deployments.log"
SLACK_WEBHOOK_URL="*********************************************************************************"  # Replace with your actual webhook
BUILD_TIMEOUT="120s"

cd "$DIR" || exit 1

# Clean Go module dependencies
sudo go mod tidy

# Build the Go project with timeout and capture logs
{
  echo "[$(date)] Starting build with timeout of $BUILD_TIMEOUT..."
  timeout "$BUILD_TIMEOUT" sudo go build main.go
} > "$LOG_FILE" 2>&1

EXIT_CODE=$?

# If build timed out
if [ $EXIT_CODE -eq 124 ]; then
  BUILD_LOG=$(tail -n 15 "$LOG_FILE")
  DEPLOY_LOG_SNIPPET=$(tail -n 10 "$DEPLOY_LOG")

  curl -X POST -H 'Content-type: application/json' \
    --data "{
      \"text\": \"⏰ *Go build TIMED OUT on EC2 instance!* \nService: \`$SERVICE_NAME\`\nHost: \`$(hostname)\`\nTimeout: \`$BUILD_TIMEOUT\`\n\n*Build Logs:*\n\`\`\`$BUILD_LOG\`\`\`\n*CodeDeploy Logs:*\n\`\`\`$DEPLOY_LOG_SNIPPET\`\`\`\"
    }" \
    "$SLACK_WEBHOOK_URL"

  exit 1
fi

# If build failed for other reason
if [ $EXIT_CODE -ne 0 ]; then
  BUILD_LOG=$(tail -n 15 "$LOG_FILE")
  DEPLOY_LOG_SNIPPET=$(tail -n 10 "$DEPLOY_LOG")

  curl -X POST -H 'Content-type: application/json' \
    --data "{
      \"text\": \"🚨 *Go build FAILED on EC2 instance!* \nService: \`$SERVICE_NAME\`\nHost: \`$(hostname)\`\nExit Code: \`$EXIT_CODE\`\n\n*Build Logs:*\n\`\`\`$BUILD_LOG\`\`\`\n*CodeDeploy Logs:*\n\`\`\`$DEPLOY_LOG_SNIPPET\`\`\`\"
    }" \
    "$SLACK_WEBHOOK_URL"

  exit 1
fi

# Restart service if build succeeded
sudo service "$SERVICE_NAME" restart

echo "[$(date)] Build and restart successful."
rm -f "$LOG_FILE"