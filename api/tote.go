package api

import (
	"encoding/json"
	"inventory/schema"
	"inventory/server/auth"
	"inventory/server/handler"
	"net/http"

	"github.com/pkg/errors"
	uuid "github.com/satori/go.uuid"
)

// # Create Tote API
func (a *API) createTote(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # <PERSON>le panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Close Request Body
	defer r.Body.Close()

	// # Request Data Struct
	var createToteForm schema.ValidateCreateTote

	// # Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&createToteForm); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Validate if the required keys are present in the ContainerQRData data as defined in the struct
	if errs := a.Validator.Validate(&createToteForm); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}

	// # Get the warehouse and User data from the request context
	createToteForm.WarehouseID = requestCTX.WarehouseID.Hex()
	createToteForm.WarehouseName = requestCTX.UserClaim.(*auth.UserClaim).Services[requestCTX.PlatformID][requestCTX.WarehouseID].WarehouseName
	createToteForm.UserID = requestCTX.UserClaim.(*auth.UserClaim).ID.Hex()
	createToteForm.Username = requestCTX.UserClaim.(*auth.UserClaim).FullName

	// # Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	body, err := json.Marshal(createToteForm)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal request body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "create_tote").Hex("user_claim", userClaim).Msg("Create Tote request data.")

	// # Create Tote and get the response
	res, err := a.App.Tote.CreateTote(&createToteForm)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "create_tote").Str("status_code", "500").Err(err).Msg("Create Tote error response.")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Logging response data for tracking
	response, err := json.Marshal(res)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "create_tote").Hex("response_data", response).Str("status_code", "200").Msg("Create Tote response data.")

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

// # Get Tote Type API
func (a *API) getToteTypes(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	whID := requestCTX.WarehouseID.Hex()

	// # Get Tote Types of a warehouse
	res, err := a.App.Tote.GetToteTypes(whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)

}

// # Get Totes API
func (a *API) getTotes(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	whID := requestCTX.WarehouseID.Hex()

	// # empty cart query param
	emptyTotes := false
	emptyToteUrl := r.URL.Query().Get("empty_totes")
	if emptyToteUrl == "true" {
		emptyTotes = true
	}

	// # Get Totes of a warehouse
	res, err := a.App.Tote.GetTotes(whID, emptyTotes)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

// # Get Tote API
func (a *API) getTote(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Get the tote ID from the request url
	tote_id := r.URL.Query().Get("tote_id")
	if tote_id == "" {
		requestCTX.SetErr(errors.New("Tote Id is required"), 400)
		return
	}
	toteID, err := uuid.FromString(tote_id)
	if err != nil {
		err = errors.Wrap(err, "Invalid tote id")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get the tote
	res, err := a.App.Tote.GetTote(toteID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}
