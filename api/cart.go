package api

import (
	"encoding/json"
	"inventory/schema"
	"inventory/server/auth"
	"inventory/server/handler"
	"net/http"
	"strings"

	"github.com/pkg/errors"
	uuid "github.com/satori/go.uuid"
)

// # Create Cart API
func (a *API) createCart(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # <PERSON>le panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Close Request Body
	defer r.Body.Close()

	// # Request Data Struct
	var createCartForm schema.ValidateCreateCart

	// # Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&createCartForm); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Validate if the required keys are present in the ContainerQRData data as defined in the struct
	if errs := a.Validator.Validate(&createCartForm); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}

	// # Get the warehouse and User data from the request context
	createCartForm.WarehouseID = requestCTX.WarehouseID.Hex()
	createCartForm.WarehouseName = requestCTX.UserClaim.(*auth.UserClaim).Services[requestCTX.PlatformID][requestCTX.WarehouseID].WarehouseName
	createCartForm.UserID = requestCTX.UserClaim.(*auth.UserClaim).ID.Hex()
	createCartForm.Username = requestCTX.UserClaim.(*auth.UserClaim).FullName

	// # Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	body, err := json.Marshal(createCartForm)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal request body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "create_cart").Hex("user_claim", userClaim).Msg("Create Cart request data.")

	// # Create Cart and get the response
	res, err := a.App.Cart.CreateCart(&createCartForm)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "create_cart").Str("status_code", "500").Err(err).Msg("Create Cart error response.")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Logging response data for tracking
	response, err := json.Marshal(res)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "create_cart").Hex("response_data", response).Str("status_code", "200").Msg("Create Cart response data.")

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

// # Get Carts API
func (a *API) getCarts(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Get the warehouse ID from the request context
	warehouseID := requestCTX.WarehouseID.Hex()

	// # empty cart query param
	emptyCart, readyForNewWaveGeneration := false, false
	emptyCartUrl := r.URL.Query().Get("empty_carts")
	if emptyCartUrl == "true" {
		emptyCart = true
	}

	readyForNewWaveGenerationUrl := r.URL.Query().Get("ready_for_new_wave_generation")
	if readyForNewWaveGenerationUrl == "true" {
		emptyCart = false
		readyForNewWaveGeneration = true
	}

	// # Get the carts
	carts, err := a.App.Cart.GetCarts(warehouseID, emptyCart, readyForNewWaveGeneration)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(carts, 200)
}

// # Get Cart API
func (a *API) getCart(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Get the cart ID from the request url
	cart_id := r.URL.Query().Get("cart_id")
	if cart_id == "" {
		requestCTX.SetErr(errors.New("Cart id is required"), 400)
		return
	}
	cartID, err := uuid.FromString(cart_id)
	if err != nil {
		err = errors.Wrap(err, "Invalid cart id")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get the cart
	cart, err := a.App.Cart.GetCart(cartID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(cart, 200)
}

// # Prepare Cart API maps cart to totes
func (a *API) prepareCart(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Close Request Body
	defer r.Body.Close()

	// # Request Data Struct
	var prepareCartForm schema.ValidatePrepareCart

	// # Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&prepareCartForm); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Validate if the required keys are present in the ContainerQRData data as defined in the struct
	if errs := a.Validator.Validate(&prepareCartForm); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}

	// # Get the warehouse and User data from the request context
	prepareCartForm.WarehouseID = requestCTX.WarehouseID.Hex()
	prepareCartForm.WarehouseName = requestCTX.UserClaim.(*auth.UserClaim).Services[requestCTX.PlatformID][requestCTX.WarehouseID].WarehouseName
	prepareCartForm.UserID = requestCTX.UserClaim.(*auth.UserClaim).ID.Hex()
	prepareCartForm.Username = requestCTX.UserClaim.(*auth.UserClaim).FullName

	// # Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	body, err := json.Marshal(prepareCartForm)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal request body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "prepare_cart").Hex("user_claim", userClaim).Msg("prepare Cart request data.")

	// # prepare Cart and get the response
	res, err := a.App.Cart.PrepareCart(&prepareCartForm)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "prepare_cart").Str("status_code", "500").Err(err).Msg("prepare Cart error response.")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Logging response data for tracking
	response, err := json.Marshal(res)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "prepare_cart").Hex("response_data", response).Str("status_code", "200").Msg("prepare Cart response data.")

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

// # Prepare Cart API maps cart to totes
func (a *API) getOrderCartDetails(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	orderID := strings.TrimSpace(r.URL.Query().Get("order_id"))
	if orderID == "" {
		requestCTX.SetErr(errors.New("invalid order id"), http.StatusBadRequest)
	}

	// # prepare Cart and get the response
	res, err := a.App.Cart.GetOrderCartDetails(requestCTX.WarehouseID.Hex(), orderID)
	if err != nil {
		requestCTX.SetErr(err, http.StatusInternalServerError)
		return
	}

	requestCTX.SetAppResponse(res, http.StatusOK)
}

// # RemoveToteFromCart API removes tote from cart
func (a *API) removeToteFromCart(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Close Request Body
	defer r.Body.Close()

	// # Request Data Struct
	var removeToteFromCartForm schema.ValidateRemoveToteFromCart

	// # Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&removeToteFromCartForm); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Validate if the required keys are present in the ContainerQRData data as defined in the struct
	if errs := a.Validator.Validate(&removeToteFromCartForm); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}

	// # Get the warehouse and User data from the request context
	removeToteFromCartForm.WarehouseID = requestCTX.WarehouseID.Hex()
	removeToteFromCartForm.WarehouseName = requestCTX.UserClaim.(*auth.UserClaim).Services[requestCTX.PlatformID][requestCTX.WarehouseID].WarehouseName
	removeToteFromCartForm.UserID = requestCTX.UserClaim.(*auth.UserClaim).ID.Hex()
	removeToteFromCartForm.Username = requestCTX.UserClaim.(*auth.UserClaim).FullName

	// # Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	body, err := json.Marshal(removeToteFromCartForm)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal request body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "remove_tote_from_cart").Hex("user_claim", userClaim).Msg("Remove Tote From Cart request data.")

	// # Remove Tote From Cart and get the response
	res, err := a.App.Cart.RemoveToteFromCart(&removeToteFromCartForm)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "remove_tote_from_cart").Str("status_code", "500").Err(err).Msg("Remove Tote From Cart error response.")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Logging response data for tracking
	response, err := json.Marshal(res)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "remove_tote_from_cart").Hex("response_data", response).Str("status_code", "200").Msg("Remove Tote From Cart response data.")

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

// # printCart API prints cart totes
func (a *API) printCart(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Close Request Body
	defer r.Body.Close()

	// # Request Data Struct
	var printCartForm schema.ValidatePrintCart

	// # Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&printCartForm); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Validate if the required keys are present in the ContainerQRData data as defined in the struct
	if errs := a.Validator.Validate(&printCartForm); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}

	// # Get the warehouse and User data from the request context
	printCartForm.WarehouseID = requestCTX.WarehouseID.Hex()
	printCartForm.WarehouseName = requestCTX.UserClaim.(*auth.UserClaim).Services[requestCTX.PlatformID][requestCTX.WarehouseID].WarehouseName
	printCartForm.UserID = requestCTX.UserClaim.(*auth.UserClaim).ID.Hex()
	printCartForm.Username = requestCTX.UserClaim.(*auth.UserClaim).FullName

	// # Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	body, err := json.Marshal(printCartForm)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal request body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "print_cart").Hex("user_claim", userClaim).Msg("Print Cart request data.")

	// # Print Cart and get the response
	res, err := a.App.Cart.PrintCart(&printCartForm)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "print_cart").Str("status_code", "500").Err(err).Msg("Print Cart error response.")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Logging response data for tracking
	response, err := json.Marshal(res)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "print_cart").Hex("response_data", response).Str("status_code", "200").Msg("Print Cart response data.")

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}
