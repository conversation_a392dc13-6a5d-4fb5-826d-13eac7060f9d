package api

import (
	"inventory/server/handler"
	"net/http"
	"strconv"
	"time"

	errors "github.com/vasupal1996/goerror"
)

// # Get Item Transactions
func (a *API) getItemTransactions(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	var err error

	whID := requestCTX.WarehouseID

	// # Get the client timezone location
	location, err := a.getClientTimeZoneLocation(&whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'search query' from query params
	search := r.URL.Query().Get("search")
	searchModifier := r.URL.Query().Get("search_modifier")

	// # Changed At Date Filter: Get 'start date' and 'end date' from query params
	startDateStr := r.URL.Query().Get("from_date")
	endDateStr := r.URL.Query().Get("to_date")

	pageNoStr := r.URL.Query().Get("page_no")
	pageSizeStr := r.URL.Query().Get("page_size")

	itemID := r.URL.Query().Get("item_id")

	if startDateStr == "" {
		startDateStr = r.URL.Query().Get("fromTime")
	}
	if endDateStr == "" {
		endDateStr = r.URL.Query().Get("toTime")
	}
	if pageNoStr == "" {
		pageNoStr = r.URL.Query().Get("pageNo")
	}
	if pageSizeStr == "" {
		pageSizeStr = r.URL.Query().Get("pageSize")
	}
	if itemID == "" {
		itemID = r.URL.Query().Get("itemID")
	}

	var startDate, endDate time.Time

	// # Validate and Format the 'date range'
	var format string = "default" // # Default to last '3 months'
	startDateStr, endDateStr, err = a.validateAndFormatDateRange(startDateStr, endDateStr, format, location)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Convert the 'string' date to 'time.Time' format
	if startDateStr != "" && endDateStr != "" {
		startDate, err = time.Parse("2006-01-02 15:04:05", startDateStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid start date", &errors.BadRequest)
			requestCTX.SetErr(err, 400)
			return
		}

		endDate, err = time.Parse("2006-01-02 15:04:05", endDateStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid end date", &errors.BadRequest)
			requestCTX.SetErr(err, 400)
			return
		}
	}

	pageNo, err := strconv.Atoi(pageNoStr)
	if err != nil {
		err = errors.New("Invalid page number", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil {
		err = errors.New("Invalid page size", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	if itemID == "" {
		err = errors.New("item_id is a required field in query params", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	// # Call 'Get Item Transactions' method
	res, err := a.App.Transaction.GetItemTransactions(itemID, search, searchModifier, pageNo, pageSize, startDate, endDate)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}
