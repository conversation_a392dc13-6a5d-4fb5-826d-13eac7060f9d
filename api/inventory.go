package api

import (
	"context"
	"encoding/json"
	"inventory/schema"
	"inventory/server/auth"
	"inventory/server/handler"
	"io"
	"net/http"
	core_proto "proto/core"
	"strconv"
	"strings"
	"time"

	errors "github.com/vasupal1996/goerror"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// # Get Client Time Zone Location
func (a *API) getClientTimeZoneLocation(whID *primitive.ObjectID) (*time.Location, error) {
	ctx := context.TODO()

	var err error

	req := &core_proto.GetWarehouseRequest{
		WarehouseId: whID.Hex(),
	}

	warehouse, err := a.App.GrpcClient.Core.Client.GetWarehouse(ctx, req)
	if err != nil {
		err = errors.Wrap(err, "failed to get warehouse", &errors.SomethingWentWrong)
		return nil, err
	}

	// # Get the timezone of the warehouse
	timezone := warehouse.GetTimezone()
	if timezone == "" {
		err = errors.New("timezone is not set for the warehouse", &errors.SomethingWentWrong)
		return nil, err
	}

	// # Load the location of the timezone
	location, err := time.LoadLocation(timezone)
	if err != nil {
		err = errors.Wrap(err, "failed to load location of the timezone", &errors.SomethingWentWrong)
		return nil, err
	}

	// # Return the location
	return location, nil
}

// # Validate And Format Date Range
func (a *API) validateAndFormatDateRange(startDate, endDate, format string, location *time.Location) (string, string, error) {
	// # Error Object
	var err error

	// # Handle all the 'date' related validations
	if startDate == "" && endDate != "" {
		err = errors.New("start date is required when end date is provided", &errors.BadRequest)
		return "", "", err
	} else if startDate != "" && endDate == "" {
		err = errors.New("end date is required when start date is provided", &errors.BadRequest)
		return "", "", err
	} else if startDate != "" && endDate != "" {
		sd, err := time.ParseInLocation("2006-01-02", startDate, location)
		if err != nil {
			err = errors.Wrap(err, "Invalid start date", &errors.BadRequest)
			return "", "", err
		}

		ed, err := time.ParseInLocation("2006-01-02", endDate, location)
		if err != nil {
			err = errors.Wrap(err, "Invalid end date", &errors.BadRequest)
			return "", "", err
		}

		// # Add '1 day' to the 'end date'
		ed = ed.AddDate(0, 0, 1)

		// # Validate the date range
		if sd.After(ed) {
			err = errors.New("Invalid date range: start date must be before end date", &errors.BadRequest)
			return "", "", err
		}

		// # Format the dates in 'UTC' format
		startDate = sd.UTC().Format("2006-01-02 15:04:05")
		endDate = ed.UTC().Format("2006-01-02 15:04:05")
	} else {
		if format == "default" {
			// # Default to last '3 months'

			// # Get the 'current time'
			now := time.Now()

			// # Get the 'current time' in the client timezone location
			currentTime := now.In(location)

			// # Start Date: Subtract '3 months' from the 'current date'
			startDate = currentTime.AddDate(0, -3, 0).UTC().Format("2006-01-02 15:04:05")

			// # End Date: Add '1 day' to the 'current date'
			endDate = currentTime.AddDate(0, 0, 1).UTC().Format("2006-01-02 15:04:05")
		} else {
			// # Default to 'empty string'
			startDate = ""
			endDate = ""
		}
	}

	// # Return the formatted 'date range'
	return startDate, endDate, nil
}

// # Get Inventory: It fetches all the warehouse 'inventory'
func (a *API) getInventory(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Checks and handles if any 'panic' occurs
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Error Object
	var err error

	// # Get the 'warehouse ID' from the request context
	warehouseID := requestCTX.WarehouseID.Hex()
	if warehouseID == "" {
		err = errors.New("warehouse ID is a required field in the request context", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get all the 'query params' from the 'request'
	clientID := r.URL.Query().Get("client_id")

	pageNoStr := r.URL.Query().Get("pageNo")
	pageSizeStr := r.URL.Query().Get("pageSize")

	onHoldStr := r.URL.Query().Get("on_hold")

	sortingValue := r.URL.Query().Get("sorting_value")
	sortingOrder := r.URL.Query().Get("sorting_order")

	search := r.URL.Query().Get("search")
	searchModifier := r.URL.Query().Get("search_modifier")

	var pageNoInt, pageSizeInt int
	var pageNo, pageSize int64

	if pageNoStr != "" {
		pageNoInt, err = strconv.Atoi(pageNoStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid page number", &errors.BadRequest)
			requestCTX.SetErr(err, 400)
			return
		}
		pageNo = int64(pageNoInt)
	}

	if pageSizeStr != "" {
		pageSizeInt, err = strconv.Atoi(pageSizeStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid page size", &errors.BadRequest)
			requestCTX.SetErr(err, 400)
			return
		}
		pageSize = int64(pageSizeInt)
	}

	var onHold string
	if onHoldStr != "" {
		onHoldCheck1 := strings.EqualFold(onHoldStr, "true")
		onHoldCheck2 := strings.EqualFold(onHoldStr, "false")
		if onHoldCheck1 {
			onHold = "true"
		} else if onHoldCheck2 {
			onHold = "false"
		} else {
			onHold = ""
		}
	} else {
		onHold = ""
	}

	// # Call 'Get Inventory' method
	res, err := a.App.Inventory.GetInventory(warehouseID, clientID, onHold, sortingValue, sortingOrder, search, searchModifier, pageNo, pageSize)
	if err != nil {
		requestCTX.SetErr(err, 500)
		return
	}

	// # Set the 'response'
	requestCTX.SetAppResponse(res, 200)
}

func (a *API) auditInventory(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrap(err, "Empty request data", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	warehouseID := requestCTX.WarehouseID.Hex()

	defer r.Body.Close()
	var auditData schema.ValidateAuditInventory

	//Reading request body to bytes
	body, err := io.ReadAll(r.Body)
	if err != nil {
		err = errors.Wrap(err, "Unable to read request body", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	// Converting request body data into native golang structure
	err = json.Unmarshal(body, &auditData)
	if err != nil {
		err = errors.Wrap(err, "Unable to read json schema", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}
	// Validate if the required keys are present in the auditData data as defined in the struct
	if errs := a.Validator.Validate(&auditData); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}
	auditData.RequestID = requestCTX.RequestID
	auditData.Username = requestCTX.UserClaim.(*auth.UserClaim).Username
	auditData.UserID = requestCTX.UserClaim.(*auth.UserClaim).ID.Hex()

	//Logging request data for tracking
	userClaim, _ := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "audit_inventory").Hex("user_claim", userClaim).Msg("Audit inventory request data.")

	// Audit inventory, replace system quantity with physical quantity
	res, err := a.App.Inventory.AuditInventory(&auditData, warehouseID)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "audit_inventory").Str("status_code", "500").Err(err).Msg("Audit inventory error response.")
		requestCTX.SetErr(err, 500)
		return
	}

	//Logging response data for tracking
	response, _ := json.Marshal(res)
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "audit_inventory").Hex("response_data", response).Str("status_code", "200").Msg("Audit inventory response data.")

	requestCTX.SetAppResponse(res, 200)
}

// # Get Item Tracked Inventory
func (a *API) getItemTrackedInventory(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	var err error

	whID := requestCTX.WarehouseID
	warehouseID := whID.Hex()

	// # Get the client timezone location
	location, err := a.getClientTimeZoneLocation(&whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'search query' from query params
	search := r.URL.Query().Get("search")

	// # Expiration Date Filter: Get 'start date' and 'end date' from query params
	startDate := r.URL.Query().Get("from_date")
	endDate := r.URL.Query().Get("to_date")

	// # Validate and Format the 'date range'
	var format string = "empty" // # Default to 'empty string'
	startDate, endDate, err = a.validateAndFormatDateRange(startDate, endDate, format, location)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	clientID := r.URL.Query().Get("client_id")

	itemID := r.URL.Query().Get("item_id")
	if itemID == "" {
		err = errors.New("item_id is a required field in query params", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	pickableNotZero := false
	pickableNotZeroStr := r.URL.Query().Get("render_pickable_non_zero")
	if pickableNotZeroStr == "true" {
		pickableNotZero = true
	}

	// # Call 'Get Item Tracked Inventory' method
	res, err := a.App.Inventory.GetItemTrackedInventory(warehouseID, clientID, itemID, search, startDate, endDate, pickableNotZero)
	if err != nil {
		requestCTX.SetErr(err, 500)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

func (a *API) searchInventory(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	warehouseID := requestCTX.WarehouseID.Hex()
	searchQuery := r.URL.Query().Get("searchQuery")
	pageSize, err := strconv.Atoi(r.URL.Query().Get("pageSize"))
	if err != nil {
		requestCTX.SetErr(errors.New("Invalid page size", &errors.BadRequest), 400)
		return
	}
	clientID := r.URL.Query().Get("clientID")

	res, err := a.App.Inventory.SearchInventory(warehouseID, clientID, searchQuery, pageSize)
	if err != nil {
		requestCTX.SetErr(err, 500)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) getConsolidatedInventory(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	pageNo, err := strconv.Atoi(r.URL.Query().Get("pageNo"))
	if err != nil {
		requestCTX.SetErr(errors.New("invalid page number", &errors.BadRequest), 400)
		return
	}
	pageSize, err := strconv.Atoi(r.URL.Query().Get("pageSize"))
	if err != nil {
		requestCTX.SetErr(errors.New("Invalid page size", &errors.BadRequest), 400)
		return
	}

	searchQuery := r.URL.Query().Get("searchQuery")

	user := requestCTX.UserClaim.(*auth.UserClaim)

	if !user.IsOrganizationOwner {
		requestCTX.SetErr(errors.New("You are not athorized to view this information", &errors.BadRequest), 400)
		return
	}

	// Get user accessible warehouses
	platformID := r.Header.Get("platform_id")
	var warehouseData []auth.ServiceData
	for _, whData := range user.Services[platformID] {
		warehouseData = append(warehouseData, whData)
	}

	res, err := a.App.Inventory.GetConsolidatedInventory(warehouseData, pageNo, pageSize, searchQuery)
	if err != nil {
		requestCTX.SetErr(err, 500)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) getOrgInventory(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	user := requestCTX.UserClaim.(*auth.UserClaim)

	if !user.IsOrganizationOwner {
		requestCTX.SetErr(errors.New("You are not athorized to view this information", &errors.BadRequest), 400)
		return
	}

	sku := r.URL.Query().Get("sku")
	if sku == "" {
		requestCTX.SetErr(errors.New("Sku is required", &errors.BadRequest), 400)
		return
	}

	// Get user accessible warehouses
	platformID := r.Header.Get("platform_id")
	var warehouseData []auth.ServiceData
	for _, whData := range user.Services[platformID] {
		warehouseData = append(warehouseData, whData)
	}

	res, err := a.App.Inventory.GetOrgInventory(warehouseData, sku)
	if err != nil {
		requestCTX.SetErr(err, 500)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) getAreaDetails(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	areaID := strings.TrimSpace(r.URL.Query().Get("area_id"))
	if areaID == "" {
		requestCTX.SetErr(errors.New("Area id is required", &errors.BadRequest), http.StatusInternalServerError)
		return
	}

	res, err := a.App.Inventory.GetAreaDetails(areaID)
	if err != nil {
		requestCTX.SetErr(err, 500)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

// # Get All Warehouse Items
func (a *API) getAllWarehouseItems(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Error Object
	var err error

	// # Get 'warehouse ID' from request context
	warehouseID := requestCTX.WarehouseID.Hex()
	if warehouseID == "" {
		err = errors.New("warehouse ID is required in request context", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'search query' from query params
	search := r.URL.Query().Get("search")

	// # Call 'Get All Warehouse Items' method
	res, err := a.App.Inventory.GetAllWarehouseItems(warehouseID, search)
	if err != nil {
		requestCTX.SetErr(err, 500)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

// # Get location by search
func (a *API) getLocationBySearch(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Error Object
	var err error

	// # Get 'warehouse ID' from request context
	warehouseID := requestCTX.WarehouseID.Hex()
	if warehouseID == "" {
		err = errors.New("warehouse ID is required in request context", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'search query' from query params
	search := r.URL.Query().Get("search")
	searchModifier := r.URL.Query().Get("search_modifier")
	if search == "" || searchModifier == "" {
		err = errors.New("search query and search modifier are required in query params", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	// # Call 'Get Location By Search' method
	res, err := a.App.Inventory.GetLocationBySearch(warehouseID, search, searchModifier)
	if err != nil {
		requestCTX.SetErr(err, 500)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}
