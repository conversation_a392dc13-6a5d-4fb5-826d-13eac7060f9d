package api

import (
	"inventory/server/auth"
	"inventory/server/handler"
	"net/http"
	"strconv"
	"strings"
	"time"

	errors "github.com/vasupal1996/goerror"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// # Get Client Inventory: It fetches all the 'client' warehouse 'inventory'
func (a *API) getClientInventory(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Checks and handles if any 'panic' occurs
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Error Object
	var err error

	// # Get the 'user claim' from the request context
	userClaim := requestCTX.UserClaim.(*auth.UserClaim)

	// # Check if the 'user' is a 'client' user
	_, ok := userClaim.ActivePlatformID["wms_client"]
	if !ok {
		err = errors.New("user is not a client user", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get the 'warehouse ID' from the request context
	warehouseID := requestCTX.WarehouseID.Hex()
	if warehouseID == "" {
		err = errors.New("warehouse ID is a required field in the request context", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get the 'client ID' from the 'user claim'
	clientID := userClaim.Services["wms_client"][requestCTX.WarehouseID].ClientID.Hex()
	clientIDs := []string{clientID}

	// # Get all the 'query params' from the 'request'
	listBy := r.URL.Query().Get("list_by")

	pageNoStr := r.URL.Query().Get("pageNo")
	pageSizeStr := r.URL.Query().Get("pageSize")

	onHoldStr := r.URL.Query().Get("on_hold")

	sortingValue := r.URL.Query().Get("sorting_value")
	sortingOrder := r.URL.Query().Get("sorting_order")

	search := r.URL.Query().Get("search")
	searchModifier := r.URL.Query().Get("search_modifier")

	if listBy == "" {
		listBy = "item"
	}

	var pageNoInt, pageSizeInt int
	var pageNo, pageSize int64

	if pageNoStr != "" {
		pageNoInt, err = strconv.Atoi(pageNoStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid page number", &errors.BadRequest)
			requestCTX.SetErr(err, 400)
			return
		}
		pageNo = int64(pageNoInt)
	}

	if pageSizeStr != "" {
		pageSizeInt, err = strconv.Atoi(pageSizeStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid page size", &errors.BadRequest)
			requestCTX.SetErr(err, 400)
			return
		}
		pageSize = int64(pageSizeInt)
	}

	var onHold string
	if onHoldStr != "" {
		onHoldCheck1 := strings.EqualFold(onHoldStr, "true")
		onHoldCheck2 := strings.EqualFold(onHoldStr, "false")
		if onHoldCheck1 {
			onHold = "true"
		} else if onHoldCheck2 {
			onHold = "false"
		} else {
			onHold = ""
		}
	} else {
		onHold = ""
	}

	if listBy == "item" {
		// # Call 'Get Inventory' method
		res, err := a.App.Inventory.GetInventory(warehouseID, clientID, onHold, sortingValue, sortingOrder, search, searchModifier, pageNo, pageSize)
		if err != nil {
			requestCTX.SetErr(err, 500)
			return
		}

		// # Set the 'response'
		requestCTX.SetAppResponse(res, 200)
	} else {
		// # Call 'Get Location Wise Inventory' method
		res, err := a.App.Operations.GetLocationWiseInventory(warehouseID, search, clientIDs, &pageNoInt, &pageSizeInt)
		if err != nil {
			requestCTX.SetErr(err, 500)
			return
		}

		// # Set the 'response'
		requestCTX.SetAppResponse(res, 200)
	}
}

// # Get Client Item Transactions
func (a *API) getClientItemTransactions(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	var err error

	whID := requestCTX.WarehouseID

	// # Get the 'user claim' from the request context
	userClaim := requestCTX.UserClaim.(*auth.UserClaim)

	// # Checking if 'user' is a 'client user'
	_, ok := userClaim.ActivePlatformID["wms_client"]
	if !ok {
		err = errors.New("user is not a client user", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get the client timezone location
	location, err := a.getClientTimeZoneLocation(&whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'search query' from query params
	search := r.URL.Query().Get("search")
	searchModifier := r.URL.Query().Get("search_modifier")

	// # Changed At Date Filter: Get 'start date' and 'end date' from query params
	startDateStr := r.URL.Query().Get("from_date")
	endDateStr := r.URL.Query().Get("to_date")

	pageNoStr := r.URL.Query().Get("page_no")
	pageSizeStr := r.URL.Query().Get("page_size")

	itemID := r.URL.Query().Get("item_id")

	// # For 'backward compatibility' with 'old query param' name
	if search == "" {
		search = r.URL.Query().Get("query")
	}
	if startDateStr == "" {
		startDateStr = r.URL.Query().Get("fromTime")
	}
	if endDateStr == "" {
		endDateStr = r.URL.Query().Get("toTime")
	}
	if pageNoStr == "" {
		pageNoStr = r.URL.Query().Get("pageNo")
	}
	if pageSizeStr == "" {
		pageSizeStr = r.URL.Query().Get("pageSize")
	}
	if itemID == "" {
		itemID = r.URL.Query().Get("itemID")
	}

	var startDate, endDate time.Time

	// # Validate and Format the 'date range'
	var format string = "default" // # Default to last '3 months'
	startDateStr, endDateStr, err = a.validateAndFormatDateRange(startDateStr, endDateStr, format, location)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Convert the 'string' date to 'time.Time' format
	if startDateStr != "" && endDateStr != "" {
		startDate, err = time.Parse("2006-01-02 15:04:05", startDateStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid start date", &errors.BadRequest)
			requestCTX.SetErr(err, 400)
			return
		}

		endDate, err = time.Parse("2006-01-02 15:04:05", endDateStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid end date", &errors.BadRequest)
			requestCTX.SetErr(err, 400)
			return
		}
	}

	pageNo, err := strconv.Atoi(pageNoStr)
	if err != nil {
		err = errors.New("Invalid page number", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil {
		err = errors.New("Invalid page size", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	if itemID == "" {
		err = errors.New("item_id is a required field in query params", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	// # Call 'Get Item Transactions' method
	res, err := a.App.Transaction.GetItemTransactions(itemID, search, searchModifier, pageNo, pageSize, startDate, endDate)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

// # Get Client Item Tracked Inventory
func (a *API) getClientItemTrackedInventory(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	var err error

	whID := requestCTX.WarehouseID
	warehouseID := whID.Hex()

	// # Get the 'user claim' from the request context
	userClaim := requestCTX.UserClaim.(*auth.UserClaim)

	// # Checking if 'user' is a 'client user'
	_, ok := userClaim.ActivePlatformID["wms_client"]
	if !ok {
		err = errors.New("user is not a client user", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get the client timezone location
	location, err := a.getClientTimeZoneLocation(&whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'search query' from query params
	search := r.URL.Query().Get("search")

	// # Expiration Date Filter: Get 'start date' and 'end date' from query params
	startDate := r.URL.Query().Get("from_date")
	endDate := r.URL.Query().Get("to_date")

	// # Validate and Format the 'date range'
	var format string = "empty" // # Default to 'empty string'
	startDate, endDate, err = a.validateAndFormatDateRange(startDate, endDate, format, location)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	clientID := r.URL.Query().Get("client_id")
	if clientID == "" {
		clientID = userClaim.Services["wms_client"][whID].ClientID.Hex()
	}

	itemID := r.URL.Query().Get("item_id")
	if itemID == "" {
		err = errors.New("item_id is a required field in query params", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	pickableNotZero := false
	pickableNotZeroStr := r.URL.Query().Get("render_pickable_non_zero")
	if pickableNotZeroStr == "true" {
		pickableNotZero = true
	}

	// # Call 'Get Item Tracked Inventory' method
	res, err := a.App.Inventory.GetItemTrackedInventory(warehouseID, clientID, itemID, search, startDate, endDate, pickableNotZero)
	if err != nil {
		requestCTX.SetErr(err, 500)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

// # Get Client Locations All Containers Layout
func (a *API) getClientLocationsAllContainersLayout(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Error Object
	var err error

	// # Get 'warehouse ID' from request context
	whID := requestCTX.WarehouseID

	// # Get the client timezone location
	location, err := a.getClientTimeZoneLocation(&whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'search query' from query params
	search := r.URL.Query().Get("search")

	// # Expiration Date Filter: Get 'start date' and 'end date' from query params
	startDateStr := r.URL.Query().Get("from_date")
	endDateStr := r.URL.Query().Get("to_date")

	// # Get 'client ID' from query params
	clientID := r.URL.Query().Get("client_id")

	// # Get Empty 'item ID'
	var itemID string = ""

	var startDate, endDate time.Time

	// # Validate and Format the 'date range'
	var format string = "empty" // # Default to 'empty string'
	startDateStr, endDateStr, err = a.validateAndFormatDateRange(startDateStr, endDateStr, format, location)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Convert the 'string' date to 'time.Time' format
	if startDateStr != "" && endDateStr != "" {
		startDate, err = time.Parse("2006-01-02 15:04:05", startDateStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid start date", &errors.BadRequest)
			requestCTX.SetErr(err, 400)
			return
		}

		endDate, err = time.Parse("2006-01-02 15:04:05", endDateStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid end date", &errors.BadRequest)
			requestCTX.SetErr(err, 400)
			return
		}
	}

	if clientID == "" {
		err = errors.New("client_id is a required field in query params", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}
	_, err = primitive.ObjectIDFromHex(clientID)
	if err != nil {
		err = errors.Wrap(err, "Invalid client ID", &errors.BadRequest)
		requestCTX.SetErr(err, 400)
		return
	}

	// # Call 'Get Item Locations All Containers' method
	res, err := a.App.Location.GetItemLocationsAllContainers(itemID, clientID, search, startDate, endDate)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}
