package api

import (
	"encoding/json"
	"inventory/schema"
	"inventory/server/auth"
	"inventory/server/handler"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Validate request data for - get location details
func (a *API) getLocation(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	location_id := r.URL.Query().Get("locationID")

	res, err := a.App.Location.GetLocation(location_id)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

// # Get Aggregate Location
// # Get Location (Details) + (Items) + (Containers) -> [Container Details + Container Items] in a 'single API'
func (a *API) getAggregateLocation(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	var err error

	// # Get 'location ID' from query params
	locationID := r.URL.Query().Get("location_id")
	if locationID == "" {
		err = errors.New("location_id is a required field in query params")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'warehouse ID' from request context
	whID := requestCTX.WarehouseID.Hex()
	if whID == "" {
		err = errors.New("warehouse ID is required in request context")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Call 'Get Aggregate Location' method
	res, err := a.App.Location.GetAggregateLocation(locationID, whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

// # Get Item Locations All Containers
func (a *API) getItemLocationsAllContainers(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Error Object
	var err error

	// # Get 'warehouse ID' from request context
	whID := requestCTX.WarehouseID

	// # Get the client timezone location
	location, err := a.getClientTimeZoneLocation(&whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'search query' from query params
	search := r.URL.Query().Get("search")

	// # Expiration Date Filter: Get 'start date' and 'end date' from query params
	startDateStr := r.URL.Query().Get("from_date")
	endDateStr := r.URL.Query().Get("to_date")

	// # Get 'item ID' from query params
	itemID := r.URL.Query().Get("item_id")

	// # Get Empty 'client ID'
	var clientID string = ""

	var startDate, endDate time.Time

	// # Validate and Format the 'date range'
	var format string = "empty" // # Default to 'empty string'
	startDateStr, endDateStr, err = a.validateAndFormatDateRange(startDateStr, endDateStr, format, location)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Convert the 'string' date to 'time.Time' format
	if startDateStr != "" && endDateStr != "" {
		startDate, err = time.Parse("2006-01-02 15:04:05", startDateStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid start date")
			requestCTX.SetErr(err, 400)
			return
		}

		endDate, err = time.Parse("2006-01-02 15:04:05", endDateStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid end date")
			requestCTX.SetErr(err, 400)
			return
		}
	}

	if itemID == "" {
		err = errors.New("item_id is a required field in query params")
		requestCTX.SetErr(err, 400)
		return
	}
	_, err = primitive.ObjectIDFromHex(itemID)
	if err != nil {
		err = errors.Wrap(err, "Invalid item ID")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Call 'Get Item Locations All Containers' method
	res, err := a.App.Location.GetItemLocationsAllContainers(itemID, clientID, search, startDate, endDate)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

func (a *API) getContainersOnLocation(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	locationID := r.URL.Query().Get("locationID")
	whID := requestCTX.WarehouseID.Hex()

	res, err := a.App.Location.GetContainersOnLocation(locationID, whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) getItemLocations(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	defer r.Body.Close()
	var ItemLocationsData schema.ValidateGetItemLocations

	// Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&ItemLocationsData); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// Validate if the required keys are present in the ItemLocationsData data as defined in the struct
	if errs := a.Validator.Validate(&ItemLocationsData); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}

	returnAll := false
	returnAllStr := r.URL.Query().Get("all")
	if returnAllStr == "true" {
		returnAll = true
	}

	allowHold := false
	allowHoldStr := r.URL.Query().Get("allow_hold")
	if allowHoldStr == "true" {
		allowHold = true
	}

	// move a items from source container to destination container
	res, err := a.App.Location.GetItemLocations(&ItemLocationsData, returnAll, allowHold)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) getItemLocationsGroup(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	defer r.Body.Close()
	var ItemLocationsData schema.ValidateGetItemLocationsGroup

	// Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&ItemLocationsData); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// Validate if the required keys are present in the ItemLocationsData data as defined in the struct
	if errs := a.Validator.Validate(&ItemLocationsData); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}

	returnAll := false
	returnAllStr := r.URL.Query().Get("all")
	if returnAllStr == "true" {
		returnAll = true
	}

	allowHold := false
	allowHoldStr := r.URL.Query().Get("allow_hold")
	if allowHoldStr == "true" {
		allowHold = true
	}

	// move a items from source container to destination container
	res, err := a.App.Location.GetItemLocationsGroup(&ItemLocationsData, returnAll, allowHold)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) holdLocation(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	defer r.Body.Close()
	var holdLocationData schema.ValidateHoldLocation

	// Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&holdLocationData); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// Validate if the required keys are present in the ItemLocationsData data as defined in the struct
	if errs := a.Validator.Validate(&holdLocationData); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}
	holdLocationData.UserID = requestCTX.UserClaim.(*auth.UserClaim).ID.Hex()
	holdLocationData.Username = requestCTX.UserClaim.(*auth.UserClaim).Username

	//Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	body, err := json.Marshal(holdLocationData)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal request body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "hold_location").Hex("user_claim", userClaim).Msg("Hold location request data.")

	// holds location
	res, err := a.App.Location.HoldLocation(&holdLocationData)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "hold_location").Str("status_code", "500").Err(err).Msg("Hold location error response.")
		requestCTX.SetErr(err, 400)
		return
	}

	//Logging response data for tracking
	response, err := json.Marshal(res)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "hold_location").Hex("response_data", response).Str("status_code", "200").Msg("Hold location response data.")

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) suggestLocations(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	defer r.Body.Close()
	var suggestLocationsData schema.ValidateSuggestLocation

	//Reading request body to bytes
	body, _ := io.ReadAll(r.Body)
	// Converting request body data into native golang structure
	err := json.Unmarshal(body, &suggestLocationsData)
	if err != nil {
		err = errors.Wrap(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// Validate if the required keys are present in the ItemLocationsData data as defined in the struct
	if errs := a.Validator.Validate(&suggestLocationsData); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}
	if suggestLocationsData.ContainerID == nil && suggestLocationsData.ItemID == nil {
		err = errors.Wrapf(err, "Invalid Request Data: Either item id or container id is required.")
		requestCTX.SetErr(err, 400)
		return
	}
	suggestLocationsData.UserID = requestCTX.UserClaim.(*auth.UserClaim).ID.Hex()
	suggestLocationsData.Username = requestCTX.UserClaim.(*auth.UserClaim).Username
	suggestLocationsData.WarehouseID = requestCTX.WarehouseID.Hex()

	//Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "suggest_empty_locations").Hex("user_claim", userClaim).Msg("Suggest Empty Locations request data.")

	// holds location
	res, err := a.App.Location.SuggestEmptyLocations(&suggestLocationsData)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "suggest_empty_locations").Str("status_code", "500").Err(err).Msg("Suggest Empty Locations error response.")
		requestCTX.SetErr(err, 400)
		return
	}

	//Logging response data for tracking
	response, err := json.Marshal(res)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "suggest_empty_locations").Hex("response_data", response).Str("status_code", "200").Msg("Suggest Empty Locations response data.")

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) getTrackedItemDetails(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	locationID := r.URL.Query().Get("location_id")
	if locationID == "" {
		requestCTX.SetErr(errors.New("location id is required"), 400)
		return
	}

	itemID := r.URL.Query().Get("item_id")
	if itemID == "" {
		requestCTX.SetErr(errors.New("item id is required"), 400)
		return
	}

	res, err := a.App.Location.GetTrackedItemDetails(locationID, itemID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) searchLocations(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	whID := requestCTX.WarehouseID.Hex()
	queryRes := r.URL.Query().Get("resources")
	if queryRes == "" {
		requestCTX.SetErr(errors.New("Resources are required"), 400)
		return
	}

	searchQuery := r.URL.Query().Get("searchQuery")

	resources := strings.Split(queryRes, ",")

	res, err := a.App.Location.SearchLocations(searchQuery, whID, resources)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) suggestLocationsForWorkOrder(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	defer r.Body.Close()
	var suggestLocationsData schema.ValidateSuggestLocationForWorkOrder

	//Reading request body to bytes
	body, _ := io.ReadAll(r.Body)
	// Converting request body data into native golang structure
	err := json.Unmarshal(body, &suggestLocationsData)
	if err != nil {
		err = errors.Wrap(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// Validate if the required keys are present in the ItemLocationsData data as defined in the struct
	if errs := a.Validator.Validate(&suggestLocationsData); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}
	suggestLocationsData.UserID = requestCTX.UserClaim.(*auth.UserClaim).ID.Hex()
	suggestLocationsData.Username = requestCTX.UserClaim.(*auth.UserClaim).Username
	suggestLocationsData.WarehouseID = requestCTX.WarehouseID.Hex()

	//Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "suggest_locations_for_work_order").Hex("user_claim", userClaim).Msg("Suggest Locations For Work Order request data.")

	// suggest locations for work order
	res, err := a.App.Location.SuggestLocationsForWorkOrder(&suggestLocationsData)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "suggest_locations_for_work_order").Str("status_code", "500").Err(err).Msg("Suggest Locations For Work Order error response.")
		requestCTX.SetErr(err, 400)
		return
	}

	//Logging response data for tracking
	response, err := json.Marshal(res)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "suggest_locations_for_work_order").Hex("response_data", response).Str("status_code", "200").Msg("Suggest Locations For Work Order response data.")

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) deleteLocations(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	defer r.Body.Close()
	var deleteLocationsForm schema.ValidateDeleteLocations

	//Reading request body to bytes
	body, _ := io.ReadAll(r.Body)
	// Converting request body data into native golang structure
	err := json.Unmarshal(body, &deleteLocationsForm)
	if err != nil {
		err = errors.Wrap(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// Validate if the required keys are present in the ItemLocationsData data as defined in the struct
	if errs := a.Validator.Validate(&deleteLocationsForm); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}
	deleteLocationsForm.UserID = requestCTX.UserClaim.(*auth.UserClaim).ID.Hex()
	deleteLocationsForm.Username = requestCTX.UserClaim.(*auth.UserClaim).Username
	deleteLocationsForm.Email = requestCTX.UserClaim.(*auth.UserClaim).Email
	deleteLocationsForm.Name = requestCTX.UserClaim.(*auth.UserClaim).FullName
	deleteLocationsForm.WarehouseID = requestCTX.WarehouseID

	//Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "suggest_locations_for_work_order").Hex("user_claim", userClaim).Msg("Suggest Locations For Work Order request data.")

	// DeleteLocations deletes locations
	deletedLoc, nonDeletedLoc, err := a.App.Location.DeleteLocations(&deleteLocationsForm)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "suggest_locations_for_work_order").Str("status_code", "500").Err(err).Msg("Suggest Locations For Work Order error response.")
		requestCTX.SetErr(err, 400)
		return
	}

	resp := map[string]interface{}{
		"deleted_locations":     deletedLoc,
		"non_deleted_locations": nonDeletedLoc,
	}

	//Logging response data for tracking
	response, err := json.Marshal(resp)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "suggest_locations_for_work_order").Hex("response_data", response).Str("status_code", "200").Msg("Suggest Locations For Work Order response data.")

	requestCTX.SetAppResponse(resp, 200)
}

// # Get All Warehouse Locations
func (a *API) getAllWarehouseLocations(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Error Object
	var err error

	// # Get 'warehouse ID' from request context
	warehouseID := requestCTX.WarehouseID.Hex()
	if warehouseID == "" {
		err = errors.New("warehouse ID is required in request context")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'search query' from query params
	search := r.URL.Query().Get("search")

	// # Call 'Get All Warehouse Locations' method
	res, err := a.App.Location.GetAllWarehouseLocations(warehouseID, search)
	if err != nil {
		requestCTX.SetErr(err, 500)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

// # Get Item Locations All Containers Layout
func (a *API) getItemLocationsAllContainersLayout(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Error Object
	var err error

	// # Get 'warehouse ID' from request context
	whID := requestCTX.WarehouseID

	// # Get the client timezone location
	location, err := a.getClientTimeZoneLocation(&whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'search query' from query params
	search := r.URL.Query().Get("search")

	// # Expiration Date Filter: Get 'start date' and 'end date' from query params
	startDateStr := r.URL.Query().Get("from_date")
	endDateStr := r.URL.Query().Get("to_date")

	// # Get 'item ID' from query params
	itemID := r.URL.Query().Get("item_id")

	// # Get Empty 'client ID'
	var clientID string = ""

	var startDate, endDate time.Time

	// # Validate and Format the 'date range'
	var format string = "empty" // # Default to 'empty string'
	startDateStr, endDateStr, err = a.validateAndFormatDateRange(startDateStr, endDateStr, format, location)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Convert the 'string' date to 'time.Time' format
	if startDateStr != "" && endDateStr != "" {
		startDate, err = time.Parse("2006-01-02 15:04:05", startDateStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid start date")
			requestCTX.SetErr(err, 400)
			return
		}

		endDate, err = time.Parse("2006-01-02 15:04:05", endDateStr)
		if err != nil {
			err = errors.Wrap(err, "Invalid end date")
			requestCTX.SetErr(err, 400)
			return
		}
	}

	if itemID == "" {
		err = errors.New("item_id is a required field in query params")
		requestCTX.SetErr(err, 400)
		return
	}
	_, err = primitive.ObjectIDFromHex(itemID)
	if err != nil {
		err = errors.Wrap(err, "Invalid item ID")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Call 'Get Item Locations All Containers' method
	res, err := a.App.Location.GetItemLocationsAllContainers(itemID, clientID, search, startDate, endDate)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

// # Get Location Details Layout
func (a *API) getLocationDetailsLayout(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Error Object
	var err error

	// # Get 'warehouse ID' from request context
	warehouseID := requestCTX.WarehouseID.Hex()
	if warehouseID == "" {
		err = errors.New("warehouse ID is required in request context")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'location ID' from query params
	locationID := r.URL.Query().Get("location_id")

	if locationID == "" {
		err = errors.New("location_id is a required field in query params")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'client ID' from query params
	clientID := r.URL.Query().Get("client_id")

	if clientID == "" {
		err = errors.New("client_id is a required field in query params")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Call 'Get Location Details Layout' method
	res, err := a.App.Location.GetLocationDetailsLayout(locationID, clientID, warehouseID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}
