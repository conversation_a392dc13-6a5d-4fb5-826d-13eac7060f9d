package api

// InitRoutes initializes all the endpoints
func (a *API) InitRoutes() {

	// # Root Routes
	a.Router.Root.Handle("/", a.requestHandler(a.redirect)).Methods("GET")
	a.Router.Root.Handle("/sample", a.requestWithAuthHandler(a.sample)).Methods("GET")

	// # Container Routes
	a.Router.Container.Handle("", a.requestWithAuthHandler(a.getContainer)).Methods("GET")
	a.Router.Container.Handle("/qr_code", a.requestWithAuthHandler(a.generateContainerQRCodes)).Methods("POST")
	// # Put Away -> Transfer Location
	a.Router.Container.Handle("/move", a.requestWithAuthHandler(a.moveItems)).Methods("POST")
	a.Router.Container.Handle("/type", a.requestWithAuthHandler(a.createContainerType)).Methods("POST")
	a.Router.Container.Handle("/type", a.requestWithAuthHandler(a.getContainerTypes)).Methods("GET")
	a.Router.Container.Handle("/item_containers", a.requestWithAuthHandler(a.getItemContainers)).Methods("POST")
	a.Router.Container.Handle("/check_container_receivable", a.requestWithAuthHandler(a.checkReceivable)).Methods("GET")
	a.Router.Container.Handle("/search", a.requestWithAuthHandler(a.getReceivableContainers)).Methods("GET")
	a.Router.Container.Handle("/brute_picking_suggestion", a.requestWithAuthHandler(a.getBrutePickingSuggestion)).Methods("GET")
	a.Router.Container.Handle("/delete/csv", a.requestWithAuthHandler(a.deleteContainers)).Methods("POST")
	a.Router.Container.Handle("/delete", a.requestWithAuthHandler(a.deleteContainer)).Methods("POST")

	// # Location Routes
	a.Router.Location.Handle("", a.requestWithAuthHandler(a.getLocation)).Methods("GET")
	a.Router.Location.Handle("/aggregate", a.requestWithAuthHandler(a.getAggregateLocation)).Methods("GET")
	a.Router.Location.Handle("/all_containers", a.requestWithAuthHandler(a.getContainersOnLocation)).Methods("GET")
	a.Router.Location.Handle("/item_locations", a.requestWithAuthHandler(a.getItemLocations)).Methods("POST")
	a.Router.Location.Handle("/item_locations_group", a.requestWithAuthHandler(a.getItemLocationsGroup)).Methods("POST")
	a.Router.Location.Handle("/hold", a.requestWithAuthHandler(a.holdLocation)).Methods("POST")
	a.Router.Location.Handle("/suggest", a.requestWithAuthHandler(a.suggestLocations)).Methods("POST")
	a.Router.Location.Handle("/tracked_item_details", a.requestWithAuthHandler(a.getTrackedItemDetails)).Methods("GET")
	a.Router.Location.Handle("/search", a.requestWithAuthHandler(a.searchLocations)).Methods("GET")
	a.Router.Location.Handle("/suggest/work_order", a.requestWithAuthHandler(a.suggestLocationsForWorkOrder)).Methods("POST")
	a.Router.Location.Handle("/delete", a.requestWithAuthHandler(a.deleteLocations)).Methods("POST")

	// # Inventory Routes
	a.Router.Inventory.Handle("", a.requestWithAuthHandler(a.getInventory)).Methods("GET")
	a.Router.Inventory.Handle("/audit", a.requestWithAuthHandler(a.auditInventory)).Methods("POST")
	a.Router.Inventory.Handle("/search", a.requestWithAuthHandler(a.searchInventory)).Methods("GET")
	a.Router.Inventory.Handle("/consolidated", a.requestWithAuthHandler(a.getConsolidatedInventory)).Methods("GET")
	a.Router.Inventory.Handle("/org", a.requestWithAuthHandler(a.getOrgInventory)).Methods("GET")
	a.Router.Inventory.Handle("/area_details", a.requestWithAuthHandler(a.getAreaDetails)).Methods("GET")
	// # Note: Deprecated API ('/inventory/tracked_inventory') -> Plan to delete this API in the future
	a.Router.Inventory.Handle("/tracked_inventory", a.requestWithAuthHandler(a.getItemTrackedInventory)).Methods("GET")

	// # Item Routes
	a.Router.Item.Handle("/locations/all/containers", a.requestWithAuthHandler(a.getItemLocationsAllContainers)).Methods("GET")
	a.Router.Item.Handle("/trackers", a.requestWithAuthHandler(a.getItemTrackedInventory)).Methods("GET")
	a.Router.Item.Handle("/transactions", a.requestWithAuthHandler(a.getItemTransactions)).Methods("GET")

	// # Location Layout Routes
	a.Router.LocationLayout.Handle("/search", a.requestWithAuthHandler(a.getLocationBySearch)).Methods("GET")
	a.Router.LocationLayout.Handle("/search/item", a.requestWithAuthHandler(a.getAllWarehouseItems)).Methods("GET")
	a.Router.LocationLayout.Handle("/search/location", a.requestWithAuthHandler(a.getAllWarehouseLocations)).Methods("GET")
	a.Router.LocationLayout.Handle("/search/lp", a.requestWithAuthHandler(a.getAllWarehouseContainers)).Methods("GET")
	a.Router.LocationLayout.Handle("/item", a.requestWithAuthHandler(a.getItemLocationsAllContainersLayout)).Methods("GET")
	a.Router.LocationLayout.Handle("/client", a.requestWithAuthHandler(a.getClientLocationsAllContainersLayout)).Methods("GET")
	a.Router.LocationLayout.Handle("/location", a.requestWithAuthHandler(a.getLocationDetailsLayout)).Methods("GET")
	a.Router.LocationLayout.Handle("/lp", a.requestWithAuthHandler(a.getContainerDetailsLayout)).Methods("GET")

	// # Transaction Routes
	// # Note: Deprecated API ('/transaction/history') -> Plan to delete this API in the future
	a.Router.Transaction.Handle("/history", a.requestWithAuthHandler(a.getItemTransactions)).Methods("GET")

	// # Script Routes
	a.Router.Script.Handle("/update_dynamic_columns", a.requestWithAuthHandler(a.updateDynamicColumns)).Methods("POST")

	// # Client Routes
	a.Router.Client.Handle("/inventory/get", a.requestWithAuthHandler(a.getClientInventory)).Methods("GET")
	a.Router.Client.Handle("/item/history", a.requestWithAuthHandler(a.getClientItemTransactions)).Methods("GET")
	a.Router.Client.Handle("/item/tracked_inventory", a.requestWithAuthHandler(a.getClientItemTrackedInventory)).Methods("GET")

	// # Cart Routes
	a.Router.Cart.Handle("", a.requestWithAuthHandler(a.getCart)).Methods("GET")
	a.Router.Cart.Handle("/get_all", a.requestWithAuthHandler(a.getCarts)).Methods("GET")
	a.Router.Cart.Handle("/create", a.requestWithAuthHandler(a.createCart)).Methods("POST")
	a.Router.Cart.Handle("/prepare", a.requestWithAuthHandler(a.prepareCart)).Methods("POST")
	a.Router.Cart.Handle("/order", a.requestWithAuthHandler(a.getOrderCartDetails)).Methods("GET")
	a.Router.Cart.Handle("/remove/tote", a.requestWithAuthHandler(a.removeToteFromCart)).Methods("POST")
	a.Router.Cart.Handle("/print", a.requestWithAuthHandler(a.printCart)).Methods("POST")

	// # Tote Routes
	a.Router.Tote.Handle("", a.requestWithAuthHandler(a.getTote)).Methods("GET")
	a.Router.Tote.Handle("/get_all", a.requestWithAuthHandler(a.getTotes)).Methods("GET")
	a.Router.Tote.Handle("/create", a.requestWithAuthHandler(a.createTote)).Methods("POST")
	a.Router.Tote.Handle("/type", a.requestWithAuthHandler(a.getToteTypes)).Methods("GET")
}
