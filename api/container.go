package api

import (
	"encoding/json"
	"inventory/model"
	"inventory/schema"
	"inventory/server/auth"
	"inventory/server/handler"
	"net/http"

	"github.com/pkg/errors"
	uuid "github.com/satori/go.uuid"
)

// # Generate QR codes to identify the containers
func (a *API) generateContainerQRCodes(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Get the warehouse ID from the request context
	whID := requestCTX.WarehouseID.Hex()

	// # Get the warehouse name from the user claim
	whName := requestCTX.UserClaim.(*auth.UserClaim).Services[requestCTX.PlatformID][requestCTX.WarehouseID].WarehouseName

	// # Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Close Request Body
	defer r.Body.Close()

	// # Request Data Struct
	var ContainerQRData schema.ValidateContainerQRCode

	// # Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&ContainerQRData); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Validate if the required keys are present in the ContainerQRData data as defined in the struct
	if errs := a.Validator.Validate(&ContainerQRData); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}

	// # Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	body, err := json.Marshal(ContainerQRData)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal request body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "generate_container_qr").Hex("user_claim", userClaim).Msg("Generate Container QR request data.")

	// # Generate QR codes to identify containers
	res, err := a.App.Container.GenerateContainerQRCodes(&ContainerQRData, whID, whName)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "generate_container_qr").Str("status_code", "500").Err(err).Msg("Generate Container QR error response.")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Logging response data for tracking
	response, err := json.Marshal(res)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "generate_container_qr").Hex("response_data", response).Str("status_code", "200").Msg("Generate Container QR response data.")

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

// Validate request data for - get container details
func (a *API) getContainer(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	container_id, err := uuid.FromString(r.URL.Query().Get("containerID"))
	if err != nil {
		err = errors.Wrap(err, "invalid container id")
		requestCTX.SetErr(err, 400)
		return
	}

	res, err := a.App.Container.GetContainer(container_id)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) moveItems(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}
	whID := requestCTX.WarehouseID.Hex()

	operationType := r.URL.Query().Get("operation_type")
	if operationType == "" {
		requestCTX.SetErr(errors.New("Operation type is required"), 400)
		return
	}

	defer r.Body.Close()
	var MoveItemsData schema.ValidateMoveItems

	// Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&MoveItemsData); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// Validate if the required keys are present in the MoveItemsData data as defined in the struct
	if errs := a.Validator.Validate(&MoveItemsData); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}

	if MoveItemsData.SourceLocation != nil {
		if len(*MoveItemsData.SourceLocation) != 24 {
			requestCTX.SetErr(errors.New("Invalid source location id"), 400)
			return
		}
	}
	if MoveItemsData.DestinationLocation != nil {
		if len(*MoveItemsData.DestinationLocation) != 24 {
			requestCTX.SetErr(errors.New("Invalid destination location id"), 400)
			return
		}
	}
	if MoveItemsData.ExplicitDestinationLocation != nil {
		if len(*MoveItemsData.ExplicitDestinationLocation) != 24 {
			requestCTX.SetErr(errors.New("Invalid explicit destination location id"), 400)
			return
		}
	}

	//Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	body, err := json.Marshal(MoveItemsData)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal request body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "item_transfer").Hex("user_claim", userClaim).Msg("Item transfer request data.")

	sessionDetails := &schema.SessionDetails{
		RequestID: requestCTX.RequestID,
		Username:  requestCTX.UserClaim.(*auth.UserClaim).Username,
		UserID:    requestCTX.UserClaim.(*auth.UserClaim).ID.Hex(),
	}

	// move a items from source container to destination container
	res, err := a.App.Container.MoveItems(&MoveItemsData, whID, operationType, sessionDetails)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "item_transfer").Str("status_code", "500").Err(err).Msg("Item transfer error response.")
		requestCTX.SetErr(err, 400)
		return
	}

	//Logging response data for tracking
	response, err := json.Marshal(res)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "item_transfer").Hex("response_data", response).Str("status_code", "200").Msg("Item transfer response data.")

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) createContainerType(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	whID := requestCTX.WarehouseID.Hex()

	// Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	defer r.Body.Close()
	var ContainerTypeData schema.ValidateCreateContainerType

	// Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&ContainerTypeData); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// Validate if the required keys are present in the ContainerTypeData data as defined in the struct
	if errs := a.Validator.Validate(&ContainerTypeData); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}

	//Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	body, err := json.Marshal(ContainerTypeData)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal request body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "create_container_type").Hex("user_claim", userClaim).Msg("Create Container Type request data.")

	// move a items from source container to destination container
	res, err := a.App.Container.CreateContainerType(&ContainerTypeData, whID)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "create_container_type").Str("status_code", "500").Err(err).Msg("Create Container Type error response.")
		requestCTX.SetErr(err, 500)
		return
	}

	//Logging response data for tracking
	response, err := json.Marshal(res)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "create_container_type").Hex("response_data", response).Str("status_code", "200").Msg("Create Container Type response data.")

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) getContainerTypes(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	whID := requestCTX.WarehouseID.Hex()

	// move a items from source container to destination container
	res, err := a.App.Container.GetContainerTypes(whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) getItemContainers(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	defer r.Body.Close()
	var ItemContainersData schema.ValidateGetItemContainers

	// Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&ItemContainersData); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// Validate if the required keys are present in the ItemContainersData data as defined in the struct
	if errs := a.Validator.Validate(&ItemContainersData); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}

	// move a items from source container to destination container
	res, err := a.App.Container.GetItemContainers(&ItemContainersData)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) checkReceivable(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	container_id := r.URL.Query().Get("container_id")
	if container_id == "" {
		requestCTX.SetErr(errors.New("Container is required"), 400)
		return
	}
	containerID, err := uuid.FromString(container_id)
	if err != nil {
		err = errors.Wrap(err, "invalid container id")
		requestCTX.SetErr(err, 400)
		return
	}

	locationID := r.URL.Query().Get("location_id")
	if locationID == "" {
		requestCTX.SetErr(errors.New("location is required"), 400)
		return
	}

	clientID := r.URL.Query().Get("client_id")
	if clientID == "" {
		requestCTX.SetErr(errors.New("client id is required"), 400)
		return
	}

	resource := r.URL.Query().Get("resource")
	resourceID := r.URL.Query().Get("resource_id") // NOTE - resource_id is optional, if not provided it will be empty

	// checking for nil warehouse ID
	if _, ok := requestCTX.UserClaim.(*auth.UserClaim).Services[requestCTX.PlatformID][requestCTX.WarehouseID]; !ok {
		e := errors.New("User is not assigned to warehouse")
		requestCTX.SetErr(e, 400)
		return
	}

	res, err := a.App.Container.CheckReceivable(containerID, locationID, clientID, requestCTX.WarehouseID.Hex(), resource, resourceID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) getReceivableContainers(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	whID := r.URL.Query().Get("warehouseID")
	if whID == "" {
		requestCTX.SetErr(errors.New("warehouse id is required"), 400)
		return
	}

	searchQuery := r.URL.Query().Get("searchQuery")

	res, err := a.App.Container.GetReceivableContainers(searchQuery, whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) getBrutePickingSuggestion(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	whID := requestCTX.WarehouseID.Hex()

	itemID := r.URL.Query().Get("item_id")
	if itemID == "" {
		requestCTX.SetErr(errors.New("Item id is required"), 400)
		return
	}

	res, err := a.App.Container.GetBrutePickingSuggestion(itemID, whID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}

// deleteContainers reads a file containing container ids/code/refNo and deletes them
func (a *API) deleteContainers(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Checks and handles if any panic occurs
	defer a.App.Utils.HandlePanic(requestCTX)

	// reading uploading file with the provided form key
	f, _, err := r.FormFile("file")
	if err != nil {
		e := errors.Wrap(err, "failed to read file")
		requestCTX.SetErr(e, 400)
		return
	}
	defer f.Close()
	if err := r.ParseForm(); err != nil {
		e := errors.Wrap(err, "failed to read file")
		requestCTX.SetErr(e, 400)
		return
	}

	// fetching container field from the form
	containerField := r.FormValue("container_field")

	//Logging request data for tracking
	userClaim, _ := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	userModel := &model.UserModel{
		UserID:   requestCTX.UserClaim.(*auth.UserClaim).ID,
		Name:     requestCTX.UserClaim.(*auth.UserClaim).FullName,
		Email:    requestCTX.UserClaim.(*auth.UserClaim).Email,
		Username: requestCTX.UserClaim.(*auth.UserClaim).Username,
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Str("action", "delete_containers").Hex("request_data", []byte(containerField)).Hex("user_claim", userClaim).Msg("Delete Containers request data.")

	// DeleteContainers deletes the containers from the warehouse
	res, err := a.App.Container.DeleteContainers(f, containerField, requestCTX.RequestID, requestCTX.WarehouseID, userModel)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "delete_containers").Str("status_code", "500").Err(err).Msg("Delete Containers error response.")
		requestCTX.SetErr(err, 500)
		return
	}

	//Logging response data for tracking
	response, _ := json.Marshal(res)
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "delete_containers").Str("request_status", "responding").Hex("response_data", response).Str("status_code", "200").Msg("Delete Containers response data.")
	requestCTX.SetAppResponse(res, 200)

	requestCTX.SetAppResponse(res, 200)
}

func (a *API) deleteContainer(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	whID := requestCTX.WarehouseID.Hex()

	// Check the request content length for empty request data
	if length := r.ContentLength; length == 0 {
		var err error
		err = errors.Wrapf(err, "Empty request data")
		requestCTX.SetErr(err, 400)
		return
	}

	defer r.Body.Close()
	var deleteContainerData schema.ValidateDeleteContainer

	// Converting request body data into native golang structure
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&deleteContainerData); err != nil {
		err = errors.Wrapf(err, "Unable to read json schema")
		requestCTX.SetErr(err, 400)
		return
	}

	// Validate if the required keys are present in the ContainerTypeData data as defined in the struct
	if errs := a.Validator.Validate(&deleteContainerData); errs != nil {
		requestCTX.SetErrs(errs, 400)
		return
	}

	//Logging request data for tracking
	userClaim, err := json.Marshal(requestCTX.UserClaim.(*auth.UserClaim))
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal user claim")
		requestCTX.SetErr(err, 400)
		return
	}
	body, err := json.Marshal(deleteContainerData)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal request body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "initiating").Hex("request_data", body).Str("action", "delete_container").Hex("user_claim", userClaim).Msg("Delete Container request data.")

	// deleteContainer deletes the container from the warehouse
	res, err := a.App.Container.DeleteContainer(&deleteContainerData, whID)
	if err != nil {
		a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("action", "delete_container").Str("status_code", "500").Err(err).Msg("Delete Container error response.")
		requestCTX.SetErr(err, 500)
		return
	}

	//Logging response data for tracking
	response, err := json.Marshal(res)
	if err != nil {
		err = errors.Wrapf(err, "Failed to marshal response body")
		requestCTX.SetErr(err, 400)
		return
	}
	a.Logger.Log().Str("request_id", requestCTX.RequestID).Str("request_status", "responding").Str("action", "delete_container").Hex("response_data", response).Str("status_code", "200").Msg("Delete Container response data.")

	requestCTX.SetAppResponse(res, 200)
}

// # Get All Warehouse Containers
func (a *API) getAllWarehouseContainers(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Error Object
	var err error

	// # Get 'warehouse ID' from request context
	warehouseID := requestCTX.WarehouseID.Hex()
	if warehouseID == "" {
		err = errors.New("warehouse ID is required in request context")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'search query' from query params
	search := r.URL.Query().Get("search")

	// # Call 'Get All Warehouse Containers' method
	res, err := a.App.Container.GetAllWarehouseContainers(warehouseID, search)
	if err != nil {
		requestCTX.SetErr(err, 500)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}

// # Get Container Details Layout
func (a *API) getContainerDetailsLayout(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// # Handle panic and publish to sentry
	defer a.App.Utils.HandlePanic(requestCTX)

	// # Error Object
	var err error

	// # Get 'warehouse ID' from request context
	warehouseID := requestCTX.WarehouseID.Hex()
	if warehouseID == "" {
		err = errors.New("warehouse ID is required in request context")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'container ID' from query params
	containerID := r.URL.Query().Get("container_id")

	if containerID == "" {
		err = errors.New("container_id is a required field in query params")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Get 'client ID' from query params
	clientID := r.URL.Query().Get("client_id")

	if clientID == "" {
		err = errors.New("client_id is a required field in query params")
		requestCTX.SetErr(err, 400)
		return
	}

	// # Call 'Get Container Details Layout' method
	res, err := a.App.Container.GetContainerDetailsLayout(containerID, clientID, warehouseID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	// # Set the response
	requestCTX.SetAppResponse(res, 200)
}
