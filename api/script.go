package api

import (
	"errors"
	"inventory/server/handler"
	"net/http"
)

func (a *API) updateDynamicColumns(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// change for branch deployment

	clientID := r.URL.Query().Get("client_id")
	columnName := r.URL.Query().Get("column_name")
	if clientID == "" || columnName == "" {
		requestCTX.SetErr(errors.New("client_id and column_name is required"), 400)
		return
	}

	res, err := a.App.Script.UpdateDynamicColumns(clientID, columnName)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}
