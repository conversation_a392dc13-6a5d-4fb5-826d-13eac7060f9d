package api

import (
	"inventory/server/handler"
	"net/http"
)

func (a *API) sample(requestCTX *handler.RequestContext, w http.ResponseWriter, r *http.Request) {
	// change for branch deployment
	whID := requestCTX.WarehouseID.Hex()
	clientID := r.URL.Query().Get("client_id")

	res, err := a.App.Sample.Sample(whID, clientID)
	if err != nil {
		requestCTX.SetErr(err, 400)
		return
	}

	requestCTX.SetAppResponse(res, 200)
}
