package api

import (
	"fmt"
	"inventory/app"
	"inventory/server/auth"
	"inventory/server/config"
	"inventory/server/handler"
	"inventory/server/validator"
	"net/http"

	"github.com/gorilla/mux"
	"github.com/rs/zerolog"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
)

// API := returns API struct
type API struct {
	Router      *Router
	MainRouter  *mux.Router
	Logger      *zerolog.Logger
	Config      *config.APIConfig
	TokenAuth   auth.TokenAuth
	SessionAuth auth.SessionAuth
	Validator   *validator.Validator

	App *app.App
}

// Options contain all the dependencies required to create a new instance of api
// and is passed in NewAPI func as argument
type Options struct {
	MainRouter  *mux.Router
	Logger      *zerolog.Logger
	Config      *config.APIConfig
	TokenAuth   auth.TokenAuth
	SessionAuth auth.SessionAuth
	Validator   *validator.Validator
}

// Router stores all the endpoints available for the server to respond.
type Router struct {
	APIV1          *mux.Router // # API Version 1
	Root           *mux.Router
	Container      *mux.Router
	Location       *mux.Router
	Inventory      *mux.Router
	Transaction    *mux.Router
	Script         *mux.Router
	StaticRoot     *mux.Router
	Client         *mux.Router
	Item           *mux.Router
	LocationLayout *mux.Router
	Cart           *mux.Router
	Tote           *mux.Router
}

// NewAPI returns API instance
func NewAPI(opts *Options) *API {
	api := API{
		MainRouter:  opts.MainRouter,
		Router:      &Router{},
		Config:      opts.Config,
		TokenAuth:   opts.TokenAuth,
		SessionAuth: opts.SessionAuth,
		Logger:      opts.Logger,
		Validator:   opts.Validator,
	}
	api.setupRoutes()
	return &api
}

func (a *API) setupRoutes() {
	// # API Versioning Prefix
	apiV1 := a.MainRouter.PathPrefix("/v1").Subrouter()
	a.Router.APIV1 = apiV1

	a.Router.Root = a.MainRouter

	a.Router.Container = a.MainRouter.PathPrefix("/container").Subrouter()
	a.Router.Location = a.MainRouter.PathPrefix("/location").Subrouter()
	a.Router.Inventory = a.MainRouter.PathPrefix("/inventory").Subrouter()
	a.Router.Transaction = a.MainRouter.PathPrefix("/transaction").Subrouter()
	a.Router.Script = a.MainRouter.PathPrefix("/script").Subrouter()
	a.Router.Client = a.MainRouter.PathPrefix("/client").Subrouter()

	a.Router.Item = apiV1.PathPrefix("/item").Subrouter()
	a.Router.LocationLayout = apiV1.PathPrefix("/location/layout").Subrouter()

	a.Router.Cart = a.MainRouter.PathPrefix("/cart").Subrouter()
	a.Router.Tote = a.MainRouter.PathPrefix("/tote").Subrouter()

	a.InitRoutes()

	if a.Config.EnableStaticRoute {
		a.Router.StaticRoot = a.MainRouter.PathPrefix("/static").Subrouter()
	}
}

func (a *API) requestHandler(h func(c *handler.RequestContext, w http.ResponseWriter, r *http.Request)) http.Handler {
	return &handler.Request{
		HandlerFunc: h,
		AuthFunc:    a.TokenAuth,
		Environment: a.Config.Mode,
		IsLoggedIn:  false,
	}
}

func (a *API) requestWithAuthHandler(h func(c *handler.RequestContext, w http.ResponseWriter, r *http.Request)) http.Handler {
	originalHandler := &handler.Request{
		HandlerFunc: h,
		AuthFunc:    a.TokenAuth,
		Environment: a.Config.Mode,
		IsLoggedIn:  true,
	}

	// Wrap with OpenTelemetry, including route information
	return otelhttp.NewHandler(
		originalHandler,
		"http.request", // Operation name
		otelhttp.WithSpanNameFormatter(func(operation string, r *http.Request) string {
			return fmt.Sprintf("%s %s", r.Method, r.URL.Path)
		}),
	)
}
