package util

import (
	"time"
)

// # Get First Of The Month: It returns the 'first day' of the 'month' for the provided 'date'
func GetFirstOfTheMonth(date time.Time) time.Time {
	currentYear, currentMonth, _ := date.Date()
	currentLocation := date.Location()
	firstOfMonth := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, currentLocation)
	return firstOfMonth
}

// # Get String Date: It converts the provided 'date' from type 'time.Time' to type 'string'
func GetStringDate(date *time.Time) string {
	// # Check if the 'date' is 'nil'
	if date == nil {
		return ""
	}

	// # Define the 'date' layout
	dateLayout := "2006-01-02 15:04:05"

	// # Format the 'date' into type 'string'
	dateStr := date.Format(dateLayout)

	// # Return the 'formatted' string 'date'
	return dateStr
}
