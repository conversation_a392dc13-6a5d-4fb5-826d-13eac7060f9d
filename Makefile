stage_cert:
	cd cert; ./stage-gen.sh; cd ..

prod_cert:
	cd cert; ./prod-gen.sh; cd ..

CA:
	cd cert; ./generate-ca.sh; cd ..

CSR:
	cd cert; ./generate-csr.sh; cd ..

SIGN:
	cd cert; ./ca-sign-certificate.sh; cd ..

# Used when setting up postgresql on local for the first time. Run the postgresql docker container
setupdb:
	docker run --name postgres -e POSTGRES_USER=leanafy -e POSTGRES_PASSWORD=leanafy -p 5432:5432 -d postgres:14-alpine

createdb:
	docker exec -it postgres createdb --username=leanafy --owner=leanafy inventorydb

dropdb:
	docker exec -it postgres dropdb --username=leanafy inventorydb

migrateup:
	migrate -path db/migrations -database "pgx://leanafy:leanafy@localhost:5432/inventorydb?sslmode=disable" up

migratedown:
	migrate -path db/migrations -database "pgx://leanafy:leanafy@localhost:5432/inventorydb?sslmode=disable" down

.PHONY: stage_cert prod_cert CA CSR SIGN setupdb createdb dropdb migrateup migratedown