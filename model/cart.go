package model

import (
	"time"

	uuid "github.com/satori/go.uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Cart struct {
	ID             *int64     `json:"id"`
	WarehouseID    *string    `json:"warehouse_id"`
	CartID         *uuid.UUID `json:"cart_id"`
	Code           *string    `json:"code"`
	QR             *string    `json:"qr"`
	Alias          *string    `json:"alias"`
	RetainTote     *bool      `json:"retain_tote"`
	CreatedAt      *time.Time `json:"created_at"`
	CreatedByID    *string    `json:"created_by_id"`
	CreatedByName  *string    `json:"created_by_name"`
	LastUpdatedAt  *time.Time `json:"last_updated_at"`
	Length         *float64   `json:"length"`
	Width          *float64   `json:"width"`
	Height         *float64   `json:"height"`
	DimsUnit       *string    `json:"dims_unit"`
	WeightCapacity *float64   `json:"weight_capacity"`
	WeightUnit     *string    `json:"weight_unit"`
	ToteCount      *int32     `json:"tote_count,omitempty"`

	CartItems    []*CartItem `json:"cart_items,omitempty"`
	Totes        []*Tote     `json:"totes,omitempty"`
	PicklistInfo *Picklist   `json:"picklist_info,omitempty"`
}

type PicklistInfo struct {
	ID     *primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Status string              `json:"status,omitempty" bson:"status,omitempty"`
}

type CartItem struct {
	ID             *int64     `json:"id"`
	WarehouseID    *string    `json:"warehouse_id"`
	CartID         *uuid.UUID `json:"cart_id"`
	CartCode       *string    `json:"cart_code"`
	ClientID       *string    `json:"client_id"`
	ClientName     *string    `json:"client_name"`
	OperationType  *string    `json:"operation_type"`
	ItemID         *string    `json:"item_id"`
	Name           *string    `json:"name"`
	SKU            *string    `json:"sku"`
	Scannable      *string    `json:"scannable"`
	BatchNumber    *string    `json:"batch_number"`
	SerialNumber   *string    `json:"serial_number"`
	ExpirationDate *time.Time `json:"expiration_date"`
	Quantity       *int32     `json:"quantity"`
	PicklistID     *string    `json:"picklist_id"`
	PicklistCode   *string    `json:"picklist_code"`
	OrderID        *string    `json:"order_id"`
	OrderCode      *string    `json:"order_code"`
	CreatedAt      *time.Time `json:"created_at"`
	CreatedByID    *string    `json:"created_by_id"`
	CreatedByName  *string    `json:"created_by_name"`
	LastUpdatedAt  *time.Time `json:"last_updated_at"`
}

type CartTotes struct {
	ID     *int64     `json:"id"`
	CartID *uuid.UUID `json:"cart_id"`
	ToteID *uuid.UUID `json:"tote_id"`
}

type Picklist struct {
	ID                      *primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	WarehouseID             *primitive.ObjectID `json:"warehouse_id,omitempty" bson:"warehouse_id,omitempty"`
	ClientID                *primitive.ObjectID `json:"client_id,omitempty" bson:"client_id,omitempty"`
	ClientName              string              `json:"client_name,omitempty" bson:"client_name,omitempty"`
	Code                    int64               `json:"code,omitempty" bson:"code,omitempty"`
	Type                    string              `json:"type,omitempty" bson:"type,omitempty"`
	PicklistDocumentUri     string              `json:"picklist_document_uri,omitempty" bson:"picklist_document_uri,omitempty"`
	Items                   []PickListItems     `json:"items,omitempty" bson:"items,omitempty"`
	Status                  string              `json:"status,omitempty" bson:"status,omitempty"`
	CreatedAt               *time.Time          `json:"created_at,omitempty" bson:"created_at,omitempty"`
	CreatedBy               *UserModel          `json:"created_by,omitempty" bson:"created_by,omitempty"`
	AssignedUsers           []UserModel         `json:"assigned_users,omitempty" bson:"assigned_users,omitempty"`
	ItemCount               int32               `json:"item_count,omitempty" bson:"item_count,omitempty"`
	Order                   interface{}         `json:"order,omitempty" bson:"order,omitempty"`
	PicklistSummaryDocument string              `json:"picklist_summary_document,omitempty" bson:"picklist_summary_document,omitempty"`
	ConsolidationStatus     string              `json:"consolidation_status,omitempty" bson:"consolidation_status,omitempty"`
	ConsolidationData       *ConsolidationData  `json:"consolidation_data,omitempty" bson:"consolidation_data,omitempty"`

	//TAT
	DueDate *time.Time `json:"due_date,omitempty" bson:"due_date,omitempty"`

	// Reclamation
	ReclamationID   *primitive.ObjectID `json:"reclamation_id,omitempty" bson:"reclamation_id,omitempty"`
	ReclamationCode int64               `json:"reclamation_code,omitempty" bson:"reclamation_code,omitempty"`

	// Wave Picklist
	WavepoolID     *primitive.ObjectID              `json:"wavepool_id,omitempty" bson:"wavepool_id,omitempty"`
	IsWavePicklist bool                             `json:"is_wave_picklist,omitempty" bson:"is_wave_picklist,omitempty"`
	Orders         map[primitive.ObjectID]WaveOrder `json:"orders,omitempty" bson:"orders,omitempty"`
	Cart           *PicklistCart                    `json:"cart,omitempty" bson:"cart,omitempty"`
	Totes          []Tote                           `json:"totes,omitempty" bson:"totes,omitempty"`
}

type ConsolidationData struct {
	ID   primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Code string             `json:"code,omitempty" bson:"code,omitempty"`
}

type WaveOrder struct {
	OrderID    primitive.ObjectID  `json:"order_id,omitempty" bson:"order_id,omitempty"`
	OrderCode  int64               `json:"order_code,omitempty" bson:"order_code,omitempty"`
	ClientID   *primitive.ObjectID `json:"client_id,omitempty" bson:"client_id,omitempty"`
	ClientName string              `json:"client_name,omitempty" bson:"client_name,omitempty"`
	Totes      []PicklistCart      `json:"totes,omitempty" bson:"totes,omitempty"`
	Status     string              `json:"status,omitempty" bson:"status,omitempty"`
}

type PickListItems struct {
	ItemID                    *primitive.ObjectID   `json:"item_id,omitempty" bson:"item_id,omitempty"`
	SKU                       string                `json:"sku,omitempty" bson:"sku,omitempty"`
	Scannable                 string                `json:"scannable,omitempty" bson:"scannable,omitempty"`
	Name                      string                `json:"name,omitempty" bson:"name,omitempty"`
	IsKit                     bool                  `json:"is_kit" bson:"is_kit"`
	IsSubItem                 bool                  `json:"is_sub_item,omitempty" bson:"is_sub_item,omitempty"`
	KitSKU                    string                `json:"kit_sku,omitempty" bson:"kit_sku,omitempty"`
	KitID                     *primitive.ObjectID   `json:"kit_id,omitempty" bson:"kit_id,omitempty"`
	BatchNumber               string                `json:"batch_number,omitempty" bson:"batch_number,omitempty"`
	SerialNumber              string                `json:"serial_number,omitempty" bson:"serial_number,omitempty"`
	Expiration                *time.Time            `json:"expiration,omitempty" bson:"expiration,omitempty"`
	Orders                    []PicklistOrder       `json:"orders,omitempty" bson:"orders,omitempty"`
	TotalQty                  int32                 `json:"total_qty,omitempty" bson:"total_qty,omitempty"`
	RemainingQty              int32                 `json:"remaining_qty" bson:"remaining_qty"`
	PickedFrom                []PickedFrom          `json:"picked_from,omitempty" bson:"picked_from,omitempty"`
	IsPickedUp                bool                  `json:"is_picked_up,omitempty" bson:"is_picked_up,omitempty"`
	BaseUnit                  string                `json:"base_unit,omitempty" bson:"base_unit,omitempty"`
	Image                     string                `json:"image,omitempty" bson:"image,omitempty"`
	RequiredTrackedQuantities []RequiredTrackedInfo `json:"required_tracked_quantities,omitempty" bson:"required_tracked_quantities,omitempty"`
}

type PicklistOrder struct {
	OrderID     *primitive.ObjectID `json:"order_id,omitempty" bson:"order_id,omitempty"`
	OrderCode   int64               `json:"order_code,omitempty" bson:"order_code,omitempty"`
	ClientID    *primitive.ObjectID `json:"client_id,omitempty" bson:"client_id,omitempty"`
	ClientName  string              `json:"client_name,omitempty" bson:"client_name,omitempty"`
	TotalQty    int32               `json:"total_qty,omitempty" bson:"total_qty,omitempty"`
	RequiredQty int32               `json:"required_qty,omitempty" bson:"required_qty,omitempty"`
	Unit        string              `json:"unit,omitempty" bson:"unit,omitempty"`
	Conversion  int32               `json:"conversion,omitempty" bson:"conversion,omitempty"`
}

type PickedFrom struct {
	LocationID        *primitive.ObjectID `json:"location_id,omitempty" bson:"location_id,omitempty"`
	LocationCode      string              `json:"location_code,omitempty" bson:"location_code,omitempty"`
	ContainerID       string              `json:"container_id,omitempty" bson:"container_id,omitempty"`
	ContainerCode     string              `json:"container_code,omitempty" bson:"container_code,omitempty"`
	ContainerRefNo    string              `json:"container_ref_no,omitempty" bson:"container_ref_no,omitempty"`
	IsContainerLoose  bool                `json:"is_container_loose" bson:"is_container_loose"`
	IsContainerPicked bool                `json:"is_container_picked" bson:"is_container_picked"`
	ItemID            *primitive.ObjectID `json:"item_id,omitempty" bson:"item_id,omitempty"`
	Quantity          int32               `json:"quantity,omitempty" bson:"quantity,omitempty"`
	PickedIn          []PickedIn          `json:"picked_in,omitempty" bson:"picked_in,omitempty"`

	BatchNumber  string     `json:"batch_number,omitempty" bson:"batch_number,omitempty"`
	SerialNumber string     `json:"serial_number,omitempty" bson:"serial_number,omitempty"`
	Expiration   *time.Time `json:"expiration,omitempty" bson:"expiration,omitempty"`
}

type PickedIn struct {
	Unit               string     `json:"unit,omitempty" bson:"unit,omitempty"`
	Quantity           int32      `json:"quantity,omitempty" bson:"quantity,omitempty"`
	BaseConversionRate int32      `json:"base_conversion_rate,omitempty" bson:"base_conversion_rate,omitempty"`
	PickedAt           *time.Time `json:"picked_at,omitempty" bson:"picked_at,omitempty"`
	PickedBy           *UserModel `json:"picked_by,omitempty" bson:"picked_by,omitempty"`
	IsFirstEntry       bool       `json:"is_first_entry,omitempty" bson:"is_first_entry,omitempty"`
}

type RequiredTrackedInfo struct {
	BatchNumber        string     `json:"batch_number,omitempty" bson:"batch_number,omitempty"`
	SerialNumber       string     `json:"serial_number,omitempty" bson:"serial_number,omitempty"`
	Expiration         *time.Time `json:"expiration,omitempty" bson:"expiration,omitempty"`
	Qty                int32      `json:"qty,omitempty" bson:"qty,omitempty"`
	TotalQty           int32      `json:"total_qty,omitempty" bson:"total_qty,omitempty"`
	RemainingQty       int32      `json:"remaining_qty,omitempty" bson:"remaining_qty,omitempty"`
	Unit               string     `json:"unit,omitempty" bson:"unit,omitempty"`
	BaseConversionRate int32      `json:"base_conversion_rate,omitempty" bson:"base_conversion_rate,omitempty"`
	BaseUnit           string     `json:"base_unit,omitempty" bson:"base_unit,omitempty"`
}

type PicklistCart struct {
	ID   string `json:"_id,omitempty" bson:"_id,omitempty"`
	Code string `json:"code,omitempty" bson:"code,omitempty"`
}
