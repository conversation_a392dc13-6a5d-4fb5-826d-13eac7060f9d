package model

import (
	"time"

	uuid "github.com/satori/go.uuid"
)

type Tote struct {
	ID            *int64     `json:"id"`
	WarehouseID   *string    `json:"warehouse_id"`
	ToteID        *uuid.UUID `json:"tote_id"`
	Code          *string    `json:"code"`
	QR            *string    `json:"qr"`
	Alias         *string    `json:"alias"`
	RefNo         *string    `json:"ref_no"`
	CreatedAt     *time.Time `json:"created_at"`
	CreatedByID   *string    `json:"created_by_id"`
	CreatedByName *string    `json:"created_by_name"`
	LastUpdatedAt *time.Time `json:"last_updated_at"`
	ToteTypeID    *int64     `json:"tote_type_id"`

	Length         *float64    `json:"length"`
	Width          *float64    `json:"width"`
	Height         *float64    `json:"height"`
	DimsUnit       *string     `json:"dims_unit"`
	WeightCapacity *float64    `json:"weight_capacity"`
	WeightUnit     *string     `json:"weight_unit"`
	Name           *string     `json:"tote_type_name"`
	ToteItems      []*ToteItem `json:"tote_items,omitempty"`

	// Cart Info
	CartInfo *CartInfo `json:"cart_info,omitempty"`
}

type CartInfo struct {
	CartID   *uuid.UUID `json:"cart_id"`
	CartCode *string    `json:"cart_code"`
}

type ToteItem struct {
	ID             *int64     `json:"id"`
	WarehouseID    *string    `json:"warehouse_id"`
	ToteID         *uuid.UUID `json:"tote_id"`
	ToteCode       *string    `json:"tote_code"`
	ClientID       *string    `json:"client_id"`
	ClientName     *string    `json:"client_name"`
	OperationType  *string    `json:"operation_type"`
	ItemID         *string    `json:"item_id"`
	Name           *string    `json:"name"`
	SKU            *string    `json:"sku"`
	Scannable      *string    `json:"scannable"`
	BatchNumber    *string    `json:"batch_number"`
	SerialNumber   *string    `json:"serial_number"`
	ExpirationDate *time.Time `json:"expiration_date"`
	Quantity       *int32     `json:"quantity"`
	PicklistID     *string    `json:"picklist_id"`
	PicklistCode   *string    `json:"picklist_code"`
	OrderID        *string    `json:"order_id"`
	OrderCode      *string    `json:"order_code"`
	CreatedAt      *time.Time `json:"created_at"`
	CreatedByID    *string    `json:"created_by_id"`
	CreatedByName  *string    `json:"created_by_name"`
	LastUpdatedAt  *time.Time `json:"last_updated_at"`
}

type ToteType struct {
	ID             *int64     `json:"id"`
	WarehouseID    *string    `json:"warehouse_id"`
	Name           *string    `json:"name"`
	Length         *float64   `json:"length"`
	Width          *float64   `json:"width"`
	Height         *float64   `json:"height"`
	DimsUnit       *string    `json:"dims_unit"`
	WeightCapacity *float64   `json:"weight_capacity"`
	WeightUnit     *string    `json:"weight_unit"`
	CreatedAt      *time.Time `json:"created_at"`
	CreatedByID    *string    `json:"created_by_id"`
	CreatedByName  *string    `json:"created_by_name"`
}
