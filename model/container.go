package model

import (
	"time"

	uuid "github.com/satori/go.uuid"
)

type Container struct {
	ID                *int64
	ItemID            *string
	ContainerID       *uuid.UUID
	ContainerTypeID   *int64
	WarehouseID       *string
	ClientID          *string
	LocationID        *string
	Quantity          *int32
	AvailableQty      *int32
	Code              *string
	LastUpdatedAt     *time.Time
	SKU               *string
	Scannable         *string
	BatchNumber       *string
	SerialNumber      *string
	ExpirationDate    *time.Time
	CreatedAt         *time.Time
	IsShipped         *bool
	ClientName        *string
	ContainerTypeName *string
	OnHold            bool
	OnHoldByID        *string
	FirstUsedAt       *time.Time
	RefNo             *string
	Name              *string
	BaseUnit          *string
	ReceivedAt        *time.Time
}

type ContainerType struct {
	ID             int
	WarehouseID    string
	Name           string
	OnlyShipsWhole bool
}

type CycleCountContainers struct {
	LocationID    *string
	LocationCode  *string
	ContainerID   *uuid.UUID
	ContainerCode *string
}

type ESContainer struct {
	WarehouseID     string
	Code            string
	SearchableCode  string
	ContainerID     string
	ContainerType   string
	ContainerTypeID int
}

type ContainerReportData struct {
	ClientName     *string
	ContainerID    uuid.UUID
	ContainerCode  string
	ContainerType  string
	LocationCode   *string
	OnHold         *bool
	ItemID         *string
	Sku            *string
	BatchNumber    *string
	SerialNumber   *string
	ExpirationDate *time.Time
	Quantity       int32
	InitialQty     int32
	ReceivedAt     *time.Time
	BaseUnit       *string
	Scannable      *string
	Name           *string
	Description    *string
	RefNo          *string
	LocationID     *string
	Strategy       *string
	FirstUsedAt    *time.Time
	CreatedAt      *time.Time
}
type WorkOrderContainer struct {
	ItemID         string
	ContainerID    uuid.UUID
	LocationID     string
	Quantity       int32
	Code           string
	BatchNumber    *string
	SerialNumber   *string
	ExpirationDate *time.Time
	LocationCode   string
	SKU            string
	ClientID       string
	ClientName     string
	WarehouseID    string
}

type LPCount struct {
	ItemID      string
	WarehouseID string
	ClientName  string
	SKU         string
	LPType      string
	Count       int32
}

// # Location Layout #

// # Container Object Model
type ContainerObject struct {
	ContainerID     *uuid.UUID    `json:"container_id"`
	ContainerTypeID *int64        `json:"container_type_id"`
	ClientID        *string       `json:"client_id"`
	WarehouseID     *string       `json:"warehouse_id"`
	LocationID      *string       `json:"location_id"`
	ClientName      *string       `json:"client_name"`
	LPCode          *string       `json:"lp_code"`
	QuantityInfo    *QuantityInfo `json:"quantity_info"`
	SKU             *string       `json:"sku"`
	Scannable       *string       `json:"scannable"`
	BatchNumber     *string       `json:"batch_number"`
	SerialNumber    *string       `json:"serial_number"`
	ExpirationDate  *time.Time    `json:"expiration_date"`
	OnHold          *bool         `json:"on_hold"`
}

// # Container Response Model
type ContainerResponse struct {
	Containers []*ContainerObject `json:"containers"`
}

// # Container Details Layout Model
type ContainerDetailsLayout struct {
	ContainerID     *uuid.UUID `json:"container_id"`
	ContainerTypeID *int64     `json:"container_type_id"`
	ClientID        *string    `json:"client_id"`
	WarehouseID     *string    `json:"warehouse_id"`
	AreaID          *string    `json:"area_id"`
	LocationID      *string    `json:"location_id"`
	AreaName        *string    `json:"area_name"`
	ClientName      *string    `json:"client_name"`
	LPCode          *string    `json:"lp_code"`
	LPType          *string    `json:"lp_type"`
	ContainerQR     *string    `json:"container_qr"`
	Properties      []string   `json:"properties"`
}

// # Generate LP Label: Container Details Model
type ContainerDetails struct {
	ContainerID    *uuid.UUID `json:"container_id"`
	ItemID         *string    `json:"item_id"`
	LocationID     *string    `json:"location_id"`
	WarehouseID    *string    `json:"warehouse_id"`
	ClientID       *string    `json:"client_id"`
	ClientName     *string    `json:"client_name"`
	ItemName       *string    `json:"item_name"`
	LPCode         *string    `json:"lp_code"`
	LPType         *string    `json:"lp_type"`
	Quantity       *int32     `json:"quantity"`
	AvailableQty   *int32     `json:"available_qty"`
	SKU            *string    `json:"sku"`
	Scannable      *string    `json:"scannable"`
	BatchNumber    *string    `json:"batch_number"`
	SerialNumber   *string    `json:"serial_number"`
	ExpirationDate *time.Time `json:"expiration_date"`
	Unit           *string    `json:"unit"`
	Description    *string    `json:"description"`
}
