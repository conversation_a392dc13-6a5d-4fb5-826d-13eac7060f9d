package model

import (
	"time"

	"inventory/util"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// User represents user in mongodb
type User struct {
	ID                  *primitive.ObjectID  `json:"_id,omitempty" bson:"_id,omitempty"`
	Gender              string               `json:"gender,omitempty" bson:"gender,omitempty"`
	Sessions            []UserSessionDetails `json:"sessions,omitempty" bson:"sessions,omitempty"`
	Password            string               `json:"password,omitempty" bson:"password,omitempty"`
	PasswordResetCode   string               `json:"password_reset_code,omitempty" bson:"password_reset_code,omitempty"`
	ResetCodeExpiration *time.Time           `json:"reset_code_expiration,omitempty" bson:"reset_code_expiration,omitempty"`
	ConfirmationCode    string               `json:"confirmation_code,omitempty" bson:"confirmation_code,omitempty"`
	Email               string               `json:"email,omitempty" bson:"email,omitempty"`
	Username            string               `json:"username,omitempty" bson:"username,omitempty"`
	FullName            string               `json:"full_name,omitempty" bson:"full_name,omitempty"`
	OrganizationID      *primitive.ObjectID  `json:"organization_id,omitempty" bson:"organization_id,omitempty"`
	EmailVerifiedAt     *time.Time           `json:"email_verified_at,omitempty" bson:"email_verified_at,omitempty"`
	CreatedAt           *time.Time           `json:"created_at,omitempty" bson:"created_at,omitempty"`
	CreatedBy           *UserModel           `json:"created_by,omitempty" bson:"created_by,omitempty"`
	UpdatedAt           *time.Time           `json:"updated_at,omitempty" bson:"updated_at,omitempty"`
	Services            []Srevices           `json:"services,omitempty" bson:"services,omitempty"`
	IsDeactivated       bool                 `json:"is_deactivated,omitempty" bson:"is_deactivated,omitempty"`
	DeactivatedAt       *time.Time           `json:"deactivated_at,omitempty" bson:"deactivated_at,omitempty"`
	DeactivatedBy       string               `json:"deactivated_by,omitempty" bson:"deactivated_by,omitempty"`
	DeactivationReason  string               `json:"deactivation_reason,omitempty" bson:"deactivation_reason,omitempty"`
	IsOwner             bool                 `json:"is_owner,omitempty" bson:"is_owner,omitempty"`
}

// CreatedBy contains info about user who is creating something
type UserModel struct {
	UserID   primitive.ObjectID `json:"user_id,omitempty" bson:"user_id,omitempty"`
	Username string             `json:"username,omitempty" bson:"username,omitempty"`
	Name     string             `json:"name,omitempty" bson:"name,omitempty"`
	Email    string             `json:"email,omitempty" bson:"email,omitempty"`
}

// SetPassword sets hashed password string
func (u *User) SetPassword(p string) {
	u.Password, _ = util.HashPassword(p)
}

// CheckPassword checks hashed password with provided string
func (u *User) CheckPassword(password string) bool {
	isValid := util.CheckPasswordHash(password, u.Password)
	return isValid
}

// SetConfirmationCode generates a unique confirmation code
func (u *User) SetConfirmationCode(token string) {
	u.ConfirmationCode = token
}

// GetConfirmationCode returns a unique confirmation code
func (u *User) GetConfirmationCode() string {
	return u.ConfirmationCode
}

// GetConfirmationURL returns a email confirmation url
func (u *User) GetConfirmationURL() string {
	url := "/confirm-user?code=" + u.GetConfirmationCode()
	return url
}

// SetPasswordResetCode sets password_reset_code
func (u *User) SetPasswordResetCode(token string) {
	u.PasswordResetCode = token
}

// GetPasswordResetCode sets password_reset_code
func (u *User) GetPasswordResetCode() string {
	return u.PasswordResetCode
}

// GetPasswordResetURL returns a email confirmation url
func (u *User) GetPasswordResetURL() string {
	url := "/password-reset?code=" + u.PasswordResetCode
	return url
}

func RemoveIndex(s []string, index int) []string {
	return append(s[:index], s[index+1:]...)
}

// UserOrganization contans reference to Organization
type UserOrganization struct {
	OrganizationID primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Role           string             `json:"role,omitempty" bson:"role,omitempty"`
	AddedAt        *time.Time         `json:"added_at,omitempty" bson:"added_at,omitempty"`
}

// UserWarehouse contans reference to Team
type UserWarehouse struct {
	WarehouseID primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	Alias       string             `json:"alias,omitempty" bson:"alias,omitempty"`
	Role        string             `json:"role,omitempty" bson:"role,omitempty"`
	AddedAt     *time.Time         `json:"added_at,omitempty" bson:"added_at,omitempty"`
}

// UserSessionDetails contains details about User Session
type UserSessionDetails struct {
	SessionID string     `json:"session_id,omitempty" bson:"session_id,omitempty"`
	UserAgent string     `json:"user_agent,omitempty" bson:"user_agent,omitempty"`
	Platform  string     `json:"platform,omitempty" bson:"platform,omitempty"`
	Token     string     `json:"token,omitempty" bson:"token,omitempty"`
	CreatedAt *time.Time `json:"created_at,omitempty" bson:"created_at,omitempty"`
}

// COnfiguredAccessProfile contains individual access permissions given/taken from user
type ConfiguredAccessProfile struct {
	AddedAccess   []string `json:"added_access,omitempty" bson:"added_access,omitempty"`
	RemovedAccess []string `json:"removed_access,omitempty" bson:"removed_access,omitempty"`
}

// ClientData contains client info of user
type ClientData struct {
	ClientID   primitive.ObjectID `json:"client_id,omitempty" bson:"client_id,omitempty"`
	ClientName string             `json:"client_name,omitempty" bson:"client_name,omitempty"`
}

// Srevices contains services and Access info of users
type Srevices struct {
	IsOwner                 bool                     `json:"is_owner,omitempty" bson:"is_owner,omitempty"`
	IsPrimary               bool                     `json:"is_primary,omitempty" bson:"is_primary,omitempty"`
	ServiceName             string                   `json:"service_name,omitempty" bson:"service_name,omitempty"`
	AccessProfile           string                   `json:"access_profile,omitempty" bson:"access_profile,omitempty"`
	ConfiguredAccessProfile *ConfiguredAccessProfile `json:"configured_access_profile,omitempty" bson:"configured_access_profile,omitempty"`
	OrganizationID          *primitive.ObjectID      `json:"organization_id,omitempty" bson:"organization_id,omitempty"`
	WarehouseID             *primitive.ObjectID      `json:"warehouse_id,omitempty" bson:"warehouse_id,omitempty"`
	WarehouseName           string                   `json:"warehouse_name,omitempty" bson:"warehouse_name,omitempty"`
	ClientID                *primitive.ObjectID      `json:"client_id,omitempty" bson:"client_id,omitempty"`
	ClientName              string                   `json:"client_name,omitempty" bson:"client_name,omitempty"`
	ClientAccess            []primitive.ObjectID     `json:"client_access,omitempty" bson:"client_access,omitempty"`
}

// AccessControl contains warehouse access permissions info
type AccessControl struct {
	ID          *primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	ServiceName string              `json:"service_name,omitempty" bson:"service_name,omitempty"`
	WarehouseID primitive.ObjectID  `json:"warehouse_id,omitempty" bson:"warehouse_id,omitempty"`
	ProfileName string              `json:"profile_name,omitempty" bson:"profile_name,omitempty"`
	Access      []string            `json:"access_profiles,omitempty" bson:"access_profiles,omitempty"`
}
