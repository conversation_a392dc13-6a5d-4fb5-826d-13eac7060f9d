package model

import (
	"time"

	uuid "github.com/satori/go.uuid"
)

type Transaction struct {
	ID                int64                   `json:"id"`
	ClientID          string                  `json:"client_id"`
	WarehouseID       string                  `json:"warehouse_id"`
	RequestID         string                  `json:"request_id"`
	ItemID            string                  `json:"item_id"`
	SKU               string                  `json:"sku"`
	Action            string                  `json:"action"`
	Change            int                     `json:"change"`
	Current           int64                   `json:"current"`
	ChangedByUsername string                  `json:"changed_by_username"`
	ChangedByID       string                  `json:"changed_by_id"`
	ChangedAt         time.Time               `json:"changed_at"`
	ChangedBy         *ChangedBy              `json:"changed_by"`
	BatchNumber       *string                 `json:"batch_number"`
	SerialNumber      *string                 `json:"serial_number"`
	ExpirationDate    *time.Time              `json:"expiration_date"`
	Source            *string                 `json:"source"`
	Document          *string                 `json:"document"`
	DocumentCode      *string                 `json:"document_code"`
	ClientName        *string                 `json:"client_name"`
	DocumentID        *string                 `json:"document_id"`
	Remark            *map[string]interface{} `json:"remark"`
	DocumentPrn       *string                 `json:"document_prn"`
	LocationID        *string                 `json:"location_id"`
	LocationCode      *string                 `json:"location_code"`
	ContainerID       *string                 `json:"container_id"`
	ContainerCode     *string                 `json:"container_code"`
	ContainerType     *string                 `json:"container_type"`
	CartID            *uuid.UUID              `json:"cart_id"`
	CartCode          *string                 `json:"cart_code"`
	ToteID            *uuid.UUID              `json:"tote_id"`
	ToteCode          *string                 `json:"tote_code"`
	CurrentTrackedQty *int64                  `json:"current_tracked_qty"`
	Unit              *string                 `json:"unit"`
}

type WarehouseOperationReport struct {
	ID                int64                   `json:"id"`
	ClientID          string                  `json:"client_id"`
	WarehouseID       string                  `json:"warehouse_id"`
	RequestID         string                  `json:"request_id"`
	ItemID            string                  `json:"item_id"`
	SKU               string                  `json:"sku"`
	Action            string                  `json:"action"`
	Change            int                     `json:"change"`
	Current           int                     `json:"current"`
	ChangedByUsername string                  `json:"changed_by_username"`
	ChangedByID       string                  `json:"changed_by_id"`
	ChangedAt         time.Time               `json:"changed_at"`
	BatchNumber       *string                 `json:"batch_number"`
	SerialNumber      *string                 `json:"serial_number"`
	ExpirationDate    *time.Time              `json:"expiration_date"`
	Source            *string                 `json:"source"`
	Document          *string                 `json:"document"`
	DocumentCode      *string                 `json:"document_code"`
	ClientName        *string                 `json:"client_name"`
	DocumentID        *string                 `json:"document_id"`
	Remark            *map[string]interface{} `json:"remark"`
	DocumentPrn       *string                 `json:"document_prn"`
	LocationID        *string                 `json:"location_id"`
	LocationCode      *string                 `json:"location_code"`
	ContainerID       *string                 `json:"container_id"`
	ContainerCode     *string                 `json:"container_code"`
	ContainerType     *string                 `json:"container_type"`
	CartID            *uuid.UUID              `json:"cart_id"`
	CartCode          *string                 `json:"cart_code"`
	ToteID            *uuid.UUID              `json:"tote_id"`
	ToteCode          *string                 `json:"tote_code"`
	CurrentTrackedQty *int64                  `json:"current_tracked_qty"`
	BaseUnit          string                  `json:"base_unit"`
}

// FSNReport contains schema for FSN Report data
type FSNReport struct {
	ClientName            string  `json:"client_name,omitempty"`
	ItemID                string  `json:"item_id,omitempty"`
	SKU                   string  `json:"sku,omitempty"`
	OpeningBalance        int     `json:"opening_balance,omitempty"`
	ClosingBalance        int     `json:"closing_balance,omitempty"`
	Received              int     `json:"received,omitempty"`
	Issued                int     `json:"issued,omitempty"`
	Duration              int     `json:"duration,omitempty"`
	AverageStay           float64 `json:"avg_stay,omitempty"`
	ConsumptionRate       float64 `json:"consumption_rate,omitempty"`
	CumulativeAverageStay float64 `json:"cumulative_avg_stay,omitempty"`
	PercentAverageStay    float64 `json:"percent_avg_stay,omitempty"`
}

type InventoryClosing struct {
	ClientID   string    `json:"client_id,omitempty"`
	ClientName string    `json:"client_name,omitempty"`
	ItemID     string    `json:"item_id,omitempty"`
	SKU        string    `json:"sku,omitempty"`
	Name       string    `json:"name,omitempty"`
	ChangedAt  time.Time `json:"changed_at,omitempty"`
	Quantity   int64     `json:"quantity,omitempty"`
	Unit       string    `json:"unit,omitempty"`
}

type ChangedBy struct {
	ID       *string `json:"id"`
	Username *string `json:"username"`
}
