package model

import (
	"inventory/constants"
	"time"
)

type Inventory struct {
	ID                  int64              `json:"_id"`
	ItemID              string             `json:"item_id"`
	ClientID            string             `json:"client_id"`
	WarehouseID         string             `json:"warehouse_id"`
	Quantity            int32              `json:"quantity"`
	AvailableQty        int32              `json:"available_qty"`
	PickableQty         int32              `json:"pickable_qty"`
	BaseUnit            string             `json:"base_unit"`
	SKU                 string             `json:"sku"`
	Scannable           string             `json:"scannable"`
	CreatedAt           *time.Time         `json:"created_at"`
	UpdatedAt           *time.Time         `json:"updated_at"`
	IsDeleted           bool               `json:"is_deleted"`
	OnHold              bool               `json:"on_hold"`
	IsBatchControlled   bool               `json:"is_batch_controlled"`
	IsTrackedBySerialNo bool               `json:"is_tracked_by_serial_no"`
	Name                string             `json:"name"`
	Description         string             `json:"description"`
	OnHoldBy            *string            `json:"on_hold_by"`
	IsPerishable        bool               `json:"is_perishable"`
	TrackedBy           *string            `json:"tracked_by"`
	Image               *string            `json:"image"`
	BackOrders          bool               `json:"back_orders"`
	ClientName          *string            `json:"client_name"`
	IsClientDeleted     bool               `json:"is_client_deleted"`
	IsKit               bool               `json:"is_kit"`
	DynamicColumn       *map[string]string `json:"dynamic_column"`
	WarehouseName       string             `json:"warehouse_name"`
	InboundQty          int32              `json:"inbound_qty"`
	TotalLPCount        int32              `json:"total_lp_count"`
	LPCountInfo         []LPCountInfo      `json:"lp_count_info"`

	// # LeanShip #
	IsLeanShip bool `json:"is_leanship"`
}

type InventoryResponse struct {
	TotalDocuments int64                    `json:"totalDocuments"`
	TotalPages     int64                    `json:"totalPages"`
	CurrentPage    int64                    `json:"currentPage"`
	Data           []*Inventory             `json:"data"`
	SearchParams   []*constants.SearchParam `json:"search_params"`
}

type LPCountInfo struct {
	LPType  string `json:"lp_type"`
	LPCount int32  `json:"lp_count"`
}

type InventorySearch struct {
	ItemID       string `json:"item_id"`
	Quantity     int32  `json:"quantity"`
	AvailableQty int32  `json:"available_qty"`
	PickableQty  int32  `json:"pickable_qty"`
	SKU          string `json:"sku"`
	Scannable    string `json:"scannable"`
	OnHold       bool   `json:"on_hold"`
	Name         string `json:"name"`
	Description  string `json:"description"`
	ClientName   string `json:"client_name"`
	ClientID     string `json:"client_id"`
	BaseUnit     string `json:"base_unit"`
	IsKit        bool   `json:"is_kit"`
	InboundQty   int32  `json:"inbound_qty"`
}

type ESInventory struct {
	ClientID            string `json:"client_id"`
	WarehouseID         string `json:"warehouse_id"`
	BaseUnit            string `json:"base_unit"`
	SKU                 string `json:"sku"`
	Scannable           string `json:"scannable"`
	IsBatchControlled   bool   `json:"is_batch_controlled"`
	IsTrackedBySerialNo bool   `json:"is_tracked_by_serial_no"`
	Name                string `json:"name"`
	Description         string `json:"description"`
	IsPerishable        bool   `json:"is_perishable"`
	ClientName          string `json:"client_name"`

	// # LeanShip #
	IsLeanShip bool `json:"is_leanship"`
}

type TrackedInventory struct {
	ID             int64      `json:"_id"`
	WarehouseID    string     `json:"warehouse_id"`
	ClientID       string     `json:"client_id"`
	ItemID         string     `json:"item_id"`
	BatchNumber    *string    `json:"batch_number"`
	SerialNumber   *string    `json:"serial_number"`
	ExpirationDate *time.Time `json:"expiration_date"`
	Quantity       int        `json:"quantity"`
	AvailableQty   int        `json:"available_qty"`
	PickableQty    int        `json:"pickable_qty"`
	SKU            string     `json:"sku"`
	Name           string     `json:"name"`
	Description    string     `json:"description"`
	BaseUnit       string     `json:"base_unit"`
	ClientName     *string    `json:"client_name"`
	TotalQuantity  int        `json:"total_quantity"`
}

type ExpirationObject struct {
	SerialNumber   string
	BatchNumber    string
	ExpirationDate *time.Time
	Quantity       int
	Unit           string
	AvailableQty   int
	PickableQty    int
}

type SerialObject struct {
	SerialNumber    string
	BatchNumber     string
	Quantity        int
	Unit            string
	AvailableQty    int
	PickableQty     int
	ExpirationDates []*ExpirationObject
}

type BatchObject struct {
	BatchNumber     string
	Quantity        int
	Unit            string
	AvailableQty    int
	PickableQty     int
	SerialNumbers   []*SerialObject
	ExpirationDates []*ExpirationObject
}

type TrackedObject struct {
	BatchNumbers    []*BatchObject
	SerialNumbers   []*SerialObject
	ExpirationDates []*ExpirationObject
}

type ConsolidatedInventory struct {
	ClientName   string  `json:"client_name"`
	Quantity     int32   `json:"quantity"`
	AvailableQty int32   `json:"available_qty"`
	PickableQty  int32   `json:"pickable_qty"`
	BaseUnit     string  `json:"base_unit"`
	SKU          string  `json:"sku"`
	Name         string  `json:"name"`
	Description  string  `json:"description"`
	Image        *string `json:"image"`
}

type ConsolidatedInventoryResponse struct {
	ConsolidatedInventory []ConsolidatedInventory `json:"consolidated_inventory"`
	TotalDocuments        int                     `json:"total_documents"`
	TotalPages            int                     `json:"total_pages"`
	CurrentPage           int                     `json:"current_page"`
}

type LocationData struct {
	ItemID          string           `json:"item_id"`
	ItemTrackedInfo *ItemTrackedInfo `json:"item_tracked_info"`
	TotalQty        int              `json:"total_qty"`
	AvailableQty    int              `json:"available_qty"`
	Locations       []LocationForWO  `json:"locations"`
}

type LocationForWO struct {
	LocationID        string  `json:"location_id"`
	AreaID            string  `json:"area_id"`
	Code              string  `json:"code"`
	AreaName          *string `json:"area_name"`
	Quantity          int     `json:"quantity"`
	AvailableQuantity int     `json:"available_quantity"`
	LocationOnHold    bool    `json:"location_on_hold"`
}

type ItemTrackedInfo struct {
	Batch      *string
	Serial     *string
	Expiration *time.Time
}

// # Nova - Item V1 Model
type ItemV1 struct {
	ItemID         string
	SKU            string
	BatchNumber    string
	SerialNumber   string
	ExpirationDate *time.Time
	Quantity       int32
	AvailableQty   int32
	PickableQty    int32
}

// # Location Layout #

// # Item Object Model
type ItemObject struct {
	ItemID       *string       `json:"item_id"`
	ClientID     *string       `json:"client_id"`
	WarehouseID  *string       `json:"warehouse_id"`
	ItemName     *string       `json:"item_name"`
	ClientName   *string       `json:"client_name"`
	Description  *string       `json:"description"`
	Image        *string       `json:"image"`
	QuantityInfo *QuantityInfo `json:"quantity_info"`
	SKU          *string       `json:"sku"`
	Scannable    *string       `json:"scannable"`
	BaseUnit     *string       `json:"base_unit"`
	OnHold       *bool         `json:"on_hold"`
}

// # Quantity Info Model
type QuantityInfo struct {
	TotalQty     *int32 `json:"total_qty"`
	AvailableQty *int32 `json:"available_qty"`
	PickableQty  *int32 `json:"pickable_qty,omitempty"` // # Optional Field
}

// # Item Response Model
type ItemResponse struct {
	Items []*ItemObject `json:"items"`
}
