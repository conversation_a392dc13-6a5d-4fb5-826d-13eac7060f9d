package model

import (
	"time"

	uuid "github.com/satori/go.uuid"
)

type Remark struct {
	Key   string
	Value any
}

type TransactionData struct {
	ClientID       string
	WarehouseID    string
	RequestID      string
	ItemID         string
	SKU            string
	Action         string
	Change         int
	Username       string
	UserID         string
	ChangedAt      time.Time
	BatchNumber    *string
	SerialNumber   *string
	ExpirationDate *time.Time
	Source         string
	Document       string
	DocumentCode   string
	ClientName     string
	DocumentID     string
	DocumentPrn    string
	Remarks        []Remark
	LocationID     string
	LocationCode   string
	ContainerID    *uuid.UUID
	ContainerCode  *string
	ContainerType  string
	CartID         *uuid.UUID `json:"cart_id"`
	CartCode       *string    `json:"cart_code"`
	ToteID         *uuid.UUID `json:"tote_id"`
	ToteCode       *string    `json:"tote_code"`

	// only for wave picking
	QtyToAddInCurrentQty *int32
}
