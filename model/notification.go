package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type Notification struct {
	Action        string             `json:"action" bson:"action,omitempty"`
	Identifier    string             `json:"identifier,omitempty" bson:"identifier,omitempty"`
	WarehouseID   primitive.ObjectID `json:"warehouse_id,omitempty" bson:"warehouse_id,omitempty"`
	Title         string             `json:"title,omitempty" bson:"title,omitempty"`
	Message       string             `json:"message,omitempty" bson:"message,omitempty"`
	Subscribers   []string           `json:"subscribers,omitempty" bson:"subscribers,omitempty"`
	Subscriber    string             `json:"subscriber,omitempty" bson:"subscriber,omitempty"`
	Image         string             `json:"image,omitempty" bson:"image,omitempty"`
	PriorityLevel string             `json:"priority_level,omitempty" bson:"priority_level,omitempty"`
}
