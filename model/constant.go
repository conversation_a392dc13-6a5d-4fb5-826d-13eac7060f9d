package model

// HeaderRequestID sets RequestID in request header
const HeaderRequestID string = "X-Request-ID"

// Authentication sets Authentication in request header
const Authentication string = "Authentication"

// Mongodb collections
const (
	ContainerWiseInventoryTable = "container_wise_inventory"
)

// Triggere event for order allocation
const (
	AutditInventoryTrigger = "audit_inventory"
	ARNReceivedTrigger     = "arn_received"
)

// OrderDeAllocationItems is used to store order allocation items
type OrderDeAllocationItems struct {
	ItemID   string `json:"item_id"`
	Quantity int32  `json:"quantity"`
}

type ItemsInvenotry struct {
	ItemID       string `json:"item_id"`
	ClientID     string `json:"client_id"`
	AvailableQty int32  `json:"available_qty"`
}
