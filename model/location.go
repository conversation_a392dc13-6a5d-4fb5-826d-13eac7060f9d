package model

import (
	"time"

	uuid "github.com/satori/go.uuid"
)

type Location struct {
	ID                *int64
	ItemID            *string
	WarehouseID       *string
	ClientID          *string
	LocationID        *string
	Code              *string
	Quantity          *int32
	AvailableQty      *int32
	AreaName          *string
	AreaID            *string
	OnHold            *bool
	OnHoldByID        *string
	LastUpdatedAt     *time.Time
	LastUpdatedByID   *string
	LastUpdatedByName *string
	SKU               *string
	Scannable         *string
	BatchNumber       *string
	SerialNumber      *string
	ExpirationDate    *time.Time
	CreatedAt         *time.Time
	ClientName        *string
	IsPrime           *bool
}

type LocationWiseInventory struct {
	ID                *int64     `json:"id"`
	ItemID            *string    `json:"item_id"`
	WarehouseID       *string    `json:"warehouse_id"`
	ClientID          *string    `json:"client_id"`
	LocationID        *string    `json:"location_id"`
	Code              *string    `json:"code"`
	Quantity          *int32     `json:"quantity"`
	AvailableQty      *int32     `json:"available_qty"`
	AreaName          *string    `json:"area_name"`
	AreaID            *string    `json:"area_id"`
	OnHold            *bool      `json:"on_hold"`
	OnHoldByID        *string    `json:"on_hold_by_id"`
	LastUpdatedAt     *time.Time `json:"last_updated_at"`
	LastUpdatedByID   *string    `json:"last_updated_by_id"`
	LastUpdatedByName *string    `json:"last_updated_by_name"`
	SKU               *string    `json:"sku"`
	Scannable         *string    `json:"scannable"`
	BatchNumber       *string    `json:"batch_number"`
	SerialNumber      *string    `json:"serial_number"`
	ExpirationDate    *time.Time `json:"expiration_date"`
	CreatedAt         *time.Time `json:"created_at"`
	ClientName        *string    `json:"client_name"`
	Name              *string    `json:"item_name"`
	BaseUnit          *string    `json:"base_unit"`
	IsPrime           *bool      `json:"is_prime"`
	ItemOnHold        *bool      `json:"item_on_hold"`
	IsKit             *bool      `json:"is_kit"`
	Image             *string    `json:"image"`
}

type ESLocation struct {
	Code           string `json:"code"`
	SearchableCode string `json:"searchable_code"`
	LocationID     string `json:"location_id"`
	WarehouseID    string `json:"warehouse_id"`
	AreaName       string `json:"area_name"`
	AreaID         string `json:"area_id"`
	IsPrime        bool   `json:"is_prime"`
}

type DeleteLocationsData struct {
	LocationID string  `json:"location_id"`
	ItemID     *string `json:"item_id"`
	Code       string  `json:"code"`
}

type SearchResponseSource struct {
	Code        string `json:"code,omitempty"`
	WarehouseID string `json:"warehouse_id,omitempty"`

	ContainerID     string `json:"container_id,omitempty"`
	ContainerType   string `json:"container_type,omitempty"`
	ContainerTypeID int    `json:"container_type_id,omitempty"`

	AreaID     string `json:"area_id,omitempty"`
	AreaName   string `json:"area_name,omitempty"`
	LocationID string `json:"location_id,omitempty"`
}

type SearchResponse struct {
	ID     string               `json:"_id"`
	Index  string               `json:"_index"`
	Score  int                  `json:"_score"`
	Source SearchResponseSource `json:"_source"`
}

// # Location Layout #

// # Location Object Model
type LocationObject struct {
	LocationID     *string       `json:"location_id"`
	ClientID       *string       `json:"client_id"`
	WarehouseID    *string       `json:"warehouse_id"`
	AreaID         *string       `json:"area_id"`
	AreaName       *string       `json:"area_name"`
	ClientName     *string       `json:"client_name"`
	LocationCode   *string       `json:"location_code"`
	QuantityInfo   *QuantityInfo `json:"quantity_info"`
	SKU            *string       `json:"sku"`
	Scannable      *string       `json:"scannable"`
	BatchNumber    *string       `json:"batch_number"`
	SerialNumber   *string       `json:"serial_number"`
	ExpirationDate *time.Time    `json:"expiration_date"`
	OnHold         *bool         `json:"on_hold"`
	IsPrime        *bool         `json:"is_prime"`
}

// # Location Response Model
type LocationResponse struct {
	Locations []*LocationObject `json:"locations"`
}

// # Location Details Layout Model
type LocationDetailsLayout struct {
	LocationID   *string  `json:"location_id"`
	ClientID     *string  `json:"client_id"`
	WarehouseID  *string  `json:"warehouse_id"`
	AreaID       *string  `json:"area_id"`
	AreaName     *string  `json:"area_name"`
	ClientName   *string  `json:"client_name"`
	LocationCode *string  `json:"location_code"`
	LocationQR   *string  `json:"location_qr"`
	Properties   []string `json:"properties"`
}

type LocationLayoutSearchData struct {
	WarehouseID     *string    `json:"warehouse_id"`
	ContainerID     *uuid.UUID `json:"container_id"`
	ContainerTypeID *int64     `json:"container_type_id"`
	ContainerCode   *string    `json:"container_code"`
	ClientID        *string    `json:"client_id"`
	ClientName      *string    `json:"client_name"`
	LocationID      *string    `json:"location_id"`
	LocationCode    *string    `json:"location_code"`
	AreaName        *string    `json:"area_name"`
	AreaID          *string    `json:"area_id"`
	OnHold          *bool      `json:"on_hold"`
	IsPrime         *bool      `json:"is_prime"`
	ItemID          *string    `json:"item_id"`
	Image           *string    `json:"image"`
	IsKit           *bool      `json:"is_kit"`
	SKU             *string    `json:"sku"`
	Name            *string    `json:"name"`
	Scannable       *string    `json:"scannable"`
	Quantity        *int32     `json:"quantity"`
	AvailableQty    *int32     `json:"available_qty"`
	BatchNumber     *string    `json:"batch_number"`
	SerialNumber    *string    `json:"serial_number"`
	ExpirationDate  *time.Time `json:"expiration_date"`
	BaseUnit        *string    `json:"base_unit"`
	ItemOnHold      *bool      `json:"item_on_hold"`
}

type LocationLayoutSearchResponse struct {
	WarehouseID    *string                             `json:"warehouse_id,omitempty"`
	ClientID       *string                             `json:"client_id,omitempty"`
	ClientName     *string                             `json:"client_name,omitempty"`
	LocationID     *string                             `json:"location_id,omitempty"`
	LocationCode   *string                             `json:"location_code,omitempty"`
	AreaName       *string                             `json:"area_name,omitempty"`
	AreaID         *string                             `json:"area_id,omitempty"`
	Properties     []string                            `json:"properties,omitempty"`
	OnHold         *bool                               `json:"on_hold"`
	IsPrime        *bool                               `json:"is_prime"`
	ContainerID    *uuid.UUID                          `json:"container_id,omitempty"`
	ContainerType  *string                             `json:"container_type,omitempty"`
	ContainerCode  *string                             `json:"container_code,omitempty"`
	ContainerRefNo *string                             `json:"container_ref_no,omitempty"`
	Items          []LocationLayoutSearchResponseItems `json:"items,omitempty"`
}

type LocationLayoutSearchResponseItems struct {
	ClientID       *string    `json:"client_id,omitempty"`
	ClientName     *string    `json:"client_name,omitempty"`
	ContainerID    *uuid.UUID `json:"container_id,omitempty"`
	ContainerType  *string    `json:"container_type,omitempty"`
	ContainerCode  *string    `json:"container_code,omitempty"`
	ContainerRefNo *string    `json:"container_ref_no,omitempty"`
	ItemID         *string    `json:"item_id,omitempty"`
	Image          *string    `json:"image,omitempty"`
	SKU            *string    `json:"sku,omitempty"`
	Name           *string    `json:"name,omitempty"`
	Scannable      *string    `json:"scannable,omitempty"`
	Quantity       *int32     `json:"quantity,omitempty"`
	AvailableQty   *int32     `json:"available_qty,omitempty"`
	BatchNumber    *string    `json:"batch_number,omitempty"`
	SerialNumber   *string    `json:"serial_number,omitempty"`
	ExpirationDate *time.Time `json:"expiration_date,omitempty"`
	IsKit          *bool      `json:"is_kit"`
	BaseUnit       *string    `json:"base_unit,omitempty"`
	OnHold         *bool      `json:"on_hold"`
}

// Define a set type using a map
type StringSet map[string]struct{}

// Function to add an element to the set
func (s StringSet) Add(value string) {
	s[value] = struct{}{}
}

// Function to remove an element from the set
func (s StringSet) Remove(value string) {
	delete(s, value)
}

// Function to check if an element is in the set
func (s StringSet) Contains(value string) bool {
	_, exists := s[value]
	return exists
}

// func to convert set to slice
func (s StringSet) ToSlice() []string {
	var slice []string
	for key := range s {
		slice = append(slice, key)
	}
	return slice
}
