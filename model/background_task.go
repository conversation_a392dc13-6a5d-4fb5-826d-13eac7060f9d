package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BackgroundTask struct {
	ID          *primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	RequestID   string              `json:"request_id,omitempty" bson:"request_id,omitempty"`
	WarehouseID *primitive.ObjectID `json:"warehouse_id,omitempty" bson:"warehouse_id,omitempty"`
	Action      string              `json:"action,omitempty" bson:"action,omitempty"`
	Title       string              `json:"title,omitempty" bson:"title,omitempty"`
	Status      string              `json:"status,omitempty" bson:"status,omitempty"`
	Parameters  *TaskParameters     `json:"parameters,omitempty" bson:"parameters,omitempty"`
	ProcessLog  *TaskProcessLog     `json:"process_log,omitempty" bson:"process_log,omitempty"`
	CreatedAt   *time.Time          `json:"created_at,omitempty" bson:"created_at,omitempty"`
	UpdatedAt   *time.Time          `json:"updated_at,omitempty" bson:"updated_at,omitempty"`
	CreatedBy   *UserModel          `json:"created_by,omitempty" bson:"created_by,omitempty"`
	UpdatedBy   *UserModel          `json:"updated_by,omitempty" bson:"updated_by,omitempty"`
	CompletedAt *time.Time          `json:"completed_at,omitempty" bson:"completed_at,omitempty"`
	CompletedBy *UserModel          `json:"completed_by,omitempty" bson:"completed_by,omitempty"`

	Timeline []TaskTimeline `json:"timeline,omitempty" bson:"timeline,omitempty"`
}

type TaskParameters struct {
	ClientID     *primitive.ObjectID `json:"client_id,omitempty" bson:"client_id,omitempty"`
	ClientName   string              `json:"client_name,omitempty" bson:"client_name,omitempty"`
	DocumentInfo *DocumentInfo       `json:"document_info,omitempty" bson:"document_info,omitempty"`
}

type DocumentInfo struct {
	DocumentID string `json:"document_id,omitempty" bson:"document_id,omitempty"`
	Type       string `json:"type,omitempty" bson:"type,omitempty"`
	Code       string `json:"code,omitempty" bson:"code,omitempty"`
}

type TaskProcessLog struct {
	ElementType       string      `json:"element_type,omitempty" bson:"element_type,omitempty"`
	ElementsToProcess *int32      `json:"elements_to_process,omitempty" bson:"elements_to_process,omitempty"`
	ProcessedElements *int32      `json:"processed_elements,omitempty" bson:"processed_elements,omitempty"`
	FailedElements    *int32      `json:"failed_elements,omitempty" bson:"failed_elements,omitempty"`
	ElementsInfo      interface{} `json:"elements_info,omitempty" bson:"elements_info,omitempty"`
	ContentUrl        string      `json:"content_url,omitempty" bson:"content_url,omitempty"`
	ContentType       string      `json:"content_type,omitempty" bson:"content_type,omitempty"`
}

type TaskTimeline struct {
	ID        *primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	TaskID    *primitive.ObjectID `json:"task_id,omitempty" bson:"task_id,omitempty"`
	Status    string              `json:"status,omitempty" bson:"status,omitempty"`
	Success   *bool               `json:"success,omitempty" bson:"success,omitempty"`
	Message   string              `json:"message,omitempty" bson:"message,omitempty"`
	Error     string              `json:"error,omitempty" bson:"error,omitempty"`
	Context   *SubTaskContext     `json:"context,omitempty" bson:"context,omitempty"`
	CreatedAt *time.Time          `json:"created_at,omitempty" bson:"created_at,omitempty"`
}

type SubTaskContext struct {
	DocumentInfo      *DocumentInfo `json:"document_info,omitempty" bson:"document_info,omitempty"`
	TaskName          string        `json:"task_name,omitempty" bson:"task_name,omitempty"`
	ServiceRequesting string        `json:"service_requesting,omitempty" bson:"service_requesting,omitempty"`
}

type LocationUploadElementsInfo struct {
	LocationCode string `json:"location_code,omitempty" bson:"location_code,omitempty"`
	Success      bool   `json:"success" bson:"success"`
	Message      string `json:"message,omitempty" bson:"message,omitempty"`
}

// # Delete Area Elements Info
type DeleteAreaElementsInfo struct {
	AreaID        string `json:"area_id,omitempty" bson:"area_id,omitempty"`
	WarehouseID   string `json:"warehouse_id,omitempty" bson:"warehouse_id,omitempty"`
	WarehouseName string `json:"warehouse_name,omitempty" bson:"warehouse_name,omitempty"`
	Success       bool   `json:"success" bson:"success"`
	Status        string `json:"status,omitempty" bson:"status,omitempty"`
	Message       string `json:"message,omitempty" bson:"message,omitempty"`
}
