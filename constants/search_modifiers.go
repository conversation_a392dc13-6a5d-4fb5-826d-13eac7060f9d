package constants

// # Search Param Structure #
type SearchParam struct {
	Level       string `json:"level"`
	Key         string `json:"key"`
	RequestKey  string `json:"request_key"`
	Description string `json:"description"`
}

// # Inventory #
var InventorySearchParams = []*SearchParam{
	// # '4' Search Modifiers For 'Inventory'
	{
		Level:       "top",
		Key:         "/name",
		RequestKey:  "name",
		Description: "Search by name of an item",
	},
	{
		Level:       "top",
		Key:         "/sku",
		RequestKey:  "sku",
		Description: "Search by SKU of an item",
	},
	{
		Level:       "top",
		Key:         "/scannable",
		RequestKey:  "scannable",
		Description: "Search by scannable of an item",
	},
	{
		Level:       "top",
		Key:         "/description",
		RequestKey:  "description",
		Description: "Search by description of an item",
	},
}

// # Item Transaction #
var ItemTransactionSearchParams = []*SearchParam{
	// # '10' Search Modifiers For 'Item Transaction'
	{
		Level:       "top",
		Key:         "/user name",
		RequestKey:  "changed_by_username",
		Description: "Search by user name of an item transaction",
	},
	{
		Level:       "top",
		Key:         "/txn type",
		RequestKey:  "action",
		Description: "Search by transaction type of an item",
	},
	{
		Level:       "top",
		Key:         "/lot number",
		RequestKey:  "batch_number",
		Description: "Search by lot number of an item",
	},
	{
		Level:       "top",
		Key:         "/serial number",
		RequestKey:  "serial_number",
		Description: "Search by serial number of an item",
	},
	{
		Level:       "top",
		Key:         "/expiry date",
		RequestKey:  "expiration_date",
		Description: "Search by expiry date of an item",
	},
	{
		Level:       "top",
		Key:         "/location",
		RequestKey:  "location_code",
		Description: "Search by location of an item",
	},
	{
		Level:       "top",
		Key:         "/lp",
		RequestKey:  "container_code",
		Description: "Search by container of an item",
	},
	{
		Level:       "top",
		Key:         "/document",
		RequestKey:  "document",
		Description: "Search by document of an item",
	},
	{
		Level:       "top",
		Key:         "/document code",
		RequestKey:  "document_code",
		Description: "Search by document code of an item",
	},
	{
		Level:       "top",
		Key:         "/document PRN",
		RequestKey:  "document_prn",
		Description: "Search by document PRN of an item",
	},
}
